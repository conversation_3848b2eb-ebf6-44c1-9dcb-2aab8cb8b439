{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es.array.filter.js\");\nrequire(\"core-js/modules/es.array.map.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.filter.js\");\nrequire(\"core-js/modules/esnext.iterator.map.js\");\nrequire(\"core-js/modules/esnext.iterator.some.js\");\nvar _litigation = require(\"@/api/litigation/litigation\");\nvar _loanReminderLog = _interopRequireDefault(require(\"@/layout/components/Dialog/loanReminderLog.vue\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default2 = exports.default = {\n  name: 'LitigationLogView',\n  components: {\n    LoanReminderLog: _loanReminderLog.default\n  },\n  props: {\n    data: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  watch: {\n    data: {\n      handler: function handler(newVal) {\n        var _this = this;\n        console.log('newVal:', newVal);\n        (0, _litigation.listLitigation_log)({\n          litigationCaseId: newVal.id\n        }).then(function (res) {\n          _this.logList = res.rows;\n          // 获取最后一个日志的状态，并设置对应的进度条步骤\n          if (res.rows && res.rows.length > 0) {\n            var lastLogStatus = res.rows[res.rows.length - 1].status;\n            _this.currentStatus = lastLogStatus;\n            _this.setActiveStepByStatus(lastLogStatus);\n          }\n        });\n      }\n    }\n  },\n  data: function data() {\n    return {\n      visible: false,\n      // 诉讼状态树结构\n      reminderLogLoanId: '',\n      litigationStatusTree: [\n      // 独立状态（不需要分类）\n      {\n        label: '暂不起诉',\n        value: '暂不起诉'\n      }, {\n        label: '撤案',\n        value: '撤案'\n      }, {\n        label: '立案前',\n        value: '立案前',\n        children: [{\n          label: '准备资料',\n          value: '准备资料'\n        }, {\n          label: '已邮寄',\n          value: '已邮寄'\n        }, {\n          label: '待立案',\n          value: '待立案'\n        }]\n      }, {\n        label: '立案-判决',\n        value: '立案-判决',\n        children: [{\n          label: '获取案件号',\n          value: '获取案件号'\n        }, {\n          label: '待出民初号',\n          value: '待出民初号'\n        }, {\n          label: '待开庭',\n          value: '待开庭'\n        }, {\n          label: '待出法院文书',\n          value: '待出法院文书'\n        }]\n      }, {\n        label: '判决-执行',\n        value: '判决-执行',\n        children: [{\n          label: '待执行',\n          value: '待执行'\n        }, {\n          label: '待出申请书',\n          value: '待出申请书'\n        }, {\n          label: '已提交执行书',\n          value: '已提交执行书'\n        }]\n      }, {\n        label: '执行后',\n        value: '执行后',\n        children: [{\n          label: '执行中',\n          value: '执行中'\n        }, {\n          label: '执行终本',\n          value: '执行终本'\n        }, {\n          label: '继续执行',\n          value: '继续执行'\n        }, {\n          label: '待送车',\n          value: '待送车'\n        }, {\n          label: '待法拍',\n          value: '待法拍'\n        }]\n      }, {\n        label: '结案',\n        value: '结案',\n        children: [{\n          label: '法诉减免结清',\n          value: '法诉减免结清'\n        }, {\n          label: '法诉全额结清',\n          value: '法诉全额结清'\n        }]\n      }],\n      // 当前激活的步骤索引\n      activeStep: 0,\n      logList: [],\n      // 当前状态\n      currentStatus: ''\n    };\n  },\n  computed: {\n    // 从状态树中提取父节点的label作为进度条步骤（排除独立状态）\n    statusSteps: function statusSteps() {\n      return this.litigationStatusTree.filter(function (item) {\n        return item.children && item.children.length > 0;\n      }).map(function (item) {\n        return item.label;\n      });\n    },\n    // 判断是否为撤案状态\n    isWithdrawn: function isWithdrawn() {\n      return this.currentStatus === '撤案';\n    }\n  },\n  methods: {\n    // 根据状态设置激活的步骤\n    setActiveStepByStatus: function setActiveStepByStatus(status) {\n      if (!status) {\n        this.activeStep = 0;\n        return;\n      }\n\n      // 如果是撤案状态，不设置进度条\n      if (status === '撤案') {\n        return;\n      }\n\n      // 查找状态对应的父节点索引\n      var parentIndex = this.findParentIndexByStatus(status);\n      this.activeStep = parentIndex >= 0 ? parentIndex : 0;\n    },\n    // 根据状态找到父节点的索引\n    findParentIndexByStatus: function findParentIndexByStatus(status) {\n      for (var i = 0; i < this.litigationStatusTree.length; i++) {\n        var item = this.litigationStatusTree[i];\n\n        // 如果是父节点本身\n        if (item.label === status || item.value === status) {\n          return i;\n        }\n\n        // 如果有子节点，在子节点中查找\n        if (item.children && item.children.length > 0) {\n          var childFound = item.children.some(function (child) {\n            return child.label === status || child.value === status;\n          });\n          if (childFound) {\n            return i;\n          }\n        }\n      }\n      return -1;\n    },\n    openDialog: function openDialog() {\n      this.visible = true;\n      // 打开对话框时加载数据\n      this.loadLogData();\n    },\n    // 加载日志数据\n    loadLogData: function loadLogData() {\n      var _this2 = this;\n      if (!this.data || !this.data.id) {\n        return;\n      }\n      console.log('加载日志数据:', this.data);\n      (0, _litigation.listLitigation_log)({\n        litigationCaseId: this.data.id\n      }).then(function (res) {\n        _this2.logList = res.rows;\n        // 获取最后一个日志的状态，并设置对应的进度条步骤\n        if (res.rows && res.rows.length > 0) {\n          var lastLogStatus = res.rows[res.rows.length - 1].status;\n          _this2.currentStatus = lastLogStatus;\n          _this2.setActiveStepByStatus(lastLogStatus);\n        }\n      }).catch(function (err) {\n        console.error('加载日志数据失败:', err);\n      });\n    },\n    handleUrgeLog: function handleUrgeLog() {\n      // 打开催记日志对话框，传入当前的 data\n      this.reminderLogLoanId = String(this.data.流程序号);\n      this.$refs.loanReminderLog.openLogDialog();\n    },\n    handleConfirm: function handleConfirm() {\n      return;\n    },\n    handleCancel: function handleCancel() {\n      this.visible = false;\n    }\n  }\n};", "map": {"version": 3, "names": ["_litigation", "require", "_loanReminderLog", "_interopRequireDefault", "name", "components", "LoanReminderLog", "props", "data", "type", "Object", "default", "watch", "handler", "newVal", "_this", "console", "log", "listLitigation_log", "litigationCaseId", "id", "then", "res", "logList", "rows", "length", "lastLogStatus", "status", "currentStatus", "setActiveStepByStatus", "visible", "reminderLogLoanId", "litigationStatusTree", "label", "value", "children", "activeStep", "computed", "statusSteps", "filter", "item", "map", "isWithdrawn", "methods", "parentIndex", "findParentIndexByStatus", "i", "childFound", "some", "child", "openDialog", "loadLogData", "_this2", "catch", "err", "error", "handleUrgeLog", "String", "流程序号", "$refs", "loanReminderLog", "openLogDialog", "handleConfirm", "handleCancel"], "sources": ["src/views/litigation/litigation/modules/litigationLogView.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :visible.sync=\"visible\" title=\"日志查看\" width=\"800px\" @close=\"handleCancel\">\r\n      <!-- 进度条或撤诉状态 -->\r\n      <div v-if=\"isWithdrawn\" style=\"text-align: center; margin-bottom: 24px; padding: 20px; background-color: #f5f5f5; border-radius: 4px;\">\r\n        <span style=\"font-size: 16px; color: #909399; font-weight: bold;\">已撤案</span>\r\n      </div>\r\n      <el-steps v-else :active=\"activeStep\" finish-status=\"success\" process-status=\"process\" align-center style=\"margin-bottom: 24px\" class=\"custom-steps\">\r\n        <el-step v-for=\"(item, idx) in statusSteps\" :key=\"idx\" :title=\"item\" />\r\n      </el-steps>\r\n\r\n      <!-- 日志表格 -->\r\n      <el-table :data=\"logList\" border style=\"width: 100%; margin-bottom: 24px\">\r\n        <el-table-column prop=\"createTime\" label=\"时间\" width=\"160\" />\r\n        <el-table-column prop=\"createBy\" label=\"跟踪人\" width=\"120\" />\r\n        <el-table-column prop=\"status\" label=\"跟踪动作\" width=\"120\" />\r\n        <el-table-column prop=\"remark\" label=\"描述\" />\r\n      </el-table>\r\n\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleUrgeLog\">催记日志</el-button>\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确认</el-button>\r\n        <el-button @click=\"handleCancel\">取消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 催记日志组件 -->\r\n    <loan-reminder-log ref=\"loanReminderLog\" :loan-id=\"reminderLogLoanId\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listLitigation_log } from '@/api/litigation/litigation'\r\nimport LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'\r\n\r\nexport default {\r\n  name: 'LitigationLogView',\r\n  components: {\r\n    LoanReminderLog,\r\n  },\r\n  props: {\r\n    data: {\r\n      type: Object,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        console.log('newVal:', newVal)\r\n        listLitigation_log({ litigationCaseId: newVal.id }).then(res => {\r\n          this.logList = res.rows\r\n          // 获取最后一个日志的状态，并设置对应的进度条步骤\r\n          if (res.rows && res.rows.length > 0) {\r\n            const lastLogStatus = res.rows[res.rows.length - 1].status\r\n            this.currentStatus = lastLogStatus\r\n            this.setActiveStepByStatus(lastLogStatus)\r\n          }\r\n        })\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      // 诉讼状态树结构\r\n      reminderLogLoanId: '',\r\n      litigationStatusTree: [\r\n        // 独立状态（不需要分类）\r\n        { label: '暂不起诉', value: '暂不起诉' },\r\n        { label: '撤案', value: '撤案' },\r\n        {\r\n          label: '立案前',\r\n          value: '立案前',\r\n          children: [\r\n            { label: '准备资料', value: '准备资料' },\r\n            { label: '已邮寄', value: '已邮寄' },\r\n            { label: '待立案', value: '待立案' },\r\n          ],\r\n        },\r\n        {\r\n          label: '立案-判决',\r\n          value: '立案-判决',\r\n          children: [\r\n            { label: '获取案件号', value: '获取案件号' },\r\n            { label: '待出民初号', value: '待出民初号' },\r\n            { label: '待开庭', value: '待开庭' },\r\n            { label: '待出法院文书', value: '待出法院文书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '判决-执行',\r\n          value: '判决-执行',\r\n          children: [\r\n            { label: '待执行', value: '待执行' },\r\n            { label: '待出申请书', value: '待出申请书' },\r\n            { label: '已提交执行书', value: '已提交执行书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '执行后',\r\n          value: '执行后',\r\n          children: [\r\n            { label: '执行中', value: '执行中' },\r\n            { label: '执行终本', value: '执行终本' },\r\n            { label: '继续执行', value: '继续执行' },\r\n            { label: '待送车', value: '待送车' },\r\n            { label: '待法拍', value: '待法拍' },\r\n          ],\r\n        },\r\n        {\r\n          label: '结案',\r\n          value: '结案',\r\n          children: [\r\n            { label: '法诉减免结清', value: '法诉减免结清' },\r\n            { label: '法诉全额结清', value: '法诉全额结清' },\r\n          ],\r\n        },\r\n      ],\r\n      // 当前激活的步骤索引\r\n      activeStep: 0,\r\n      logList: [],\r\n      // 当前状态\r\n      currentStatus: '',\r\n    }\r\n  },\r\n  computed: {\r\n    // 从状态树中提取父节点的label作为进度条步骤（排除独立状态）\r\n    statusSteps() {\r\n      return this.litigationStatusTree\r\n        .filter(item => item.children && item.children.length > 0)\r\n        .map(item => item.label)\r\n    },\r\n    // 判断是否为撤案状态\r\n    isWithdrawn() {\r\n      return this.currentStatus === '撤案'\r\n    },\r\n  },\r\n  methods: {\r\n    // 根据状态设置激活的步骤\r\n    setActiveStepByStatus(status) {\r\n      if (!status) {\r\n        this.activeStep = 0\r\n        return\r\n      }\r\n\r\n      // 如果是撤案状态，不设置进度条\r\n      if (status === '撤案') {\r\n        return\r\n      }\r\n\r\n      // 查找状态对应的父节点索引\r\n      const parentIndex = this.findParentIndexByStatus(status)\r\n      this.activeStep = parentIndex >= 0 ? parentIndex : 0\r\n    },\r\n\r\n    // 根据状态找到父节点的索引\r\n    findParentIndexByStatus(status) {\r\n      for (let i = 0; i < this.litigationStatusTree.length; i++) {\r\n        const item = this.litigationStatusTree[i]\r\n\r\n        // 如果是父节点本身\r\n        if (item.label === status || item.value === status) {\r\n          return i\r\n        }\r\n\r\n        // 如果有子节点，在子节点中查找\r\n        if (item.children && item.children.length > 0) {\r\n          const childFound = item.children.some(child => child.label === status || child.value === status)\r\n          if (childFound) {\r\n            return i\r\n          }\r\n        }\r\n      }\r\n      return -1\r\n    },\r\n\r\n    openDialog() {\r\n      this.visible = true\r\n      // 打开对话框时加载数据\r\n      this.loadLogData()\r\n    },\r\n\r\n    // 加载日志数据\r\n    loadLogData() {\r\n      if (!this.data || !this.data.id) {\r\n        return\r\n      }\r\n\r\n      console.log('加载日志数据:', this.data)\r\n      listLitigation_log({ litigationCaseId: this.data.id }).then(res => {\r\n        this.logList = res.rows\r\n        // 获取最后一个日志的状态，并设置对应的进度条步骤\r\n        if (res.rows && res.rows.length > 0) {\r\n          const lastLogStatus = res.rows[res.rows.length - 1].status\r\n          this.currentStatus = lastLogStatus\r\n          this.setActiveStepByStatus(lastLogStatus)\r\n        }\r\n      }).catch(err => {\r\n        console.error('加载日志数据失败:', err)\r\n      })\r\n    },\r\n    handleUrgeLog() {\r\n      // 打开催记日志对话框，传入当前的 data\r\n      this.reminderLogLoanId = String(this.data.流程序号)\r\n      this.$refs.loanReminderLog.openLogDialog()\r\n    },\r\n    handleConfirm() {\r\n      return\r\n    },\r\n    handleCancel() {\r\n      this.visible = false\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 自定义步骤条样式 - 激活节点为蓝色 */\r\n::v-deep .custom-steps .el-step__head.is-process {\r\n  color: #409eff;\r\n  border-color: #409eff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__head.is-process .el-step__icon {\r\n  background-color: #409eff;\r\n  border-color: #409eff;\r\n  color: #fff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__title.is-process {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AAgCA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,eAAA,EAAAA;EACA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,KAAA;IACAJ,IAAA;MACAK,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAC,KAAA;QACAC,OAAA,CAAAC,GAAA,YAAAH,MAAA;QACA,IAAAI,8BAAA;UAAAC,gBAAA,EAAAL,MAAA,CAAAM;QAAA,GAAAC,IAAA,WAAAC,GAAA;UACAP,KAAA,CAAAQ,OAAA,GAAAD,GAAA,CAAAE,IAAA;UACA;UACA,IAAAF,GAAA,CAAAE,IAAA,IAAAF,GAAA,CAAAE,IAAA,CAAAC,MAAA;YACA,IAAAC,aAAA,GAAAJ,GAAA,CAAAE,IAAA,CAAAF,GAAA,CAAAE,IAAA,CAAAC,MAAA,MAAAE,MAAA;YACAZ,KAAA,CAAAa,aAAA,GAAAF,aAAA;YACAX,KAAA,CAAAc,qBAAA,CAAAH,aAAA;UACA;QACA;MACA;IACA;EACA;EACAlB,IAAA,WAAAA,KAAA;IACA;MACAsB,OAAA;MACA;MACAC,iBAAA;MACAC,oBAAA;MACA;MACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,EACA;MACA;MACAE,UAAA;MACAb,OAAA;MACA;MACAK,aAAA;IACA;EACA;EACAS,QAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAN,oBAAA,CACAO,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAL,QAAA,IAAAK,IAAA,CAAAL,QAAA,CAAAV,MAAA;MAAA,GACAgB,GAAA,WAAAD,IAAA;QAAA,OAAAA,IAAA,CAAAP,KAAA;MAAA;IACA;IACA;IACAS,WAAA,WAAAA,YAAA;MACA,YAAAd,aAAA;IACA;EACA;EACAe,OAAA;IACA;IACAd,qBAAA,WAAAA,sBAAAF,MAAA;MACA,KAAAA,MAAA;QACA,KAAAS,UAAA;QACA;MACA;;MAEA;MACA,IAAAT,MAAA;QACA;MACA;;MAEA;MACA,IAAAiB,WAAA,QAAAC,uBAAA,CAAAlB,MAAA;MACA,KAAAS,UAAA,GAAAQ,WAAA,QAAAA,WAAA;IACA;IAEA;IACAC,uBAAA,WAAAA,wBAAAlB,MAAA;MACA,SAAAmB,CAAA,MAAAA,CAAA,QAAAd,oBAAA,CAAAP,MAAA,EAAAqB,CAAA;QACA,IAAAN,IAAA,QAAAR,oBAAA,CAAAc,CAAA;;QAEA;QACA,IAAAN,IAAA,CAAAP,KAAA,KAAAN,MAAA,IAAAa,IAAA,CAAAN,KAAA,KAAAP,MAAA;UACA,OAAAmB,CAAA;QACA;;QAEA;QACA,IAAAN,IAAA,CAAAL,QAAA,IAAAK,IAAA,CAAAL,QAAA,CAAAV,MAAA;UACA,IAAAsB,UAAA,GAAAP,IAAA,CAAAL,QAAA,CAAAa,IAAA,WAAAC,KAAA;YAAA,OAAAA,KAAA,CAAAhB,KAAA,KAAAN,MAAA,IAAAsB,KAAA,CAAAf,KAAA,KAAAP,MAAA;UAAA;UACA,IAAAoB,UAAA;YACA,OAAAD,CAAA;UACA;QACA;MACA;MACA;IACA;IAEAI,UAAA,WAAAA,WAAA;MACA,KAAApB,OAAA;MACA;MACA,KAAAqB,WAAA;IACA;IAEA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,UAAA5C,IAAA,UAAAA,IAAA,CAAAY,EAAA;QACA;MACA;MAEAJ,OAAA,CAAAC,GAAA,iBAAAT,IAAA;MACA,IAAAU,8BAAA;QAAAC,gBAAA,OAAAX,IAAA,CAAAY;MAAA,GAAAC,IAAA,WAAAC,GAAA;QACA8B,MAAA,CAAA7B,OAAA,GAAAD,GAAA,CAAAE,IAAA;QACA;QACA,IAAAF,GAAA,CAAAE,IAAA,IAAAF,GAAA,CAAAE,IAAA,CAAAC,MAAA;UACA,IAAAC,aAAA,GAAAJ,GAAA,CAAAE,IAAA,CAAAF,GAAA,CAAAE,IAAA,CAAAC,MAAA,MAAAE,MAAA;UACAyB,MAAA,CAAAxB,aAAA,GAAAF,aAAA;UACA0B,MAAA,CAAAvB,qBAAA,CAAAH,aAAA;QACA;MACA,GAAA2B,KAAA,WAAAC,GAAA;QACAtC,OAAA,CAAAuC,KAAA,cAAAD,GAAA;MACA;IACA;IACAE,aAAA,WAAAA,cAAA;MACA;MACA,KAAAzB,iBAAA,GAAA0B,MAAA,MAAAjD,IAAA,CAAAkD,IAAA;MACA,KAAAC,KAAA,CAAAC,eAAA,CAAAC,aAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAjC,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}