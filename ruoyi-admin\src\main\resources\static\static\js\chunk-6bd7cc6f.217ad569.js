(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6bd7cc6f"],{"68c1":function(e,a,t){"use strict";t.r(a);var n=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"app-container"},[t("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[t("el-form-item",{attrs:{label:"",prop:"expenseAmount"}},[t("el-input",{attrs:{placeholder:"请输入费用金额",clearable:""},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleQuery(a)}},model:{value:e.queryParams.expenseAmount,callback:function(a){e.$set(e.queryParams,"expenseAmount",a)},expression:"queryParams.expenseAmount"}})],1),t("el-form-item",{attrs:{label:"",prop:"expenseDate"}},[t("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择费用发生日期"},model:{value:e.queryParams.expenseDate,callback:function(a){e.$set(e.queryParams,"expenseDate",a)},expression:"queryParams.expenseDate"}})],1),t("el-form-item",{attrs:{label:"",prop:"applicantId"}},[t("el-input",{attrs:{placeholder:"请输入申请人ID",clearable:""},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleQuery(a)}},model:{value:e.queryParams.applicantId,callback:function(a){e.$set(e.queryParams,"applicantId",a)},expression:"queryParams.applicantId"}})],1),t("el-form-item",{attrs:{label:"",prop:"applicantName"}},[t("el-input",{attrs:{placeholder:"请输入申请人姓名",clearable:""},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleQuery(a)}},model:{value:e.queryParams.applicantName,callback:function(a){e.$set(e.queryParams,"applicantName",a)},expression:"queryParams.applicantName"}})],1),t("el-form-item",{attrs:{label:"",prop:"applicationTime"}},[t("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择申请时间"},model:{value:e.queryParams.applicationTime,callback:function(a){e.$set(e.queryParams,"applicationTime",a)},expression:"queryParams.applicationTime"}})],1),t("el-form-item",{attrs:{label:"",prop:"approverId"}},[t("el-input",{attrs:{placeholder:"请输入审批人ID",clearable:""},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleQuery(a)}},model:{value:e.queryParams.approverId,callback:function(a){e.$set(e.queryParams,"approverId",a)},expression:"queryParams.approverId"}})],1),t("el-form-item",{attrs:{label:"",prop:"approverName"}},[t("el-input",{attrs:{placeholder:"请输入审批人姓名",clearable:""},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleQuery(a)}},model:{value:e.queryParams.approverName,callback:function(a){e.$set(e.queryParams,"approverName",a)},expression:"queryParams.approverName"}})],1),t("el-form-item",{attrs:{label:"",prop:"approvalTimeRange"}},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",clearable:""},on:{change:e.handleApprovalTimeRangeChange},model:{value:e.approvalTimeRange,callback:function(a){e.approvalTimeRange=a},expression:"approvalTimeRange"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),t("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")]),t("el-button",{attrs:{type:e.showPendingOnly?"success":"info",icon:"el-icon-s-check",size:"mini"},on:{click:e.togglePendingView}},[e._v(" "+e._s(e.showPendingOnly?"显示全部":"待我审批")+" ")])],1)],1),t("el-row",{staticClass:"mb8",attrs:{gutter:10}},[t("el-col",{attrs:{span:1.5}},[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["daily_expense_approval:daily_expense_approval:export"],expression:"['daily_expense_approval:daily_expense_approval:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.daily_expense_approvalList}},[t("el-table-column",{attrs:{label:"费用类型",align:"center",prop:"expenseType"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(" "+e._s(e.getExpenseTypeLabel(a.row.expenseType))+" ")]}}])}),t("el-table-column",{attrs:{label:"费用金额",align:"center",prop:"expenseAmount"}}),t("el-table-column",{attrs:{label:"费用发生日期",align:"center",prop:"expenseDate",width:"180"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e.parseTime(a.row.expenseDate,"{y}-{m}-{d}")))])]}}])}),t("el-table-column",{attrs:{label:"费用说明",align:"center",prop:"expenseDescription"}}),t("el-table-column",{attrs:{label:"申请人姓名",align:"center",prop:"applicantName"}}),t("el-table-column",{attrs:{label:"申请时间",align:"center",prop:"applicationTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e.parseTime(a.row.applicationTime,"{y}-{m}-{d}")))])]}}])}),t("el-table-column",{attrs:{label:"审批状态",align:"center",prop:"approvalStatus"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{type:e.getStatusTagType(a.row.approvalStatus)}},[e._v(" "+e._s(e.getStatusText(a.row.approvalStatus))+" ")])]}}])}),t("el-table-column",{attrs:{label:"审批人",align:"center",prop:"approverName"}}),t("el-table-column",{attrs:{label:"审批时间",align:"center",prop:"approvalTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e.parseTime(a.row.approvalTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),t("el-table-column",{attrs:{label:"审批备注",align:"center",prop:"approvalRemark"}}),t("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(a){return[e.canApprove(a.row)?t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["daily_expense_approval:daily_expense_approval:edit"],expression:"['daily_expense_approval:daily_expense_approval:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-check"},on:{click:function(t){return e.handleApprove(a.row,"approve")}}},[e._v("通过")]):e._e(),e.canApprove(a.row)?t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["daily_expense_approval:daily_expense_approval:edit"],expression:"['daily_expense_approval:daily_expense_approval:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-close"},on:{click:function(t){return e.handleApprove(a.row,"reject")}}},[e._v("拒绝")]):e._e(),t("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(t){return e.handleView(a.row)}}},[e._v("查看")])]}}])})],1),t("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(a){return e.$set(e.queryParams,"pageNum",a)},"update:limit":function(a){return e.$set(e.queryParams,"pageSize",a)},pagination:e.getList}}),t("el-dialog",{attrs:{title:"日常费用详情",visible:e.viewDialogVisible,width:"600px","append-to-body":""},on:{"update:visible":function(a){e.viewDialogVisible=a}}},[t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"费用类型"}},[e._v(" "+e._s(e.getExpenseTypeLabel(e.viewData.expenseType))+" ")]),t("el-descriptions-item",{attrs:{label:"费用金额"}},[e._v(" ￥"+e._s(e.viewData.expenseAmount)+" ")]),t("el-descriptions-item",{attrs:{label:"费用发生日期"}},[e._v(" "+e._s(e.parseTime(e.viewData.expenseDate,"{y}-{m}-{d}"))+" ")]),t("el-descriptions-item",{attrs:{label:"申请人"}},[e._v(" "+e._s(e.viewData.applicantName)+" ")]),t("el-descriptions-item",{attrs:{label:"申请时间"}},[e._v(" "+e._s(e.parseTime(e.viewData.applicationTime,"{y}-{m}-{d}"))+" ")]),t("el-descriptions-item",{attrs:{label:"审批状态"}},[t("el-tag",{attrs:{type:e.getStatusTagType(e.viewData.approvalStatus)}},[e._v(" "+e._s(e.getStatusText(e.viewData.approvalStatus))+" ")])],1),"0"!==e.viewData.approvalStatus?t("el-descriptions-item",{attrs:{label:"审批人"}},[e._v(" "+e._s(e.viewData.approverName||"-")+" ")]):e._e(),"0"!==e.viewData.approvalStatus?t("el-descriptions-item",{attrs:{label:"审批时间"}},[e._v(" "+e._s(e.viewData.approvalTime?e.parseTime(e.viewData.approvalTime,"{y}-{m}-{d} {h}:{i}:{s}"):"-")+" ")]):e._e(),t("el-descriptions-item",{attrs:{label:"费用说明",span:2}},[e._v(" "+e._s(e.viewData.expenseDescription||"-")+" ")]),"0"!==e.viewData.approvalStatus?t("el-descriptions-item",{attrs:{label:"审批备注",span:2}},[e._v(" "+e._s(e.viewData.approvalRemark||"-")+" ")]):e._e()],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(a){e.viewDialogVisible=!1}}},[e._v("关 闭")])],1)],1)],1)},l=[],r=t("5530"),i=t("a6de"),p={name:"Daily_expense_approval",data:function(){return{loading:!0,showSearch:!0,total:0,daily_expense_approvalList:[],viewDialogVisible:!1,viewData:{},approvalTimeRange:[],showPendingOnly:!1,queryParams:{pageNum:1,pageSize:10,litigationCaseId:null,expenseType:null,expenseAmount:null,expenseDate:null,expenseDescription:null,receiptUrl:null,applicantId:null,applicantName:null,applicationTime:null,approvalStatus:null,approverId:null,approverName:null,approvalStartTime:null,approvalEndTime:null,approvalRemark:null}}},created:function(){this.getList(),this.$route.query.litigationCaseId&&(this.queryParams.litigationCaseId=this.$route.query.litigationCaseId,this.handleQuery())},methods:{getList:function(){var e=this;this.loading=!0;var a=this.showPendingOnly?i["d"]:i["c"];a(this.queryParams).then((function(a){e.daily_expense_approvalList=a.rows,e.total=a.total,e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.approvalTimeRange=[],this.resetForm("queryForm"),this.handleQuery()},handleApprovalTimeRangeChange:function(e){e&&2===e.length?(this.queryParams.approvalStartTime=e[0],this.queryParams.approvalEndTime=e[1]):(this.queryParams.approvalStartTime=null,this.queryParams.approvalEndTime=null),this.handleQuery()},handleExport:function(){this.download("daily_expense_approval/daily_expense_approval/export",Object(r["a"])({},this.queryParams),"daily_expense_approval_".concat((new Date).getTime(),".xlsx"))},reset:function(){this.form={id:null,litigationCaseId:null,expenseType:null,expenseAmount:null,expenseDate:null,expenseDescription:null,receiptUrl:null,applicantId:null,applicantName:null,applicationTime:null,approvalStatus:"0",approverId:null,approverName:null,approvalTime:null,approvalRemark:null,delFlag:"0"},this.resetForm("form")},handleApprove:function(e,a){var t=this,n="approve"===a?"通过":"拒绝";"approve"===a?this.$confirm("确认".concat(n,"该日常费用申请？"),"".concat(n,"审批"),{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var l={action:a,remark:"审批通过"};Object(i["b"])(e.id,l).then((function(e){t.$modal.msgSuccess("".concat(n,"成功")),t.getList()})).catch((function(e){t.$modal.msgError("".concat(n,"失败：")+(e.msg||"未知错误"))}))})).catch((function(){t.$modal.msgInfo("已取消审批")})):this.$prompt("请输入".concat(n,"理由"),"".concat(n,"审批"),{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/.+/,inputErrorMessage:"请输入审批理由"}).then((function(l){var r=l.value,p={action:a,remark:r};Object(i["b"])(e.id,p).then((function(e){t.$modal.msgSuccess("".concat(n,"成功")),t.getList()})).catch((function(e){t.$modal.msgError("".concat(n,"失败：")+(e.msg||"未知错误"))}))})).catch((function(){t.$modal.msgInfo("已取消审批")}))},handleView:function(e){this.viewData=Object(r["a"])({},e),this.viewDialogVisible=!0},getExpenseTypeLabel:function(e){var a={oil_fee:"油费",road_fee:"路费",meal_fee:"餐费",accommodation_fee:"住宿费",transport_fee:"交通费",parking_fee:"停车费",communication_fee:"通讯费",other:"其他"};return a[e]||e},getStatusText:function(e){var a={0:"待审批",1:"全部通过",2:"已拒绝",3:"主管审批中",4:"总监审批中",5:"财务主管审批中",6:"总经理审批中"};return a[e]||"未知状态"},getStatusTagType:function(e){var a={0:"warning",1:"success",2:"danger",3:"primary",4:"primary",5:"primary",6:"primary"};return a[e]||"info"},canApprove:function(e){return"1"!==e.approvalStatus&&"2"!==e.approvalStatus},togglePendingView:function(){this.showPendingOnly=!this.showPendingOnly,this.handleQuery()}}},o=p,s=t("2877"),u=Object(s["a"])(o,n,l,!1,null,null,null);a["default"]=u.exports},a6de:function(e,a,t){"use strict";t.d(a,"c",(function(){return l})),t.d(a,"d",(function(){return r})),t.d(a,"a",(function(){return i})),t.d(a,"b",(function(){return p}));var n=t("b775");function l(e){return Object(n["a"])({url:"/daily_expense_approval/daily_expense_approval/list",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/daily_expense_approval/daily_expense_approval/pendingList",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/daily_expense_approval/daily_expense_approval",method:"post",data:e})}function p(e,a){return Object(n["a"])({url:"/daily_expense_approval/daily_expense_approval/approve/"+e,method:"post",data:a})}}}]);