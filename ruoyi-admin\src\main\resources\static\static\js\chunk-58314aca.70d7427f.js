(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-58314aca"],{"0f5f":function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.carInfo.plateNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车架号"}},[e._v(e._s(e.carInfo.identityNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"发动机号"}},[e._v(e._s(e.carInfo.engineNumber||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(e._s(e.carStatusLabel))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆位置"}},[e._v(e._s(e.carInfo.carAddress||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(e._s(e.gpsStatusLabel))]):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看车牌照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"行驶证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看行驶证照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"登记证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看登记证照片")])],1):e._e()],1)],1)},n=[],o=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),a("bd52")),l={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var e=this,t=this.carStatusList.find((function(t){return t.value===e.carInfo.carStatus}));return t?t.label:"未知状态"},gpsStatusLabel:function(){var e=this,t=this.gpsStatusList.find((function(t){return t.value===e.carInfo.gpsStatus}));return t?t.label:"未知状态"}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,e&&this.plateNo&&Object(o["e"])(this.plateNo).then((function(e){t.carInfo=e.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},s=l,i=a("2877"),u=Object(i["a"])(s,r,n,!1,null,null,null);t["a"]=u.exports},1791:function(e,t,a){},"2eca":function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[e._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},n=[],o=a("bd52"),l={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(o["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},s=l,i=(a("d6fd"),a("2877")),u=Object(i["a"])(s,r,n,!1,null,"8a3d4978",null);t["a"]=u.exports},5029:function(e,t,a){"use strict";a.d(t,"d",(function(){return n})),a.d(t,"g",(function(){return o})),a.d(t,"c",(function(){return l})),a.d(t,"b",(function(){return s})),a.d(t,"a",(function(){return i})),a.d(t,"f",(function(){return u})),a.d(t,"e",(function(){return c}));var r=a("b775");function n(e){return Object(r["a"])({url:"/vm_car_order/vm_car_order/list",method:"get",params:e})}function o(){return Object(r["a"])({url:"/vm_car_order/vm_car_order/cate",method:"get"})}function l(e){return Object(r["a"])({url:"/vm_car_order/vm_car_order/"+e,method:"get"})}function s(e){return Object(r["a"])({url:"/vm_car_order/vm_car_order/"+e,method:"delete"})}function i(e){return Object(r["a"])({url:"/car_order/car_order",method:"put",data:e})}function u(e){return Object(r["a"])({url:"/vm_car_order/vm_car_order/submitCost",method:"post",data:e})}function c(e){return Object(r["a"])({url:"/vm_car_order/vm_car_order/mailKey",method:"post",data:e})}},"84bd":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"请输入车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"jgName"}},[a("el-input",{attrs:{placeholder:"录单渠道名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jgName,callback:function(t){e.$set(e.queryParams,"jgName",t)},expression:"queryParams.jgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"garageName"}},[a("el-input",{attrs:{placeholder:"车库名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.garageName,callback:function(t){e.$set(e.queryParams,"garageName",t)},expression:"queryParams.garageName"}})],1),a("el-form-item",{attrs:{label:"",prop:"keyStatus"}},[a("el-select",{attrs:{placeholder:"请选择钥匙状态",clearable:""},model:{value:e.queryParams.keyStatus,callback:function(t){e.$set(e.queryParams,"keyStatus",t)},expression:"queryParams.keyStatus"}},e._l(e.keyStatusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择订单状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.statusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"teamName"}},[a("el-select",{attrs:{placeholder:"请选择找车团队",clearable:""},model:{value:e.queryParams.teamName,callback:function(t){e.$set(e.queryParams,"teamName",t)},expression:"queryParams.teamName"}},e._l(e.teamList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"派单时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.queryParams.originallyTime,callback:function(t){e.$set(e.queryParams,"originallyTime",t)},expression:"queryParams.originallyTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vm_car_orderList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"customerName"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openUserInfo({customerId:t.row.customerId,applyId:t.row.applyNo})}}},[e._v(" "+e._s(t.row.customerName)+" ")])]}}])}),a("el-table-column",{attrs:{label:"联系电话",align:"center",prop:"mobilePhone",width:"130"}}),a("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"jgName",width:"130"}}),a("el-table-column",{attrs:{label:"逾期状态",align:"center",prop:"slippageStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.slippageStatus?a("span",[e._v(" "+e._s(1==t.row.slippageStatus?"提醒":2==t.row.slippageStatus?"电催":3==t.row.slippageStatus?"上访":4==t.row.slippageStatus?"逾期30-60":"逾期60+")+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"欠款金额",align:"center",prop:"overdueAmt",width:"130"}}),a("el-table-column",{attrs:{label:"派单员",align:"center",prop:"dispatcher",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(" "+e._s(1===t.row.dispatcher?"贷后文员":2===t.row.dispatcher?"法诉文员":"")+" ")])]}}])}),a("el-table-column",{attrs:{label:"业务员",align:"center",prop:"nickName"}}),a("el-table-column",{attrs:{label:"车牌号",align:"center",prop:"plateNo"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openCarInfo(t.row.plateNo)}}},[e._v(e._s(t.row.plateNo))])]}}])}),a("el-table-column",{attrs:{label:"车辆位置",align:"center",prop:"carDetailAddress"}}),a("el-table-column",{attrs:{label:"车辆状态",align:"center",prop:"carStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(" "+e._s(1==t.row.carStatus?"省内正常行驶":2==t.row.carStatus?"省外正常行驶":3==t.row.carStatus?"抵押":4==t.row.carStatus?"疑似抵押":5==t.row.carStatus?"疑似黑车":6==t.row.carStatus?"已入库":7==t.row.carStatus?"车在法院":8==t.row.carStatus?"已法拍":"协商卖车")+" ")])]}}])}),a("el-table-column",{attrs:{label:"GPS状态",align:"center",prop:"gpsStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(" "+e._s(1==t.row.gpsStatus?"部分拆除":2==t.row.gpsStatus?"全部拆除":3==t.row.gpsStatus?"GPS正常":"停车30天以上")+" ")])]}}])}),a("el-table-column",{attrs:{label:"车库名称",align:"center",prop:"garageName",width:"130"}}),a("el-table-column",{attrs:{label:"找车团队",align:"center",prop:"teamName"}}),a("el-table-column",{attrs:{label:"派单时间",align:"center",prop:"allocationTime",width:"180"}}),a("el-table-column",{attrs:{label:"钥匙状态",align:"center",prop:"keyStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(1==t.row.keyStatus?"已邮寄":2==t.row.keyStatus?"已收回":"未归还"))])]}}])}),a("el-table-column",{attrs:{label:"订单状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.statusList[t.row.status].label||"暂无数据"))])]}}])}),a("el-table-column",{attrs:{label:"钥匙邮寄时间",align:"center",prop:"keyTime",width:"180"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vm_car_order:vm_car_order:edit"],expression:"['vm_car_order:vm_car_order:edit']"}],staticStyle:{"margin-left":"10px"},attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v(" 邮寄钥匙 ")]),4!==t.row.status?a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleRevoke(t.row)}}},[e._v("撤销订单")]):e._e(),2===t.row.status?a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleSubmitCost(t.row)}}},[e._v("提交找车费用")]):e._e()]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"邮寄地址"}},[a("el-select",{attrs:{"value-key":"children",placeholder:"请选择省"},on:{change:e.provinceChange},model:{value:e.form.keyProvince,callback:function(t){e.$set(e.form,"keyProvince",t)},expression:"form.keyProvince"}},e._l(e.provinceList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e}})})),1),a("el-select",{staticStyle:{"margin-top":"10px"},attrs:{"value-key":"children",placeholder:"请选择市"},on:{change:e.cityChange},model:{value:e.form.keyCity,callback:function(t){e.$set(e.form,"keyCity",t)},expression:"form.keyCity"}},e._l(e.cityList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e}})})),1),a("el-select",{staticStyle:{"margin-top":"10px"},attrs:{"value-key":"children",placeholder:"请选择区"},on:{change:e.districtChange},model:{value:e.form.keyBorough,callback:function(t){e.$set(e.form,"keyBorough",t)},expression:"form.keyBorough"}},e._l(e.districtList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e}})})),1)],1),a("el-form-item",{attrs:{label:"详细地址"}},[a("el-input",{attrs:{placeholder:"请填写详细地址",clearable:""},model:{value:e.form.keyAddress,callback:function(t){e.$set(e.form,"keyAddress",t)},expression:"form.keyAddress"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"提交找车费用",visible:e.costDialogVisible,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.costDialogVisible=t}}},[a("el-form",{ref:"costForm",attrs:{model:e.costForm,rules:e.costRules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"佣金",prop:"transportationFee"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{precision:2,min:0,max:999999,placeholder:"请输入佣金"},model:{value:e.costForm.transportationFee,callback:function(t){e.$set(e.costForm,"transportationFee",t)},expression:"costForm.transportationFee"}})],1),a("el-form-item",{attrs:{label:"拖车费",prop:"towingFee"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{precision:2,min:0,max:999999,placeholder:"请输入拖车费"},model:{value:e.costForm.towingFee,callback:function(t){e.$set(e.costForm,"towingFee",t)},expression:"costForm.towingFee"}})],1),a("el-form-item",{attrs:{label:"贴机费",prop:"trackerInstallationFee"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{precision:2,min:0,max:999999,placeholder:"请输入贴机费"},model:{value:e.costForm.trackerInstallationFee,callback:function(t){e.$set(e.costForm,"trackerInstallationFee",t)},expression:"costForm.trackerInstallationFee"}})],1),a("el-form-item",{attrs:{label:"其他报销",prop:"otherReimbursement"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{precision:2,min:0,max:999999,placeholder:"请输入其他报销费用"},model:{value:e.costForm.otherReimbursement,callback:function(t){e.$set(e.costForm,"otherReimbursement",t)},expression:"costForm.otherReimbursement"}})],1),a("el-form-item",{attrs:{label:"总费用"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{value:e.totalCost,readonly:""}},[a("template",{slot:"append"},[e._v("元")])],2)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitCostForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancelCost}},[e._v("取 消")])],1)],1),a("userInfo",{ref:"userInfo",attrs:{visible:e.userInfoVisible,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.userInfoVisible=t}}}),a("carInfo",{ref:"carInfo",attrs:{visible:e.carInfoVisible,title:"车辆信息",plateNo:e.plateNo,permission:"2"},on:{"update:visible":function(t){e.carInfoVisible=t}}})],1)},n=[],o=a("5530"),l=a("c14f"),s=a("1da1"),i=(a("d81d"),a("b0c0"),a("b680"),a("d3b7"),a("0643"),a("a573"),a("5029")),u=a("a1e7"),c=a("cf0d"),m=a("2eca"),d=a("0f5f"),p={name:"Vm_car_order",components:{userInfo:m["a"],carInfo:d["a"]},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,vm_car_orderList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:15,customerName:"",plateNo:"",jgName:"",garageName:"",keyStatus:"",status:"",teamName:"",startTime:"",endTime:"",originallyTime:""},jgNameList:[{label:"A公司",value:1},{label:"B公司",value:2}],keyStatusList:[{label:"已邮寄",value:1},{label:"已收回",value:2},{label:"已归还",value:3}],statusList:[{label:"发起订单",value:0},{label:"已分配",value:1},{label:"已完成",value:2},{label:"未完成",value:3},{label:"已撤销",value:4}],teamList:[],form:{id:"",keyProvince:"",keyCity:"",keyBorough:"",keyAddress:""},rules:{keyProvince:"",keyCity:"",keyBorough:"",keyAddress:""},provinceList:c,cityList:[],districtList:[],revokeList:{id:"",status:4},customerInfo:{customerId:"",applyId:""},userInfoVisible:!1,plateNo:"",carInfoVisible:!1,costDialogVisible:!1,costForm:{id:"",applyNo:"",loanId:"",transportationFee:0,towingFee:0,trackerInstallationFee:0,otherReimbursement:0},costRules:{transportationFee:[{type:"number",min:0,message:"佣金不能小于0",trigger:"blur"}],towingFee:[{type:"number",min:0,message:"拖车费不能小于0",trigger:"blur"}],trackerInstallationFee:[{type:"number",min:0,message:"贴机费不能小于0",trigger:"blur"}],otherReimbursement:[{type:"number",min:0,message:"其他报销不能小于0",trigger:"blur"}]}}},computed:{totalCost:function(){var e=this.costForm.transportationFee||0,t=this.costForm.towingFee||0,a=this.costForm.trackerInstallationFee||0,r=this.costForm.otherReimbursement||0;return(e+t+a+r).toFixed(2)}},created:function(){this.getTeamList(),this.getList()},methods:{handleChange:function(e){this.queryParams.jgName=e},provinceChange:function(e){this.form.keyProvince=e.name,this.cityList=e.children},cityChange:function(e){this.form.keyCity=e.name,this.districtList=e.children},districtChange:function(e){this.form.keyBorough=e.name},getTeamList:function(){var e=this;return Object(s["a"])(Object(l["a"])().m((function t(){var a;return Object(l["a"])().w((function(t){while(1)switch(t.p=t.n){case 0:return t.p=0,t.n=1,Object(u["d"])();case 1:a=t.v,e.teamList=(a.rows||[]).map((function(e){return{label:e.teamName,value:e.teamName}})),t.n=3;break;case 2:t.p=2,t.v,e.teamList=[];case 3:return t.a(2)}}),t,null,[[0,2]])})))()},getList:function(){var e=this;this.loading=!0,Object(i["d"])(this.queryParams).then((function(t){e.vm_car_orderList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:"",keyProvince:"",keyCity:"",keyBorough:"",keyAddress:""},this.resetForm("form")},handleQuery:function(){this.queryParams.originallyTime&&(this.queryParams.startTime=this.queryParams.originallyTime[0],this.queryParams.endTime=this.queryParams.originallyTime[1]),this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.customerName=null,this.queryParams.plateNo=null,this.queryParams.jgName=null,this.queryParams.garageName=null,this.queryParams.keyStatus=null,this.queryParams.status=null,this.queryParams.collectionMethod=null,this.queryParams.teamName=null,this.queryParams.originallyTime=null,this.queryParams.startTime=null,this.queryParams.endTime=null,this.handleQuery()},restSearch:function(){this.queryParams={customerName:"",plateNo:"",jgName:"",garageId:"",keyStatus:"",status:"",teamName:"",startTime:"",endTime:"",originallyTime:"",pageNum:1}},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加VIEW"},handleUpdate:function(e){var t=this;this.reset(),this.form.id=e.id,this.form.keyProvince=e.keyProvince,this.form.keyCity=e.keyCity,this.form.keyBorough=e.keyBorough,this.form.keyAddress=e.keyAddress;var a=e.id||this.ids;Object(i["c"])(a).then((function(e){t.open=!0,t.title="邮寄钥匙"}))},submitForm:function(){var e=this;Object(i["e"])(this.form).then((function(){e.$modal.msgSuccess("钥匙邮寄成功"),e.open=!1,e.getList()})).catch((function(t){e.$modal.msgError("邮寄失败："+(t.message||"未知错误"))}))},handleRevoke:function(e){var t=this;console.log("1111"),this.revokeList.id=e.id;var a={id:e.id,status:4};this.$modal.confirm("是否确认撤销？").then((function(){return Object(i["a"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("撤销成功")})).catch((function(e){console.log(e)}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认撤销编号为"'+a+'"的数据项？').then((function(){return Object(i["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("撤销成功")})).catch((function(){}))},handleExport:function(){this.download("vm_car_order/vm_car_order/export",Object(o["a"])({},this.queryParams),"vm_car_order_".concat((new Date).getTime(),".xlsx"))},openUserInfo:function(e){this.customerInfo=e,this.userInfoVisible=!0},openCarInfo:function(e){this.plateNo=e,this.carInfoVisible=!0},handleSubmitCost:function(e){this.resetCostForm(),this.costForm.id=e.id,this.costForm.applyNo=e.applyNo,this.costForm.loanId=e.loanId,this.costDialogVisible=!0},resetCostForm:function(){this.costForm={id:"",applyNo:"",loanId:"",transportationFee:0,towingFee:0,trackerInstallationFee:0,otherReimbursement:0},this.$refs.costForm&&this.$refs.costForm.resetFields()},cancelCost:function(){this.costDialogVisible=!1,this.resetCostForm()},submitCostForm:function(){var e=this;this.$refs.costForm.validate((function(t){t&&Object(i["f"])(e.costForm).then((function(){e.$modal.msgSuccess("找车费用提交成功"),e.costDialogVisible=!1,e.resetCostForm(),e.getList()}))}))}}},f=p,b=a("2877"),h=Object(b["a"])(f,r,n,!1,null,null,null);t["default"]=h.exports},a1e7:function(e,t,a){"use strict";a.d(t,"d",(function(){return n})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"e",(function(){return s})),a.d(t,"b",(function(){return i}));var r=a("b775");function n(e){return Object(r["a"])({url:"/car_team/car_team/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/car_team/car_team/"+e,method:"get"})}function l(e){return Object(r["a"])({url:"/car_team/car_team",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/car_team/car_team",method:"put",data:e})}function i(e){return Object(r["a"])({url:"/car_team/car_team/"+e,method:"delete"})}},bd52:function(e,t,a){"use strict";a.d(t,"l",(function(){return n})),a.d(t,"n",(function(){return o})),a.d(t,"h",(function(){return l})),a.d(t,"i",(function(){return s})),a.d(t,"o",(function(){return i})),a.d(t,"e",(function(){return u})),a.d(t,"s",(function(){return c})),a.d(t,"t",(function(){return m})),a.d(t,"a",(function(){return d})),a.d(t,"A",(function(){return p})),a.d(t,"g",(function(){return f})),a.d(t,"k",(function(){return b})),a.d(t,"w",(function(){return h})),a.d(t,"z",(function(){return y})),a.d(t,"f",(function(){return _})),a.d(t,"x",(function(){return v})),a.d(t,"c",(function(){return g})),a.d(t,"b",(function(){return k})),a.d(t,"v",(function(){return w})),a.d(t,"y",(function(){return S})),a.d(t,"j",(function(){return I})),a.d(t,"q",(function(){return N})),a.d(t,"B",(function(){return x})),a.d(t,"m",(function(){return P})),a.d(t,"r",(function(){return j})),a.d(t,"p",(function(){return O})),a.d(t,"d",(function(){return F})),a.d(t,"u",(function(){return q}));var r=a("b775");function n(e){return Object(r["a"])({url:"/vw_account_loan/vw_account_loan/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/vw_account_loan/vw_account_loan/listBySlippageStatus3",method:"get",params:e})}function l(e){return Object(r["a"])({url:"/vw_account_loan/vw_account_loan/byLoanId/"+e,method:"get"})}function s(){return Object(r["a"])({url:"/bank_account/bank_account/bank?pageSize=30",method:"get"})}function i(e){return Object(r["a"])({url:"/loan_compensation/loan_compensation/detail",method:"get",params:e})}function u(e){return Object(r["a"])({url:"/vw_car/vw_car/"+e,method:"get"})}function c(e){return Object(r["a"])({url:"/loan_reminder/loan_reminder/list",method:"get",params:e})}function m(e){return Object(r["a"])({url:"/loan_reminder/loan_reminder",method:"post",data:e})}function d(e){return Object(r["a"])({url:"/vw_account_loan/vw_account_loan",method:"post",data:e})}function p(e){return Object(r["a"])({url:"/vw_account_loan/vw_account_loan",method:"put",data:e})}function f(e){return Object(r["a"])({url:"/vw_account_loan/vw_account_loan/"+e,method:"delete"})}function b(e,t){return Object(r["a"])({url:"/vw_customer_info/vw_customer_info/"+e+"?applyNo="+t,method:"get"})}function h(e){return Object(r["a"])({url:"/loan_settle/loan_settle/detail",method:"get",params:e})}function y(e){return Object(r["a"])({url:"/trial_balance/trial_balance/submit",method:"post",data:e})}function _(e){return Object(r["a"])({url:"/trial_balance/trial_balance/submitdc",method:"post",data:e})}function v(e){return Object(r["a"])({url:"/loan_settle/loan_settle",method:"post",data:e})}function g(e,t){return Object(r["a"])({url:"/loan_settle/loan_settle/process",method:"post",data:{loanId:e,status:t}})}function k(e){return Object(r["a"])({url:"/loan_compensation/loan_compensation/initiate",method:"post",data:e})}function w(e){return Object(r["a"])({url:"/loan_compensation/loan_compensation",method:"put",data:e})}function S(e){return Object(r["a"])({url:"/loan_settle/loan_settle",method:"put",data:e})}function I(e){return Object(r["a"])({url:"/system/user/listByRoleId",method:"get",params:{roleId:e}})}function N(e){return Object(r["a"])({url:"/loan_list/loan_list/batchUpdateUrgeUser",method:"put",data:e})}function x(e){return Object(r["a"])({url:"/loan_list/loan_list",method:"put",data:e})}function P(e){return Object(r["a"])({url:"/vw_account_loan/vw_account_loan/extension_list",method:"get",params:e})}function j(e){return Object(r["a"])({url:"/loan_extension/loan_extension/extension_detail",method:"get",params:{loan_id:e}})}function O(e){return Object(r["a"])({url:"/loan_extension/loan_extension/approve",method:"put",data:e})}function F(e){return Object(r["a"])({url:"/loan_list/loan_list/batchAssignPetitionUser",method:"put",data:e})}function q(e){return Object(r["a"])({url:"/loan_list/loan_list/revokePetitionUser/".concat(e),method:"put"})}},d6fd:function(e,t,a){"use strict";a("1791")}}]);