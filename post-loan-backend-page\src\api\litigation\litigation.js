import request from '@/utils/request'

// 查询法诉案件列表
export function listLitigation(query) {
  return request({
    url: '/vw_litigation_case_full/vw_litigation_case_full/list',
    method: 'get',
    params: query
  })
}

// 查询法诉案件详细
export function getLitigation(id) {
  return request({
    url: '/litigation_case/litigation_case/' + id,
    method: 'get'
  })
}

// 新增法诉案件
export function addLitigation(data) {
  return request({
    url: '/litigation_case/litigation_case',
    method: 'post',
    data: data
  })
}

// 法诉分期申请
export function addLitigationInstallment(data) {
  return request({
    url: '/litigation_case/litigation_case/installment',
    method: 'post',
    data: data
  })
}

// 修改法诉案件
export function updateLitigation(data) {
  return request({
    url: '/litigation_case/litigation_case',
    method: 'put',
    data: data
  })
}

// 删除法诉案件
export function delLitigation(id) {
  return request({
    url: '/litigation_case/litigation_case/' + id,
    method: 'delete'
  })
}

// 根据loanId查询已有法诉案件列表
export function getLitigationByLoanId(loanId) {
  return request({
    url: '/litigation_case/litigation_case/byLoanId/' + loanId,
    method: 'get'
  })
}

//提交法诉日志
export function submitLitigationLog(data) {
  return request({
    url: '/common/public/insertLoanReminderAndLitigationLog',
    method: 'post',
    data: data
  })
}

// 查询法诉费用明细列表
export function listLitigation_cost(query) {
  return request({
    url: '/litigation_cost/litigation_cost/list',
    method: 'get',
    params: query
  })
}

// 查询法诉费用明细详细
export function getLitigation_cost(id) {
  return request({
    url: '/litigation_cost/litigation_cost/' + id,
    method: 'get'
  })
}

// 新增法诉费用明细
export function addLitigation_cost(data) {
  return request({
    url: '/litigation_cost/litigation_cost',
    method: 'post',
    data: data
  })
}

// 修改法诉费用明细
export function updateLitigation_cost(data) {
  return request({
    url: '/litigation_cost/litigation_cost',
    method: 'put',
    data: data
  })
}

// 删除法诉费用明细
export function delLitigation_cost(id) {
  return request({
    url: '/litigation_cost/litigation_cost/' + id,
    method: 'delete'
  })
}

// 检查已提交的限制性费用类型（判决金额和利息）
export function checkSubmittedLimitedFees(litigationCaseId) {
  return request({
    url: '/litigation_cost/litigation_cost/checkLimitedFees/' + litigationCaseId,
    method: 'get'
  })
}

// 获取法诉费用汇总数据
export function getLitigationCostSummary(caseIds) {
  return request({
    url: '/litigation_cost/litigation_cost/summary',
    method: 'post',
    data: { caseIds: caseIds }
  })
}

// 查询法诉日志列表
export function listLitigation_log(query) {
  return request({
    url: '/litigation_log/litigation_log/list',
    method: 'get',
    params: query
  })
}

// 查询法诉日志详细
export function getLitigation_log(id) {
  return request({
    url: '/litigation_log/litigation_log/' + id,
    method: 'get'
  })
}

// 新增法诉日志
export function addLitigation_log(data) {
  return request({
    url: '/litigation_log/litigation_log',
    method: 'post',
    data: data
  })
}

// 修改法诉日志
export function updateLitigation_log(data) {
  return request({
    url: '/litigation_log/litigation_log',
    method: 'put',
    data: data
  })
}

// 删除法诉日志
export function delLitigation_log(id) {
  return request({
    url: '/litigation_log/litigation_log/' + id,
    method: 'delete'
  })
}