(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-777ec67c"],{"0f5f":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.carInfo.plateNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车架号"}},[e._v(e._s(e.carInfo.identityNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"发动机号"}},[e._v(e._s(e.carInfo.engineNumber||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(e._s(e.carStatusLabel))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆位置"}},[e._v(e._s(e.carInfo.carAddress||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(e._s(e.gpsStatusLabel))]):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看车牌照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"行驶证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看行驶证照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"登记证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看登记证照片")])],1):e._e()],1)],1)},i=[],n=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),a("bd52")),r={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var e=this,t=this.carStatusList.find((function(t){return t.value===e.carInfo.carStatus}));return t?t.label:"未知状态"},gpsStatusLabel:function(){var e=this,t=this.gpsStatusList.find((function(t){return t.value===e.carInfo.gpsStatus}));return t?t.label:"未知状态"}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,e&&this.plateNo&&Object(n["e"])(this.plateNo).then((function(e){t.carInfo=e.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},s=r,o=a("2877"),u=Object(o["a"])(s,l,i,!1,null,null,null);t["a"]=u.exports},1791:function(e,t,a){},"192a":function(e,t,a){},"2eca":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[e._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},i=[],n=a("bd52"),r={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(n["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},s=r,o=(a("d6fd"),a("2877")),u=Object(o["a"])(s,l,i,!1,null,"8a3d4978",null);t["a"]=u.exports},"73a5":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"certId"}},[a("el-input",{attrs:{placeholder:"贷款人身份证号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.certId,callback:function(t){e.$set(e.queryParams,"certId",t)},expression:"queryParams.certId"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"salesman"}},[a("el-input",{attrs:{placeholder:"业务员姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.salesman,callback:function(t){e.$set(e.queryParams,"salesman",t)},expression:"queryParams.salesman"}})],1),a("el-form-item",{attrs:{label:"",prop:"jgName"}},[a("el-input",{attrs:{placeholder:"录单渠道名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jgName,callback:function(t){e.$set(e.queryParams,"jgName",t)},expression:"queryParams.jgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"followUp"}},[a("el-input",{attrs:{placeholder:"跟催员",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.followUp,callback:function(t){e.$set(e.queryParams,"followUp",t)},expression:"queryParams.followUp"}})],1),a("el-form-item",{attrs:{label:""}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.handleDateChange},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.loan_reminder_approveList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"贷款人姓名",align:"center",prop:"customerName"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openUserInfo({customerId:t.row.customerId,applyId:t.row.applyId})}}},[e._v(" "+e._s(t.row.customerName)+" ")])]}}])}),a("el-table-column",{attrs:{label:"贷款人身份证号",align:"center",prop:"certId"}}),a("el-table-column",{attrs:{label:"车牌号",align:"center",prop:"plateNo"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openCarInfo(t.row.plateNo)}}},[e._v(e._s(t.row.plateNo))])]}}])}),a("el-table-column",{attrs:{label:"业务员",align:"center",prop:"salesman"}}),a("el-table-column",{attrs:{label:"录单渠道",align:"center",prop:"jgName"}}),a("el-table-column",{attrs:{label:"跟催员",align:"center",prop:"followUp"}}),a("el-table-column",{attrs:{label:"车辆状态",align:"center",prop:"carStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.getCarStatusLabel(t.row.carStatus)))])]}}])}),a("el-table-column",{attrs:{label:"还款状态",align:"center",prop:"repaymentStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.getRepaymentStatusLabel(t.row.repaymentStatus)))])]}}])}),a("el-table-column",{attrs:{label:"催记类型",align:"center",prop:"urgeStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.getFollowUpLabel(t.row.urgeStatus)))])]}}])}),a("el-table-column",{attrs:{label:"审批状态",align:"center",prop:"examineStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.examineStatus||null==t.row.examineStatus?a("span",{staticStyle:{color:"#E6A23C"}},[e._v("未审批")]):e._e(),1==t.row.examineStatus?a("span",{staticStyle:{color:"#67C23A"}},[e._v("贷后通过")]):e._e(),2==t.row.examineStatus?a("span",{staticStyle:{color:"#67C23A"}},[e._v("法诉通过")]):e._e(),3==t.row.examineStatus?a("span",{staticStyle:{color:"#F56C6C"}},[e._v("拒绝")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[0!=t.row.examineStatus&&null!=t.row.examineStatus&&void 0!==t.row.examineStatus||!((1==t.row.status||3==t.row.status)&&e.isClerkSupervisor||2==t.row.status&&e.isJudicialDirector)?e._e():a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleApprove(t.row)}}},[e._v(" 审批 ")]),1==t.row.examineStatus||2==t.row.examineStatus||3==t.row.examineStatus?a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleView(t.row)}}},[e._v(" 查看 ")]):e._e()]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:"催记审批",visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-descriptions",{attrs:{column:2,border:""}},[a("el-descriptions-item",{attrs:{label:"贷款人"}},[e._v(" "+e._s(e.logDetail.customerName||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"出单渠道"}},[e._v(" "+e._s(e.logDetail.jgName||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"放款银行"}},[e._v(" "+e._s(e.logDetail.lendingBank||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"银行逾期金额"}},[e._v(" "+e._s(e.logDetail.bankyqMoney||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"代扣逾期金额"}},[e._v(" "+e._s(e.logDetail.dkyqMoney||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(" "+e._s(e.getCarStatusLabel(e.logDetail.carStatus))+" ")]),a("el-descriptions-item",{attrs:{label:"还款类型",span:2}},[e._v(" "+e._s(e.getRepaymentStatusLabel(e.logDetail.repaymentStatus))+" ")]),a("el-descriptions-item",{attrs:{label:"还款时间"}},[e._v(" "+e._s(e.logDetail.appointedTime||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"预计还款时间"}},[e._v(" "+e._s(e.logDetail.appointedTime||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"银行还款金额"}},[e._v(" "+e._s(e.logDetail.bmoney||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"账号类型"}},[e._v(" "+e._s(e.logDetail.baccount)+" ")]),e.getBrepaymentImgList(e.logDetail.brepaymentImg).length>0?a("el-descriptions-item",{attrs:{label:"还款凭证",span:2}},e._l(e.getBrepaymentImgList(e.logDetail.brepaymentImg),(function(t,l){return a("el-image",{key:l,staticStyle:{width:"102px",height:"102px","margin-right":"10px"},attrs:{src:t,fit:"cover","preview-src-list":e.getBrepaymentImgList(e.logDetail.brepaymentImg)}})})),1):e._e(),a("el-descriptions-item",{attrs:{label:"代扣还款金额"}},[e._v(" "+e._s(e.logDetail.dmoney||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"账号类型"}},[e._v(" "+e._s(e.logDetail.daccount)+" ")]),e.getDrepaymentImgList(e.logDetail.drepaymentImg).length>0?a("el-descriptions-item",{staticStyle:{height:"120px !important"},attrs:{label:"还款凭证",span:2}},e._l(e.getDrepaymentImgList(e.logDetail.drepaymentImg),(function(t,l){return a("el-image",{key:l,staticStyle:{width:"102px",height:"102px","margin-right":"10px"},attrs:{src:t,fit:"cover","preview-src-list":e.getDrepaymentImgList(e.logDetail.drepaymentImg)}})})),1):e._e(),a("el-descriptions-item",{attrs:{label:"其他还款金额"}},[e._v(" "+e._s(e.logDetail.omoney||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"账号类型"}},[e._v(" "+e._s(e.logDetail.oaccount)+" ")]),e.getOrepaymentImgList(e.logDetail.orepaymentImg).length>0?a("el-descriptions-item",{attrs:{label:"还款凭证",span:2}},e._l(e.getOrepaymentImgList(e.logDetail.orepaymentImg),(function(t,l){return a("el-image",{key:l,staticStyle:{width:"102px",height:"102px","margin-right":"10px"},attrs:{src:t,fit:"cover","preview-src-list":e.getOrepaymentImgList(e.logDetail.orepaymentImg)}})})),1):e._e(),a("el-descriptions-item",{attrs:{label:"代偿还款金额"}},[e._v(" "+e._s(e.logDetail.cmoney||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"账号类型"}},[e._v(" "+e._s(e.logDetail.caccount)+" ")]),e.getCrePaymentImgList(e.logDetail.crepaymentImg).length>0?a("el-descriptions-item",{attrs:{label:"还款凭证",span:2}},e._l(e.getCrePaymentImgList(e.logDetail.crepaymentImg),(function(t,l){return a("el-image",{key:l,staticStyle:{width:"102px",height:"102px","margin-right":"10px"},attrs:{src:t,fit:"cover","preview-src-list":e.getCrePaymentImgList(e.logDetail.crepaymentImg)}})})),1):e._e(),a("el-descriptions-item",{attrs:{label:"催记类型"}},[e._v(" "+e._s(e.getFollowUpLabel(e.logDetail.urgeStatus))+" ")]),a("el-descriptions-item",{attrs:{label:"下次跟进时间"}},[e._v(" "+e._s(e.logDetail.trackingTime||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"催记描述",span:2}},[e._v(" "+e._s(e.logDetail.urgeDescribe||"-")+" ")])],1),a("el-form",{ref:"form",staticStyle:{"margin-top":"20px"},attrs:{model:e.form,"label-width":"120px","label-position":"left"}},[a("el-form-item",{attrs:{label:"审批结果"}},[a("el-radio-group",{model:{value:e.form.examineStatus,callback:function(t){e.$set(e.form,"examineStatus",t)},expression:"form.examineStatus"}},[a("el-radio",{attrs:{label:e.getApprovalPassValue()}},[e._v("通过")]),a("el-radio",{attrs:{label:3}},[e._v("拒绝")])],1)],1),3==e.form.examineStatus?a("el-form-item",{attrs:{label:"拒绝原因"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请填写拒绝原因"},model:{value:e.form.examineReason,callback:function(t){e.$set(e.form,"examineReason",t)},expression:"form.examineReason"}})],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitApprove}},[e._v("提交")]),a("el-button",{on:{click:function(t){return e.cancel()}}},[e._v("取消")])],1)],1),a("el-dialog",{attrs:{title:"催记查看",visible:e.viewOpen,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.viewOpen=t}}},[a("el-descriptions",{attrs:{column:2,border:""}},[a("el-descriptions-item",{attrs:{label:"贷款人"}},[e._v(" "+e._s(e.viewDetail.customerName||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"出单渠道"}},[e._v(" "+e._s(e.viewDetail.jgName||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"放款银行"}},[e._v(" "+e._s(e.viewDetail.lendingBank||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"银行逾期金额"}},[e._v(" "+e._s(e.viewDetail.bankyqMoney||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"代扣逾期金额"}},[e._v(" "+e._s(e.viewDetail.dkyqMoney||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(" "+e._s(e.getCarStatusLabel(e.viewDetail.carStatus))+" ")]),a("el-descriptions-item",{attrs:{label:"还款类型",span:2}},[e._v(" "+e._s(e.getRepaymentStatusLabel(e.viewDetail.repaymentStatus))+" ")]),a("el-descriptions-item",{attrs:{label:"还款时间"}},[e._v(" "+e._s(e.viewDetail.appointedTime||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"预计还款时间"}},[e._v(" "+e._s(e.viewDetail.appointedTime||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"银行还款金额"}},[e._v(" "+e._s(e.viewDetail.bmoney||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"账号类型"}},[e._v(" "+e._s(e.viewDetail.baccount)+" ")]),e.getBrepaymentImgList(e.viewDetail.brepaymentImg).length>0?a("el-descriptions-item",{attrs:{label:"还款凭证",span:2}},e._l(e.getBrepaymentImgList(e.viewDetail.brepaymentImg),(function(t,l){return a("el-image",{key:l,staticStyle:{width:"102px",height:"102px","margin-right":"10px"},attrs:{src:t,fit:"cover","preview-src-list":e.getBrepaymentImgList(e.viewDetail.brepaymentImg)}})})),1):e._e(),a("el-descriptions-item",{attrs:{label:"代扣还款金额"}},[e._v(" "+e._s(e.viewDetail.dmoney||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"账号类型"}},[e._v(" "+e._s(e.viewDetail.daccount)+" ")]),e.getDrepaymentImgList(e.viewDetail.drepaymentImg).length>0?a("el-descriptions-item",{staticStyle:{height:"120px !important"},attrs:{label:"还款凭证",span:2}},e._l(e.getDrepaymentImgList(e.viewDetail.drepaymentImg),(function(t,l){return a("el-image",{key:l,staticStyle:{width:"102px",height:"102px","margin-right":"10px"},attrs:{src:t,fit:"cover","preview-src-list":e.getDrepaymentImgList(e.viewDetail.drepaymentImg)}})})),1):e._e(),a("el-descriptions-item",{attrs:{label:"其他还款金额"}},[e._v(" "+e._s(e.viewDetail.omoney||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"账号类型"}},[e._v(" "+e._s(e.viewDetail.oaccount)+" ")]),e.getOrepaymentImgList(e.viewDetail.orepaymentImg).length>0?a("el-descriptions-item",{attrs:{label:"还款凭证",span:2}},e._l(e.getOrepaymentImgList(e.viewDetail.orepaymentImg),(function(t,l){return a("el-image",{key:l,staticStyle:{width:"102px",height:"102px","margin-right":"10px"},attrs:{src:t,fit:"cover","preview-src-list":e.getOrepaymentImgList(e.viewDetail.orepaymentImg)}})})),1):e._e(),a("el-descriptions-item",{attrs:{label:"代偿还款金额"}},[e._v(" "+e._s(e.viewDetail.cmoney||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"账号类型"}},[e._v(" "+e._s(e.viewDetail.caccount)+" ")]),e.getCrePaymentImgList(e.viewDetail.crepaymentImg).length>0?a("el-descriptions-item",{attrs:{label:"还款凭证",span:2}},e._l(e.getCrePaymentImgList(e.viewDetail.crepaymentImg),(function(t,l){return a("el-image",{key:l,staticStyle:{width:"102px",height:"102px","margin-right":"10px"},attrs:{src:t,fit:"cover","preview-src-list":e.getCrePaymentImgList(e.viewDetail.crepaymentImg)}})})),1):e._e(),a("el-descriptions-item",{attrs:{label:"催记类型"}},[e._v(" "+e._s(e.getFollowUpLabel(e.viewDetail.urgeStatus))+" ")]),a("el-descriptions-item",{attrs:{label:"下次跟进时间"}},[e._v(" "+e._s(e.viewDetail.trackingTime||" ")+" ")]),a("el-descriptions-item",{attrs:{label:"催记描述",span:2}},[e._v(" "+e._s(e.viewDetail.urgeDescribe||" ")+" ")]),a("el-descriptions-item",{attrs:{label:"审批状态",span:2}},[0==e.viewDetail.examineStatus||null==e.viewDetail.examineStatus?a("span",{staticStyle:{color:"#E6A23C"}},[e._v("未审批")]):e._e(),1==e.viewDetail.examineStatus?a("span",{staticStyle:{color:"#67C23A"}},[e._v("贷后通过")]):e._e(),2==e.viewDetail.examineStatus?a("span",{staticStyle:{color:"#67C23A"}},[e._v("法诉通过")]):e._e(),3==e.viewDetail.examineStatus?a("span",{staticStyle:{color:"#F56C6C"}},[e._v("拒绝")]):e._e()]),3==e.viewDetail.examineStatus?a("el-descriptions-item",{attrs:{label:"拒绝原因",span:2}},[e._v(" "+e._s(e.viewDetail.examineReason||" ")+" ")]):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.viewOpen=!1}}},[e._v("关闭")])],1)],1),a("userInfo",{ref:"userInfo",attrs:{visible:e.userInfoVisible,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.userInfoVisible=t}}}),a("carInfo",{ref:"carInfo",attrs:{visible:e.carInfoVisible,title:"车辆信息",plateNo:e.plateNo,permission:"2"},on:{"update:visible":function(t){e.carInfoVisible=t}}})],1)},i=[],n=(a("4de4"),a("7db0"),a("caad"),a("d81d"),a("d3b7"),a("2532"),a("498a"),a("0643"),a("2382"),a("fffc"),a("a573"),a("a72d")),r=a("2eca"),s=a("0f5f"),o={name:"Vw_account_loan",components:{userInfo:r["a"],carInfo:s["a"]},data:function(){return{loading:!1,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,loan_reminder_approveList:[],open:!1,viewOpen:!1,form:{},queryParams:{pageNum:1,pageSize:15,customerName:null,certId:null,plateNo:null,salesman:null,jgName:null,followUp:null,allocationTime:null,startTime:null,endTime:null},dateRange:[],customerInfo:{customerId:"",applyId:""},userInfoVisible:!1,plateNo:"",carInfoVisible:!1,logDetail:{},viewDetail:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],repaymentList:[{label:"未还款",value:0},{label:"还款",value:1},{label:"部分还款",value:2}],followUpList:[{label:"继续联系",value:1},{label:"约定还款",value:2},{label:"无法跟进",value:3}],type:null,isClerkSupervisor:!1,isJudicialDirector:!1}},created:function(){this.type=this.$route.query.type||null;var e=this.$store.state.user.roles||[];this.isClerkSupervisor=e.includes("admin")||e.includes("clerk_supervisor"),this.isJudicialDirector=e.includes("admin")||e.includes("judicial_director"),this.getList()},methods:{handleQuery:function(){this.queryParams.allocationTime&&(this.queryParams.startTime=this.queryParams.allocationTime[0],this.queryParams.endTime=this.queryParams.allocationTime[1]),this.dateRange&&2===this.dateRange.length?(this.queryParams.startTime=this.dateRange[0],this.queryParams.endTime=this.dateRange[1]):(this.queryParams.startTime=null,this.queryParams.endTime=null),this.queryParams.pageNum=1,this.getList()},handleDateChange:function(e){this.dateRange=e},resetQuery:function(){this.queryParams.customerName=null,this.queryParams.certId=null,this.queryParams.plateNo=null,this.queryParams.salesman=null,this.queryParams.jgName=null,this.queryParams.partnerId=null,this.queryParams.followUp=null,this.queryParams.followStatus=null,this.queryParams.allocationTime=null,this.queryParams.startTime=null,this.queryParams.endTime=null,this.dateRange=[],this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},getList:function(){var e=this;if("yq"==this.type)this.queryParams.status="1,3";else{if("fs"!=this.type)return void(this.loan_reminder_approveList=[]);this.queryParams.status="2"}this.loading=!0,Object(n["b"])(this.queryParams).then((function(t){e.loan_reminder_approveList=t.rows,e.total=t.total,e.loading=!1}))},handleApprove:function(e){this.open=!0,this.logDetail=e,this.form={id:e.id,examineStatus:null,examineReason:null}},handleView:function(e){this.viewDetail=e,this.viewOpen=!0},getApprovalPassValue:function(){return this.isClerkSupervisor&&"yq"===this.type?1:this.isJudicialDirector&&"fs"===this.type?2:1},getCarStatusLabel:function(e){var t=this.carStatusList.find((function(t){return t.value===e}));return t?t.label:" "},getRepaymentStatusLabel:function(e){var t=this.repaymentList.find((function(t){return t.value===e}));return t?t.label:" "},getFollowUpLabel:function(e){var t=this.followUpList.find((function(t){return t.value===e}));return t?t.label:" "},submitApprove:function(){var e=this;this.form.examineStatus?3!=this.form.examineStatus||this.form.examineReason?Object(n["c"])(this.form).then((function(t){200==t.code?(e.$message.success("审批成功"),e.open=!1,e.getList()):e.$message.error(t.msg)})):this.$message.error("请填写拒绝原因"):this.$message.error("请选择审批结果")},cancel:function(){this.open=!1,this.viewOpen=!1,this.form={},this.logDetail={},this.viewDetail={}},openUserInfo:function(e){this.customerInfo=e,this.userInfoVisible=!0},openCarInfo:function(e){this.plateNo=e,this.carInfoVisible=!0},getBrepaymentImgList:function(e){return e?Array.isArray(e)?e:e.split(",").filter((function(e){return e.trim()})):[]},getDrepaymentImgList:function(e){return e?Array.isArray(e)?e:e.split(",").filter((function(e){return e.trim()})):[]},getOrepaymentImgList:function(e){return e?Array.isArray(e)?e:e.split(",").filter((function(e){return e.trim()})):[]},getCrePaymentImgList:function(e){return e?Array.isArray(e)?e:e.split(",").filter((function(e){return e.trim()})):[]}}},u=o,c=(a("8668"),a("2877")),m=Object(c["a"])(u,l,i,!1,null,"65616fc2",null);t["default"]=m.exports},8668:function(e,t,a){"use strict";a("192a")},a72d:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"d",(function(){return n})),a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return s}));var l=a("b775");function i(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/list",method:"get",params:e})}function n(e){return Object(l["a"])({url:"/loan_reminder/loan_reminder/list",method:"get",params:e})}function r(e){return Object(l["a"])({url:"/loan_reminder/loan_reminder/list/approve",method:"get",params:e})}function s(e){return Object(l["a"])({url:"/loan_reminder/loan_reminder/approve",method:"put",data:e})}},bd52:function(e,t,a){"use strict";a.d(t,"l",(function(){return i})),a.d(t,"n",(function(){return n})),a.d(t,"h",(function(){return r})),a.d(t,"i",(function(){return s})),a.d(t,"o",(function(){return o})),a.d(t,"e",(function(){return u})),a.d(t,"s",(function(){return c})),a.d(t,"t",(function(){return m})),a.d(t,"a",(function(){return p})),a.d(t,"A",(function(){return d})),a.d(t,"g",(function(){return f})),a.d(t,"k",(function(){return b})),a.d(t,"w",(function(){return g})),a.d(t,"z",(function(){return _})),a.d(t,"f",(function(){return v})),a.d(t,"x",(function(){return y})),a.d(t,"c",(function(){return h})),a.d(t,"b",(function(){return w})),a.d(t,"v",(function(){return I})),a.d(t,"y",(function(){return S})),a.d(t,"j",(function(){return x})),a.d(t,"q",(function(){return D})),a.d(t,"B",(function(){return k})),a.d(t,"m",(function(){return L})),a.d(t,"r",(function(){return O})),a.d(t,"p",(function(){return P})),a.d(t,"d",(function(){return q})),a.d(t,"u",(function(){return j}));var l=a("b775");function i(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/list",method:"get",params:e})}function n(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/listBySlippageStatus3",method:"get",params:e})}function r(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/byLoanId/"+e,method:"get"})}function s(){return Object(l["a"])({url:"/bank_account/bank_account/bank?pageSize=30",method:"get"})}function o(e){return Object(l["a"])({url:"/loan_compensation/loan_compensation/detail",method:"get",params:e})}function u(e){return Object(l["a"])({url:"/vw_car/vw_car/"+e,method:"get"})}function c(e){return Object(l["a"])({url:"/loan_reminder/loan_reminder/list",method:"get",params:e})}function m(e){return Object(l["a"])({url:"/loan_reminder/loan_reminder",method:"post",data:e})}function p(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan",method:"post",data:e})}function d(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan",method:"put",data:e})}function f(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/"+e,method:"delete"})}function b(e,t){return Object(l["a"])({url:"/vw_customer_info/vw_customer_info/"+e+"?applyNo="+t,method:"get"})}function g(e){return Object(l["a"])({url:"/loan_settle/loan_settle/detail",method:"get",params:e})}function _(e){return Object(l["a"])({url:"/trial_balance/trial_balance/submit",method:"post",data:e})}function v(e){return Object(l["a"])({url:"/trial_balance/trial_balance/submitdc",method:"post",data:e})}function y(e){return Object(l["a"])({url:"/loan_settle/loan_settle",method:"post",data:e})}function h(e,t){return Object(l["a"])({url:"/loan_settle/loan_settle/process",method:"post",data:{loanId:e,status:t}})}function w(e){return Object(l["a"])({url:"/loan_compensation/loan_compensation/initiate",method:"post",data:e})}function I(e){return Object(l["a"])({url:"/loan_compensation/loan_compensation",method:"put",data:e})}function S(e){return Object(l["a"])({url:"/loan_settle/loan_settle",method:"put",data:e})}function x(e){return Object(l["a"])({url:"/system/user/listByRoleId",method:"get",params:{roleId:e}})}function D(e){return Object(l["a"])({url:"/loan_list/loan_list/batchUpdateUrgeUser",method:"put",data:e})}function k(e){return Object(l["a"])({url:"/loan_list/loan_list",method:"put",data:e})}function L(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/extension_list",method:"get",params:e})}function O(e){return Object(l["a"])({url:"/loan_extension/loan_extension/extension_detail",method:"get",params:{loan_id:e}})}function P(e){return Object(l["a"])({url:"/loan_extension/loan_extension/approve",method:"put",data:e})}function q(e){return Object(l["a"])({url:"/loan_list/loan_list/batchAssignPetitionUser",method:"put",data:e})}function j(e){return Object(l["a"])({url:"/loan_list/loan_list/revokePetitionUser/".concat(e),method:"put"})}},d6fd:function(e,t,a){"use strict";a("1791")}}]);