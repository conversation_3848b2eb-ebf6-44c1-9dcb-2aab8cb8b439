{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.addLitigation = addLitigation;\nexports.addLitigationInstallment = addLitigationInstallment;\nexports.addLitigation_cost = addLitigation_cost;\nexports.addLitigation_log = addLitigation_log;\nexports.checkSubmittedLimitedFees = checkSubmittedLimitedFees;\nexports.delLitigation = delLitigation;\nexports.delLitigation_cost = delLitigation_cost;\nexports.delLitigation_log = delLitigation_log;\nexports.getLitigation = getLitigation;\nexports.getLitigationByLoanId = getLitigationByLoanId;\nexports.getLitigationCostSummary = getLitigationCostSummary;\nexports.getLitigation_cost = getLitigation_cost;\nexports.getLitigation_log = getLitigation_log;\nexports.listLitigation = listLitigation;\nexports.listLitigation_cost = listLitigation_cost;\nexports.listLitigation_log = listLitigation_log;\nexports.submitLitigationLog = submitLitigationLog;\nexports.updateLitigation = updateLitigation;\nexports.updateLitigation_cost = updateLitigation_cost;\nexports.updateLitigation_log = updateLitigation_log;\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\n// 查询法诉案件列表\nfunction listLitigation(query) {\n  return (0, _request.default)({\n    url: '/vw_litigation_case_full/vw_litigation_case_full/list',\n    method: 'get',\n    params: query\n  });\n}\n\n// 查询法诉案件详细\nfunction getLitigation(id) {\n  return (0, _request.default)({\n    url: '/litigation_case/litigation_case/' + id,\n    method: 'get'\n  });\n}\n\n// 新增法诉案件\nfunction addLitigation(data) {\n  return (0, _request.default)({\n    url: '/litigation_case/litigation_case',\n    method: 'post',\n    data: data\n  });\n}\n\n// 法诉分期申请\nfunction addLitigationInstallment(data) {\n  return (0, _request.default)({\n    url: '/litigation_case/litigation_case/installment',\n    method: 'post',\n    data: data\n  });\n}\n\n// 修改法诉案件\nfunction updateLitigation(data) {\n  return (0, _request.default)({\n    url: '/litigation_case/litigation_case',\n    method: 'put',\n    data: data\n  });\n}\n\n// 删除法诉案件\nfunction delLitigation(id) {\n  return (0, _request.default)({\n    url: '/litigation_case/litigation_case/' + id,\n    method: 'delete'\n  });\n}\n\n// 根据loanId查询已有法诉案件列表\nfunction getLitigationByLoanId(loanId) {\n  return (0, _request.default)({\n    url: '/litigation_case/litigation_case/byLoanId/' + loanId,\n    method: 'get'\n  });\n}\n\n//提交法诉日志\nfunction submitLitigationLog(data) {\n  return (0, _request.default)({\n    url: '/common/public/insertLoanReminderAndLitigationLog',\n    method: 'post',\n    data: data\n  });\n}\n\n// 查询法诉费用明细列表\nfunction listLitigation_cost(query) {\n  return (0, _request.default)({\n    url: '/litigation_cost/litigation_cost/list',\n    method: 'get',\n    params: query\n  });\n}\n\n// 查询法诉费用明细详细\nfunction getLitigation_cost(id) {\n  return (0, _request.default)({\n    url: '/litigation_cost/litigation_cost/' + id,\n    method: 'get'\n  });\n}\n\n// 新增法诉费用明细\nfunction addLitigation_cost(data) {\n  return (0, _request.default)({\n    url: '/litigation_cost/litigation_cost',\n    method: 'post',\n    data: data\n  });\n}\n\n// 修改法诉费用明细\nfunction updateLitigation_cost(data) {\n  return (0, _request.default)({\n    url: '/litigation_cost/litigation_cost',\n    method: 'put',\n    data: data\n  });\n}\n\n// 删除法诉费用明细\nfunction delLitigation_cost(id) {\n  return (0, _request.default)({\n    url: '/litigation_cost/litigation_cost/' + id,\n    method: 'delete'\n  });\n}\n\n// 检查已提交的限制性费用类型（判决金额和利息）\nfunction checkSubmittedLimitedFees(litigationCaseId) {\n  return (0, _request.default)({\n    url: '/litigation_cost/litigation_cost/checkLimitedFees/' + litigationCaseId,\n    method: 'get'\n  });\n}\n\n// 获取法诉费用汇总数据\nfunction getLitigationCostSummary(caseIds) {\n  return (0, _request.default)({\n    url: '/litigation_cost/litigation_cost/summary',\n    method: 'post',\n    data: {\n      caseIds: caseIds\n    }\n  });\n}\n\n// 查询法诉日志列表\nfunction listLitigation_log(query) {\n  return (0, _request.default)({\n    url: '/litigation_log/litigation_log/list',\n    method: 'get',\n    params: query\n  });\n}\n\n// 查询法诉日志详细\nfunction getLitigation_log(id) {\n  return (0, _request.default)({\n    url: '/litigation_log/litigation_log/' + id,\n    method: 'get'\n  });\n}\n\n// 新增法诉日志\nfunction addLitigation_log(data) {\n  return (0, _request.default)({\n    url: '/litigation_log/litigation_log',\n    method: 'post',\n    data: data\n  });\n}\n\n// 修改法诉日志\nfunction updateLitigation_log(data) {\n  return (0, _request.default)({\n    url: '/litigation_log/litigation_log',\n    method: 'put',\n    data: data\n  });\n}\n\n// 删除法诉日志\nfunction delLitigation_log(id) {\n  return (0, _request.default)({\n    url: '/litigation_log/litigation_log/' + id,\n    method: 'delete'\n  });\n}", "map": {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listLitigation", "query", "request", "url", "method", "params", "getLitigation", "id", "addLitigation", "data", "addLitigationInstallment", "updateLitigation", "delLitigation", "getLitigationByLoanId", "loanId", "submitLitigationLog", "listLitigation_cost", "getLitigation_cost", "addLitigation_cost", "updateLitigation_cost", "delLitigation_cost", "checkSubmittedLimitedFees", "litigationCaseId", "getLitigationCostSummary", "caseIds", "listLitigation_log", "getLitigation_log", "addLitigation_log", "updateLitigation_log", "delLitigation_log"], "sources": ["D:/code_project/java_project/loan/post-loan-backend-page/src/api/litigation/litigation.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询法诉案件列表\r\nexport function listLitigation(query) {\r\n  return request({\r\n    url: '/vw_litigation_case_full/vw_litigation_case_full/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询法诉案件详细\r\nexport function getLitigation(id) {\r\n  return request({\r\n    url: '/litigation_case/litigation_case/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增法诉案件\r\nexport function addLitigation(data) {\r\n  return request({\r\n    url: '/litigation_case/litigation_case',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 法诉分期申请\r\nexport function addLitigationInstallment(data) {\r\n  return request({\r\n    url: '/litigation_case/litigation_case/installment',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改法诉案件\r\nexport function updateLitigation(data) {\r\n  return request({\r\n    url: '/litigation_case/litigation_case',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除法诉案件\r\nexport function delLitigation(id) {\r\n  return request({\r\n    url: '/litigation_case/litigation_case/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 根据loanId查询已有法诉案件列表\r\nexport function getLitigationByLoanId(loanId) {\r\n  return request({\r\n    url: '/litigation_case/litigation_case/byLoanId/' + loanId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n//提交法诉日志\r\nexport function submitLitigationLog(data) {\r\n  return request({\r\n    url: '/common/public/insertLoanReminderAndLitigationLog',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 查询法诉费用明细列表\r\nexport function listLitigation_cost(query) {\r\n  return request({\r\n    url: '/litigation_cost/litigation_cost/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询法诉费用明细详细\r\nexport function getLitigation_cost(id) {\r\n  return request({\r\n    url: '/litigation_cost/litigation_cost/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增法诉费用明细\r\nexport function addLitigation_cost(data) {\r\n  return request({\r\n    url: '/litigation_cost/litigation_cost',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改法诉费用明细\r\nexport function updateLitigation_cost(data) {\r\n  return request({\r\n    url: '/litigation_cost/litigation_cost',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除法诉费用明细\r\nexport function delLitigation_cost(id) {\r\n  return request({\r\n    url: '/litigation_cost/litigation_cost/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 检查已提交的限制性费用类型（判决金额和利息）\r\nexport function checkSubmittedLimitedFees(litigationCaseId) {\r\n  return request({\r\n    url: '/litigation_cost/litigation_cost/checkLimitedFees/' + litigationCaseId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 获取法诉费用汇总数据\r\nexport function getLitigationCostSummary(caseIds) {\r\n  return request({\r\n    url: '/litigation_cost/litigation_cost/summary',\r\n    method: 'post',\r\n    data: { caseIds: caseIds }\r\n  })\r\n}\r\n\r\n// 查询法诉日志列表\r\nexport function listLitigation_log(query) {\r\n  return request({\r\n    url: '/litigation_log/litigation_log/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询法诉日志详细\r\nexport function getLitigation_log(id) {\r\n  return request({\r\n    url: '/litigation_log/litigation_log/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增法诉日志\r\nexport function addLitigation_log(data) {\r\n  return request({\r\n    url: '/litigation_log/litigation_log',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改法诉日志\r\nexport function updateLitigation_log(data) {\r\n  return request({\r\n    url: '/litigation_log/litigation_log',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除法诉日志\r\nexport function delLitigation_log(id) {\r\n  return request({\r\n    url: '/litigation_log/litigation_log/' + id,\r\n    method: 'delete'\r\n  })\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uDAAuD;IAC5DC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,aAAaA,CAACC,EAAE,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mCAAmC,GAAGI,EAAE;IAC7CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,wBAAwBA,CAACD,IAAI,EAAE;EAC7C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,8CAA8C;IACnDC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,gBAAgBA,CAACF,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,aAAaA,CAACL,EAAE,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mCAAmC,GAAGI,EAAE;IAC7CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,qBAAqBA,CAACC,MAAM,EAAE;EAC5C,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,4CAA4C,GAAGW,MAAM;IAC1DV,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,mBAAmBA,CAACN,IAAI,EAAE;EACxC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mDAAmD;IACxDC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,mBAAmBA,CAACf,KAAK,EAAE;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASgB,kBAAkBA,CAACV,EAAE,EAAE;EACrC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mCAAmC,GAAGI,EAAE;IAC7CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,kBAAkBA,CAACT,IAAI,EAAE;EACvC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,qBAAqBA,CAACV,IAAI,EAAE;EAC1C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,kBAAkBA,CAACb,EAAE,EAAE;EACrC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mCAAmC,GAAGI,EAAE;IAC7CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASiB,yBAAyBA,CAACC,gBAAgB,EAAE;EAC1D,OAAO,IAAApB,gBAAO,EAAC;IACbC,GAAG,EAAE,oDAAoD,GAAGmB,gBAAgB;IAC5ElB,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASmB,wBAAwBA,CAACC,OAAO,EAAE;EAChD,OAAO,IAAAtB,gBAAO,EAAC;IACbC,GAAG,EAAE,0CAA0C;IAC/CC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAE;MAAEe,OAAO,EAAEA;IAAQ;EAC3B,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,kBAAkBA,CAACxB,KAAK,EAAE;EACxC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASyB,iBAAiBA,CAACnB,EAAE,EAAE;EACpC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC,GAAGI,EAAE;IAC3CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASuB,iBAAiBA,CAAClB,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASmB,oBAAoBA,CAACnB,IAAI,EAAE;EACzC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASoB,iBAAiBA,CAACtB,EAAE,EAAE;EACpC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC,GAAGI,EAAE;IAC3CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}