<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.litigation_log.mapper.LitigationLogMapper">
    
    <resultMap type="LitigationLog" id="LitigationLogResult">
        <result property="id"    column="id"    />
        <result property="loanId"    column="loan_id"    />
        <result property="docName"    column="doc_name"    />
        <result property="docNumber"    column="doc_number"    />
        <result property="docUploadUrl"    column="doc_upload_url"    />
        <result property="openDate"    column="open_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="litigationId"    column="litigation_id"    />
        <result property="status"    column="status"    />
        <result property="docEffectiveDate"    column="doc_effective_date"    />
        <result property="urgeDescribe"    column="urge_describe"    />
    </resultMap>

    <sql id="selectLitigationLogVo">
        select id, loan_id, doc_name, doc_number, doc_upload_url, open_date, create_by, create_time, update_by, update_time, litigation_id, status, doc_effective_date, urge_describe from litigation_log
    </sql>

    <select id="selectLitigationLogList" parameterType="LitigationLog" resultMap="LitigationLogResult">
        <include refid="selectLitigationLogVo"/>
        <where>  
            <if test="loanId != null "> and loan_id = #{loanId}</if>
            <if test="docName != null  and docName != ''"> and doc_name like concat('%', #{docName}, '%')</if>
            <if test="docNumber != null  and docNumber != ''"> and doc_number = #{docNumber}</if>
            <if test="docUploadUrl != null  and docUploadUrl != ''"> and doc_upload_url = #{docUploadUrl}</if>
            <if test="openDate != null "> and open_date = #{openDate}</if>
            <if test="litigationId != null "> and litigation_id = #{litigationId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="docEffectiveDate != null "> and doc_effective_date = #{docEffectiveDate}</if>
        </where>
    </select>
    
    <select id="selectLitigationLogById" parameterType="Long" resultMap="LitigationLogResult">
        <include refid="selectLitigationLogVo"/>
        where id = #{id}
    </select>

    <select id="selectLatestLitigationLogByLitigationId" parameterType="Long" resultMap="LitigationLogResult">
        <include refid="selectLitigationLogVo"/>
        where litigation_id = #{litigationId}
        order by create_time desc
        limit 1
    </select>

    <insert id="insertLitigationLog" parameterType="LitigationLog" useGeneratedKeys="true" keyProperty="id">
        insert into litigation_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="loanId != null">loan_id,</if>
            <if test="docName != null">doc_name,</if>
            <if test="docNumber != null">doc_number,</if>
            <if test="docUploadUrl != null">doc_upload_url,</if>
            <if test="openDate != null">open_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="litigationId != null">litigation_id,</if>
            <if test="status != null">status,</if>
            <if test="docEffectiveDate != null">doc_effective_date,</if>
            <if test="urgeDescribe != null">urge_describe,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="loanId != null">#{loanId},</if>
            <if test="docName != null">#{docName},</if>
            <if test="docNumber != null">#{docNumber},</if>
            <if test="docUploadUrl != null">#{docUploadUrl},</if>
            <if test="openDate != null">#{openDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="litigationId != null">#{litigationId},</if>
            <if test="status != null">#{status},</if>
            <if test="docEffectiveDate != null">#{docEffectiveDate},</if>
            <if test="urgeDescribe != null">#{urgeDescribe},</if>
         </trim>
    </insert>

    <update id="updateLitigationLog" parameterType="LitigationLog">
        update litigation_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="loanId != null">loan_id = #{loanId},</if>
            <if test="docName != null">doc_name = #{docName},</if>
            <if test="docNumber != null">doc_number = #{docNumber},</if>
            <if test="docUploadUrl != null">doc_upload_url = #{docUploadUrl},</if>
            <if test="openDate != null">open_date = #{openDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="litigationId != null">litigation_id = #{litigationId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="docEffectiveDate != null">doc_effective_date = #{docEffectiveDate},</if>
            <if test="urgeDescribe != null">urge_describe = #{urgeDescribe},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLitigationLogById" parameterType="Long">
        delete from litigation_log where id = #{id}
    </delete>

    <delete id="deleteLitigationLogByIds" parameterType="String">
        delete from litigation_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>