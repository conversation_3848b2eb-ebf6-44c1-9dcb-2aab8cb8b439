(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0e6c6af6"],{"0f5f":function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.carInfo.plateNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车架号"}},[e._v(e._s(e.carInfo.identityNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"发动机号"}},[e._v(e._s(e.carInfo.engineNumber||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(e._s(e.carStatusLabel))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆位置"}},[e._v(e._s(e.carInfo.carAddress||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(e._s(e.gpsStatusLabel))]):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看车牌照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"行驶证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看行驶证照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"登记证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看登记证照片")])],1):e._e()],1)],1)},n=[],o=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),a("bd52")),l={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var e=this,t=this.carStatusList.find((function(t){return t.value===e.carInfo.carStatus}));return t?t.label:"未知状态"},gpsStatusLabel:function(){var e=this,t=this.gpsStatusList.find((function(t){return t.value===e.carInfo.gpsStatus}));return t?t.label:"未知状态"}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,e&&this.plateNo&&Object(o["e"])(this.plateNo).then((function(e){t.carInfo=e.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},i=l,s=a("2877"),c=Object(s["a"])(i,r,n,!1,null,null,null);t["a"]=c.exports},1791:function(e,t,a){},"2eca":function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[e._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},n=[],o=a("bd52"),l={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(o["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},i=l,s=(a("d6fd"),a("2877")),c=Object(s["a"])(i,r,n,!1,null,"8a3d4978",null);t["a"]=c.exports},"50a1":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"请输入车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"jgName"}},[a("el-input",{attrs:{placeholder:"录入渠道名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jgName,callback:function(t){e.$set(e.queryParams,"jgName",t)},expression:"queryParams.jgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"garageName"}},[a("el-input",{attrs:{placeholder:"请输入车库名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.garageName,callback:function(t){e.$set(e.queryParams,"garageName",t)},expression:"queryParams.garageName"}})],1),a("el-form-item",{attrs:{label:"",prop:"keyStatus"}},[a("el-select",{attrs:{placeholder:"请选择钥匙状态",clearable:""},model:{value:e.queryParams.keyStatus,callback:function(t){e.$set(e.queryParams,"keyStatus",t)},expression:"queryParams.keyStatus"}},e._l(e.keyStatusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"teamName"}},[a("el-input",{attrs:{placeholder:"找车团队",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.teamName,callback:function(t){e.$set(e.queryParams,"teamName",t)},expression:"queryParams.teamName"}})],1),a("el-form-item",{attrs:{label:"派单时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.queryParams.originallyTime,callback:function(t){e.$set(e.queryParams,"originallyTime",t)},expression:"queryParams.originallyTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vw_car_order_examineList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"customerName"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.customerId&&t.row.applyId?a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openUserInfo({customerId:t.row.customerId,applyId:t.row.applyId})}}},[e._v(" "+e._s(t.row.customerName)+" ")]):a("span",[e._v(e._s(t.row.customerName))])]}}])}),a("el-table-column",{attrs:{label:"联系电话",align:"center",prop:"mobilePhone"}}),a("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"jgName"}}),a("el-table-column",{attrs:{label:"车牌号",align:"center",prop:"plateNo"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openCarInfo(t.row.plateNo)}}},[e._v(e._s(t.row.plateNo))])]}}])}),a("el-table-column",{attrs:{label:"接单团队",align:"center",prop:"teamName"}}),a("el-table-column",{attrs:{label:"派单时间",align:"center",prop:"allocationTime",width:"180"}}),a("el-table-column",{attrs:{label:"钥匙状态",align:"center",prop:"keyStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(1==t.row.keyStatus?"已邮寄":2==t.row.keyStatus?"已收回":"未归还"))])]}}])}),a("el-table-column",{attrs:{label:"入库时间",align:"center",prop:"inboundTime",width:"180"}}),a("el-table-column",{attrs:{label:"找车佣金",align:"center",prop:"locatingCommission"}}),a("el-table-column",{attrs:{label:"佣金",align:"center",prop:"transportationFee"}}),a("el-table-column",{attrs:{label:"拖车费",align:"center",prop:"towingFee"}}),a("el-table-column",{attrs:{label:"贴机费",align:"center",prop:"trackerInstallationFee"}}),a("el-table-column",{attrs:{label:"其他报销",align:"center",prop:"otherReimbursement"}}),a("el-table-column",{attrs:{label:"合计费用",align:"center",prop:"totalMoney"}}),a("el-table-column",{attrs:{label:"审批状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.getStatusText(t.row.status)))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_car_order_examine:vw_car_order_examine:edit"],expression:"['vw_car_order_examine:vw_car_order_examine:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleViewDetails(t.row)}}},[e._v(" 审批 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:"找车费用审批详情",visible:e.detailsDialogVisible,width:"1200px","append-to-body":""},on:{"update:visible":function(t){e.detailsDialogVisible=t}}},[a("div",{staticClass:"approval-header"},[a("el-row",[a("el-col",{attrs:{span:8}},[a("strong",[e._v("贷款人：")]),e.currentOrderInfo&&e.currentOrderInfo.customerId&&e.currentOrderInfo.applyId?a("el-button",{staticStyle:{color:"#409EFF"},attrs:{type:"text"},on:{click:function(t){return e.openUserInfo({customerId:e.currentOrderInfo.customerId,applyId:e.currentOrderInfo.applyId})}}},[e._v(" "+e._s(e.currentOrderInfo.customerName)+" ")]):a("span",[e._v(e._s(e.currentOrderInfo?e.currentOrderInfo.customerName:""))])],1),a("el-col",{attrs:{span:8}},[a("strong",[e._v("接单团队：")]),e._v(e._s(e.currentOrderInfo?e.currentOrderInfo.teamName:"")+" ")]),a("el-col",{attrs:{span:8}},[a("strong",[e._v("车牌号：")]),e.currentOrderInfo&&e.currentOrderInfo.plateNo?a("el-button",{staticStyle:{color:"#409EFF"},attrs:{type:"text"},on:{click:function(t){return e.openCarInfo(e.currentOrderInfo.plateNo)}}},[e._v(" "+e._s(e.currentOrderInfo.plateNo)+" ")]):a("span",[e._v(e._s(e.currentOrderInfo?e.currentOrderInfo.plateNo:""))])],1)],1),a("el-row",{staticStyle:{"margin-top":"10px"}},[a("el-col",{attrs:{span:8}},[a("strong",[e._v("出单渠道：")]),e._v(e._s(e.currentOrderInfo?e.currentOrderInfo.jgName:"")+" ")]),a("el-col",{attrs:{span:8}},[a("strong",[e._v("派单时间：")]),e._v(e._s(e.currentOrderInfo?e.currentOrderInfo.allocationTime:"")+" ")]),a("el-col",{attrs:{span:8}},[a("strong",[e._v("钥匙状态：")]),e.currentOrderInfo?a("span",[e._v(e._s(1==e.currentOrderInfo.keyStatus?"已邮寄":2==e.currentOrderInfo.keyStatus?"已收回":"未归还"))]):e._e()])],1)],1),a("div",{staticClass:"batch-approval-section",staticStyle:{margin:"20px 0"}},[a("el-button",{attrs:{type:"success",size:"small",disabled:0===e.selectedRecords.length},on:{click:function(t){return e.handleBatchApprove("approve")}}},[e._v(" 批量通过 ("+e._s(e.selectedRecords.length)+") ")]),a("el-button",{attrs:{type:"danger",size:"small",disabled:0===e.selectedRecords.length},on:{click:function(t){return e.handleBatchApprove("reject")}}},[e._v(" 批量拒绝 ("+e._s(e.selectedRecords.length)+") ")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.recordsLoading,expression:"recordsLoading"}],attrs:{data:e.feeRecords},on:{"selection-change":e.handleRecordSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center",fixed:"left"}}),a("el-table-column",{attrs:{label:"提交时间",align:"center",prop:"applicationTime",width:"150"}}),a("el-table-column",{attrs:{label:"提交人",align:"center",prop:"applicationBy",width:"100"}}),a("el-table-column",{attrs:{label:"佣金",align:"center",prop:"transportationFee",width:"80"}}),a("el-table-column",{attrs:{label:"拖车费",align:"center",prop:"towingFee",width:"80"}}),a("el-table-column",{attrs:{label:"贴机费",align:"center",prop:"trackerInstallationFee",width:"80"}}),a("el-table-column",{attrs:{label:"其他报销",align:"center",prop:"otherReimbursement",width:"80"}}),a("el-table-column",{attrs:{label:"合计费用",align:"center",prop:"totalMoney",width:"100"}}),a("el-table-column",{attrs:{label:"审批状态",align:"center",prop:"status",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[null==t.row.status||0==t.row.status?a("el-tag",{attrs:{type:"info"}},[e._v("未审核")]):1==t.row.status?a("el-tag",{attrs:{type:"success"}},[e._v("已通过")]):2==t.row.status?a("el-tag",{attrs:{type:"danger"}},[e._v("已拒绝")]):a("el-tag",{attrs:{type:"warning"}},[e._v(e._s(e.getStatusText(t.row.status)))])]}}])}),a("el-table-column",{attrs:{label:"拒绝原因",align:"center",prop:"reasons",width:"150"}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"150",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.canApproveRecord(t.row)?a("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(a){return e.handleSingleApprove(t.row,"approve")}}},[e._v(" 通过 ")]):e._e(),e.canApproveRecord(t.row)?a("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(a){return e.handleSingleApprove(t.row,"reject")}}},[e._v(" 拒绝 ")]):a("div",[1==t.row.status?a("el-tag",{attrs:{type:"success",size:"mini"}},[e._v("已通过")]):2==t.row.status?a("el-tag",{attrs:{type:"danger",size:"mini"}},[e._v("已拒绝")]):[0,3,4,5,6].includes(t.row.status)||null==t.row.status?a("div",[a("el-tag",{attrs:{type:"info",size:"mini"}},[e._v(e._s(e.getStatusText(t.row.status)))]),a("div",{staticStyle:{"font-size":"11px",color:"#999","margin-top":"2px"}},[e._v(" 需要："+e._s(e.getRequiredRoleText(t.row.status))+" ")])],1):a("el-tag",{attrs:{type:"warning",size:"mini"}},[e._v(e._s(e.getStatusText(t.row.status)))])],1)]}}])})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.detailsDialogVisible=!1}}},[e._v("关 闭")])],1)],1),a("el-dialog",{attrs:{title:"审批确认",visible:e.singleApprovalDialogVisible,width:"400px","append-to-body":""},on:{"update:visible":function(t){e.singleApprovalDialogVisible=t}}},[a("el-form",{ref:"singleApprovalForm",attrs:{model:e.singleApprovalForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"审批结果"}},[a("el-tag",{attrs:{type:"approve"===e.singleApprovalForm.action?"success":"danger"}},[e._v(" "+e._s("approve"===e.singleApprovalForm.action?"通过":"拒绝")+" ")])],1),"reject"===e.singleApprovalForm.action?a("el-form-item",{attrs:{label:"拒绝原因",prop:"rejectReason",rules:[{required:!0,message:"请输入拒绝原因",trigger:"blur"}]}},[a("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入拒绝原因"},model:{value:e.singleApprovalForm.rejectReason,callback:function(t){e.$set(e.singleApprovalForm,"rejectReason",t)},expression:"singleApprovalForm.rejectReason"}})],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.confirmSingleApproval}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.singleApprovalDialogVisible=!1}}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"批量审批确认",visible:e.batchApprovalDialogVisible,width:"400px","append-to-body":""},on:{"update:visible":function(t){e.batchApprovalDialogVisible=t}}},[a("el-form",{ref:"batchApprovalForm",attrs:{model:e.batchApprovalForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"审批结果"}},[a("el-tag",{attrs:{type:"approve"===e.batchApprovalForm.action?"success":"danger"}},[e._v(" "+e._s("approve"===e.batchApprovalForm.action?"批量通过":"批量拒绝")+" ")])],1),a("el-form-item",{attrs:{label:"选中记录"}},[a("span",[e._v(e._s(e.selectedRecords.length)+" 条记录")])]),"reject"===e.batchApprovalForm.action?a("el-form-item",{attrs:{label:"拒绝原因",prop:"rejectReason",rules:[{required:!0,message:"请输入拒绝原因",trigger:"blur"}]}},[a("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入拒绝原因"},model:{value:e.batchApprovalForm.rejectReason,callback:function(t){e.$set(e.batchApprovalForm,"rejectReason",t)},expression:"batchApprovalForm.rejectReason"}})],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.confirmBatchApproval}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.batchApprovalDialogVisible=!1}}},[e._v("取 消")])],1)],1),a("userInfo",{ref:"userInfo",attrs:{visible:e.userInfoVisible,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.userInfoVisible=t}}}),a("carInfo",{ref:"carInfo",attrs:{visible:e.carInfoVisible,title:"车辆信息",plateNo:e.plateNo,permission:"2"},on:{"update:visible":function(t){e.carInfoVisible=t}}})],1)},n=[],o=a("5530"),l=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("b775"));function i(e){return Object(l["a"])({url:"/vw_car_order_examine/vw_car_order_examine/list",method:"get",params:e})}function s(){return Object(l["a"])({url:"/vm_car_order/vm_car_order/cate",method:"get"})}function c(e){return Object(l["a"])({url:"/vw_car_order_examine/vw_car_order_examine/"+e,method:"delete"})}function u(e){return Object(l["a"])({url:"/car_order_examine/car_order_examine/records/"+e,method:"get"})}function p(e){return Object(l["a"])({url:"/car_order_examine/car_order_examine/batchApprove",method:"post",data:e})}function d(e){return Object(l["a"])({url:"/car_order_examine/car_order_examine/singleApprove",method:"post",data:e})}var m=a("2eca"),f=a("0f5f"),b={name:"Vw_car_order_examine",components:{userInfo:m["a"],carInfo:f["a"]},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,vw_car_order_examineList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:15,teamName:null,keyStatus:null,originallyTime:null,startTime:"",endTime:"",customerName:null,plateNo:null,jgName:null,garageName:null},form:{id:"",status:0,newStatus:null,rejectReason:null,customerName:"",customerId:"",applyId:"",plateNo:"",jgName:"",teamName:"",allocationTime:"",keyStatus:"",totalMoney:"",_readonly:!1},rules:{id:[{required:!0,message:"$comment不能为空",trigger:"blur"}]},jgNameList:[{label:"A公司",value:1},{label:"B公司",value:2}],keyStatusList:[{label:"已邮寄",value:1},{label:"已收回",value:2},{label:"已归还",value:3}],teamList:[{label:"A团队",value:1},{label:"B团队",value:2}],customerInfo:{customerId:"",applyId:""},userInfoVisible:!1,plateNo:"",carInfoVisible:!1,detailsDialogVisible:!1,currentOrderInfo:null,feeRecords:[],recordsLoading:!1,selectedRecords:[],singleApprovalDialogVisible:!1,singleApprovalForm:{id:"",action:"",rejectReason:""},batchApprovalDialogVisible:!1,batchApprovalForm:{action:"",rejectReason:""}}},created:function(){this.getTeam(),this.getList()},methods:{getTeam:function(){var e=this;s().then((function(t){e.teamList=t.team,e.jgNameList=t.office}))},getList:function(){var e=this;this.loading=!0,i(this.queryParams).then((function(t){e.vw_car_order_examineList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:"",status:0,newStatus:null,rejectReason:null,customerName:"",customerId:"",applyId:"",plateNo:"",jgName:"",teamName:"",allocationTime:"",keyStatus:"",totalMoney:"",_readonly:!1},this.resetForm("form")},handleQuery:function(){this.queryParams.originallyTime&&(this.queryParams.startTime=this.queryParams.originallyTime[0],this.queryParams.endTime=this.queryParams.originallyTime[1]),this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.customerName=null,this.queryParams.plateNo=null,this.queryParams.jgName=null,this.queryParams.keyStatus=null,this.queryParams.teamName=null,this.queryParams.originallyTime=null,this.queryParams.startTime=null,this.queryParams.endTime=null,this.queryParams.garageName=null,this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加找车费用审批"},handleUpdate:function(e){this.form.id=e.id,this.form.rejectReason=e.rejectReason,this.form.status=e.status,this.open=!0,this.title="找车费用审批"},getStatusText:function(e){var t={0:"未审批",1:"全部通过",2:"已拒绝",3:"贷后主管审批",4:"总监审批",5:"财务主管/总监抄送",6:"总经理/董事长审批(抄送)"};return t[e]||"未知状态"},getRequiredRoleText:function(e){switch(e){case 0:case null:case void 0:return"贷后主管";case 3:return"总监";case 4:return"财务主管/总监";case 5:return"总经理/董事长";case 6:return"已完成";default:return"未知"}},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除找车费用审批编号为"'+a+'"的数据项？').then((function(){return c(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("vw_car_order_examine/vw_car_order_examine/export",Object(o["a"])({},this.queryParams),"vw_car_order_examine_".concat((new Date).getTime(),".xlsx"))},openUserInfo:function(e){console.log("点击客户信息:",e),e.customerId&&e.applyId?(this.customerInfo=e,this.userInfoVisible=!0):this.$modal.msgError("客户信息不完整，无法查看详情")},openCarInfo:function(e){this.plateNo=e,this.carInfoVisible=!0},handleViewDetails:function(e){this.currentOrderInfo=e,this.detailsDialogVisible=!0,this.loadFeeRecords(e.orderId)},loadFeeRecords:function(e){var t=this;this.recordsLoading=!0,u(e).then((function(e){t.feeRecords=e.data||[],t.recordsLoading=!1})).catch((function(e){t.recordsLoading=!1,console.error("加载费用记录失败:",e),t.$modal.msgError("加载费用记录失败")}))},handleRecordSelectionChange:function(e){this.selectedRecords=e},handleSingleApprove:function(e,t){this.singleApprovalForm.id=e.id,this.singleApprovalForm.action=t,this.singleApprovalForm.rejectReason="",this.singleApprovalDialogVisible=!0},confirmSingleApproval:function(){var e=this;"reject"===this.singleApprovalForm.action?this.$refs["singleApprovalForm"].validate((function(t){t&&e.executeSingleApproval()})):this.executeSingleApproval()},executeSingleApproval:function(){var e=this,t={id:this.singleApprovalForm.id,action:this.singleApprovalForm.action,rejectReason:this.singleApprovalForm.rejectReason};d(t).then((function(){e.$modal.msgSuccess("".concat("approve"===e.singleApprovalForm.action?"通过":"拒绝","审批成功")),e.singleApprovalDialogVisible=!1,e.loadFeeRecords(e.currentOrderInfo.id),e.getList()})).catch((function(e){console.error("审批失败:",e)}))},handleBatchApprove:function(e){0!==this.selectedRecords.length?(this.batchApprovalForm.action=e,this.batchApprovalForm.rejectReason="",this.batchApprovalDialogVisible=!0):this.$modal.msgError("请选择要审批的记录")},confirmBatchApproval:function(){var e=this;"reject"===this.batchApprovalForm.action?this.$refs["batchApprovalForm"].validate((function(t){t&&e.executeBatchApproval()})):this.executeBatchApproval()},executeBatchApproval:function(){var e=this,t={ids:this.selectedRecords.map((function(e){return e.id})),action:this.batchApprovalForm.action,rejectReason:this.batchApprovalForm.rejectReason};p(t).then((function(){e.$modal.msgSuccess("批量".concat("approve"===e.batchApprovalForm.action?"通过":"拒绝","审批成功")),e.batchApprovalDialogVisible=!1,e.loadFeeRecords(e.currentOrderInfo.id),e.getList()})).catch((function(e){console.error("批量审批失败:",e)}))},canApproveRecord:function(e){return 0===e.status||null===e.status||""===e.status},getStatusTagType:function(e){switch(e){case 0:return"info";case 1:return"success";case 7:return"danger";case 3:case 4:case 5:case 6:return"warning";default:return"info"}}}},v=b,h=(a("73b8"),a("2877")),g=Object(h["a"])(v,r,n,!1,null,"84e7fd22",null);t["default"]=g.exports},"5cd8":function(e,t,a){},"73b8":function(e,t,a){"use strict";a("5cd8")},bd52:function(e,t,a){"use strict";a.d(t,"l",(function(){return n})),a.d(t,"n",(function(){return o})),a.d(t,"h",(function(){return l})),a.d(t,"i",(function(){return i})),a.d(t,"o",(function(){return s})),a.d(t,"e",(function(){return c})),a.d(t,"s",(function(){return u})),a.d(t,"t",(function(){return p})),a.d(t,"a",(function(){return d})),a.d(t,"A",(function(){return m})),a.d(t,"g",(function(){return f})),a.d(t,"k",(function(){return b})),a.d(t,"w",(function(){return v})),a.d(t,"z",(function(){return h})),a.d(t,"f",(function(){return g})),a.d(t,"x",(function(){return _})),a.d(t,"c",(function(){return y})),a.d(t,"b",(function(){return w})),a.d(t,"v",(function(){return I})),a.d(t,"y",(function(){return x})),a.d(t,"j",(function(){return k})),a.d(t,"q",(function(){return j})),a.d(t,"B",(function(){return N})),a.d(t,"m",(function(){return S})),a.d(t,"r",(function(){return O})),a.d(t,"p",(function(){return A})),a.d(t,"d",(function(){return P})),a.d(t,"u",(function(){return q}));var r=a("b775");function n(e){return Object(r["a"])({url:"/vw_account_loan/vw_account_loan/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/vw_account_loan/vw_account_loan/listBySlippageStatus3",method:"get",params:e})}function l(e){return Object(r["a"])({url:"/vw_account_loan/vw_account_loan/byLoanId/"+e,method:"get"})}function i(){return Object(r["a"])({url:"/bank_account/bank_account/bank?pageSize=30",method:"get"})}function s(e){return Object(r["a"])({url:"/loan_compensation/loan_compensation/detail",method:"get",params:e})}function c(e){return Object(r["a"])({url:"/vw_car/vw_car/"+e,method:"get"})}function u(e){return Object(r["a"])({url:"/loan_reminder/loan_reminder/list",method:"get",params:e})}function p(e){return Object(r["a"])({url:"/loan_reminder/loan_reminder",method:"post",data:e})}function d(e){return Object(r["a"])({url:"/vw_account_loan/vw_account_loan",method:"post",data:e})}function m(e){return Object(r["a"])({url:"/vw_account_loan/vw_account_loan",method:"put",data:e})}function f(e){return Object(r["a"])({url:"/vw_account_loan/vw_account_loan/"+e,method:"delete"})}function b(e,t){return Object(r["a"])({url:"/vw_customer_info/vw_customer_info/"+e+"?applyNo="+t,method:"get"})}function v(e){return Object(r["a"])({url:"/loan_settle/loan_settle/detail",method:"get",params:e})}function h(e){return Object(r["a"])({url:"/trial_balance/trial_balance/submit",method:"post",data:e})}function g(e){return Object(r["a"])({url:"/trial_balance/trial_balance/submitdc",method:"post",data:e})}function _(e){return Object(r["a"])({url:"/loan_settle/loan_settle",method:"post",data:e})}function y(e,t){return Object(r["a"])({url:"/loan_settle/loan_settle/process",method:"post",data:{loanId:e,status:t}})}function w(e){return Object(r["a"])({url:"/loan_compensation/loan_compensation/initiate",method:"post",data:e})}function I(e){return Object(r["a"])({url:"/loan_compensation/loan_compensation",method:"put",data:e})}function x(e){return Object(r["a"])({url:"/loan_settle/loan_settle",method:"put",data:e})}function k(e){return Object(r["a"])({url:"/system/user/listByRoleId",method:"get",params:{roleId:e}})}function j(e){return Object(r["a"])({url:"/loan_list/loan_list/batchUpdateUrgeUser",method:"put",data:e})}function N(e){return Object(r["a"])({url:"/loan_list/loan_list",method:"put",data:e})}function S(e){return Object(r["a"])({url:"/vw_account_loan/vw_account_loan/extension_list",method:"get",params:e})}function O(e){return Object(r["a"])({url:"/loan_extension/loan_extension/extension_detail",method:"get",params:{loan_id:e}})}function A(e){return Object(r["a"])({url:"/loan_extension/loan_extension/approve",method:"put",data:e})}function P(e){return Object(r["a"])({url:"/loan_list/loan_list/batchAssignPetitionUser",method:"put",data:e})}function q(e){return Object(r["a"])({url:"/loan_list/loan_list/revokePetitionUser/".concat(e),method:"put"})}},d6fd:function(e,t,a){"use strict";a("1791")}}]);