(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4e60b350"],{"04d6":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{visible:e.visible,title:"日志查看",width:"800px"},on:{"update:visible":function(t){e.visible=t},close:e.handleCancel}},[a("el-steps",{staticClass:"custom-steps",staticStyle:{"margin-bottom":"24px"},attrs:{active:e.activeStep,"finish-status":"success","process-status":"process","align-center":""}},e._l(e.statusSteps,(function(e,t){return a("el-step",{key:t,attrs:{title:e}})})),1),a("el-table",{staticStyle:{width:"100%","margin-bottom":"24px"},attrs:{data:e.logList,border:""}},[a("el-table-column",{attrs:{prop:"createTime",label:"时间",width:"160"}}),a("el-table-column",{attrs:{prop:"createBy",label:"跟踪人",width:"120"}}),a("el-table-column",{attrs:{prop:"status",label:"跟踪动作",width:"120"}}),a("el-table-column",{attrs:{prop:"remark",label:"描述"}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleUrgeLog}},[e._v("催记日志")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleConfirm}},[e._v("确认")]),a("el-button",{on:{click:e.handleCancel}},[e._v("取消")])],1)],1),a("loan-reminder-log",{ref:"loanReminderLog",attrs:{"loan-id":e.reminderLogLoanId}})],1)},i=[],r=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("9a9a"),a("413c")),n=a("7954"),o={name:"LitigationLogView",components:{LoanReminderLog:n["a"]},props:{data:{type:Object,default:function(){return{}}}},watch:{data:{handler:function(e){var t=this;console.log("newVal:",e),Object(r["g"])({litigationCaseId:e.id}).then((function(e){if(t.logList=e.rows,e.rows&&e.rows.length>0){var a=e.rows[e.rows.length-1].status;t.setActiveStepByStatus(a)}}))}}},data:function(){return{visible:!1,reminderLogLoanId:"",litigationStatusTree:[{label:"立案前",value:"立案前",children:[{label:"准备资料",value:"准备资料"},{label:"已邮寄",value:"撤案已邮寄"},{label:"待立案",value:"待立案"}]},{label:"立案-判决",value:"立案-判决",children:[{label:"待出民初号",value:"待出民初号"},{label:"待开庭",value:"待开庭"},{label:"待出法院文书",value:"待出法院文书"}]},{label:"判决-执行",value:"判决-执行",children:[{label:"待出申请书",value:"待出申请书"},{label:"已提交执行书",value:"已提交执行书"}]},{label:"执行后",value:"执行后",children:[{label:"执行中",value:"执行中"},{label:"待送车",value:"待送车"},{label:"待法拍",value:"待法拍"},{label:"继续执行",value:"继续执行"},{label:"执行终本",value:"执行终本"}]},{label:"结案",value:"结案",children:[{label:"法诉减免结清",value:"法诉减免结清"},{label:"法诉全额结清",value:"法诉全额结清"}]},{label:"撤案",value:"撤案"}],activeStep:0,logList:[]}},computed:{statusSteps:function(){return this.litigationStatusTree.map((function(e){return e.label}))}},methods:{setActiveStepByStatus:function(e){if(e){var t=this.findParentIndexByStatus(e);this.activeStep=t>=0?t:0}else this.activeStep=0},findParentIndexByStatus:function(e){for(var t=0;t<this.litigationStatusTree.length;t++){var a=this.litigationStatusTree[t];if(a.label===e||a.value===e)return t;if(a.children&&a.children.length>0){var l=a.children.some((function(t){return t.label===e||t.value===e}));if(l)return t}}return-1},openDialog:function(){this.visible=!0},handleUrgeLog:function(){this.reminderLogLoanId=String(this.data.流程序号),this.$refs.loanReminderLog.openLogDialog()},handleConfirm:function(){},handleCancel:function(){this.visible=!1}}},s=o,u=(a("db56"),a("2877")),c=Object(u["a"])(s,l,i,!1,null,"22548702",null);t["default"]=c.exports},"0da6":function(e,t,a){},"0f5f":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.carInfo.plateNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车架号"}},[e._v(e._s(e.carInfo.identityNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"发动机号"}},[e._v(e._s(e.carInfo.engineNumber||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(e._s(e.carStatusLabel))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆位置"}},[e._v(e._s(e.carInfo.carAddress||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(e._s(e.gpsStatusLabel))]):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看车牌照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"行驶证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看行驶证照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"登记证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看登记证照片")])],1):e._e()],1)],1)},i=[],r=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),a("bd52")),n={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var e=this,t=this.carStatusList.find((function(t){return t.value===e.carInfo.carStatus}));return t?t.label:"未知状态"},gpsStatusLabel:function(){var e=this,t=this.gpsStatusList.find((function(t){return t.value===e.carInfo.gpsStatus}));return t?t.label:"未知状态"}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,e&&this.plateNo&&Object(r["e"])(this.plateNo).then((function(e){t.carInfo=e.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},o=n,s=a("2877"),u=Object(s["a"])(o,l,i,!1,null,null,null);t["a"]=u.exports},1791:function(e,t,a){},"2bf4":function(e,t,a){"use strict";a.d(t,"d",(function(){return i})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return n})),a.d(t,"e",(function(){return o})),a.d(t,"b",(function(){return s}));var l=a("b775");function i(e){return Object(l["a"])({url:"/car_order/car_order/list",method:"get",params:e})}function r(e){return Object(l["a"])({url:"/car_order/car_order/"+e,method:"get"})}function n(e){return Object(l["a"])({url:"/car_order/car_order",method:"post",data:e})}function o(e){return Object(l["a"])({url:"/car_order/car_order",method:"put",data:e})}function s(e){return Object(l["a"])({url:"/car_order/car_order/"+e,method:"delete"})}},"2eca":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[e._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},i=[],r=a("bd52"),n={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(r["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},o=n,s=(a("d6fd"),a("2877")),u=Object(s["a"])(o,l,i,!1,null,"8a3d4978",null);t["a"]=u.exports},3806:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.visible,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.visible=t},close:e.resetForm}},[a("el-form",{ref:"form",attrs:{model:e.loanReminder,"label-width":"120px"}},[a("el-divider",{attrs:{"content-position":"left"}},[a("i",{staticClass:"el-icon-document"}),e._v(" 贷款信息 ")]),a("el-descriptions",{attrs:{title:"",column:3,border:""}},[a("el-descriptions-item",{attrs:{label:"贷款人"}},[e._v(" "+e._s(e.loanReminder.customerName)+" ")]),a("el-descriptions-item",{attrs:{label:"出单渠道"}},[e._v(" "+e._s(e.loanReminder.channel)+" ")]),a("el-descriptions-item",{attrs:{label:"放款银行"}},[e._v(" "+e._s(e.loanReminder.bank)+" ")])],1),a("el-divider",{attrs:{"content-position":"left"}},[a("i",{staticClass:"el-icon-document"}),e._v(" 文书信息 ")]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"变更法诉状态"}},[a("litigation-status",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择法诉状态"},model:{value:e.litigationLog.status,callback:function(t){e.$set(e.litigationLog,"status",t)},expression:"litigationLog.status"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"文书名称"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择文书名称"},model:{value:e.litigationLog.docName,callback:function(t){e.$set(e.litigationLog,"docName",t)},expression:"litigationLog.docName"}},[a("el-option",{attrs:{label:"诉前调号",value:"诉前调号"}}),a("el-option",{attrs:{label:"民初号",value:"民初号"}}),a("el-option",{attrs:{label:"执行号",value:"执行号"}}),a("el-option",{attrs:{label:"执保号",value:"执保号"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"文书号"}},[a("el-input",{attrs:{placeholder:"请输入文书号"},model:{value:e.litigationLog.docNumber,callback:function(t){e.$set(e.litigationLog,"docNumber",t)},expression:"litigationLog.docNumber"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"文书生效"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期"},model:{value:e.litigationLog.docEffectiveDate,callback:function(t){e.$set(e.litigationLog,"docEffectiveDate",t)},expression:"litigationLog.docEffectiveDate"}})],1)],1),"待出法院文书"===e.litigationLog.status?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"登记开庭时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"选择开庭时间"},model:{value:e.litigationLog.openDate,callback:function(t){e.$set(e.litigationLog,"openDate",t)},expression:"litigationLog.openDate"}})],1)],1):e._e(),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"上传文书"}},[a("el-upload",{attrs:{data:e.data,action:e.uploadUrl,headers:e.headers,limit:1,"file-list":e.litigationLog.docUploadUrl,"on-success":function(t,a,l){return e.handleUploadSuccess(t,a,l,"litigationLog.docUploadUrl")},"on-remove":function(t,a){return e.handleRemove(t,a,"litigationLog.docUploadUrl")},"on-error":e.handleUploadError}},[a("el-button",{attrs:{size:"small",type:"primary",disabled:e.litigationLog.docUploadUrl.length>=1}},[e._v("点击上传")])],1)],1)],1)],1),a("el-divider",{attrs:{"content-position":"left"}},[a("i",{staticClass:"el-icon-money"}),e._v(" 还款信息 ")]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"还款类型"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择还款类型"},model:{value:e.loanReminder.repaymentStatus,callback:function(t){e.$set(e.loanReminder,"repaymentStatus",t)},expression:"loanReminder.repaymentStatus"}},[a("el-option",{attrs:{label:"部分还款",value:"2"}}),a("el-option",{attrs:{label:"分期还款",value:"3"}}),a("el-option",{attrs:{label:"协商买车",value:"4"}}),a("el-option",{attrs:{label:"法诉结清",value:"5"}}),a("el-option",{attrs:{label:"法诉减免结清",value:"6"}}),a("el-option",{attrs:{label:"拍卖回款",value:"7"}}),a("el-option",{attrs:{label:"法院划扣",value:"8"}}),a("el-option",{attrs:{label:"其他分配回款",value:"9"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"款项明细类型"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择款项明细类型"},model:{value:e.loanReminder.fundsRepayment,callback:function(t){e.$set(e.loanReminder,"fundsRepayment",t)},expression:"loanReminder.fundsRepayment"}},[a("el-option",{attrs:{label:"律师费",value:"律师费"}}),a("el-option",{attrs:{label:"法诉费",value:"法诉费"}}),a("el-option",{attrs:{label:"保全费",value:"保全费"}}),a("el-option",{attrs:{label:"布控费",value:"布控费"}}),a("el-option",{attrs:{label:"公告费",value:"公告费"}}),a("el-option",{attrs:{label:"评估费",value:"评估费"}}),a("el-option",{attrs:{label:"执行费",value:"执行费"}}),a("el-option",{attrs:{label:"违约金",value:"违约金"}}),a("el-option",{attrs:{label:"担保费",value:"担保费"}}),a("el-option",{attrs:{label:"居间费",value:"居间费"}}),a("el-option",{attrs:{label:"代偿金",value:"代偿金"}}),a("el-option",{attrs:{label:"判决金额",value:"判决金额"}}),a("el-option",{attrs:{label:"利息",value:"利息"}}),a("el-option",{attrs:{label:"其他欠款",value:"其他欠款"}}),a("el-option",{attrs:{label:"保险费",value:"保险费"}})],1)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"金额"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,precision:2,placeholder:"请输入金额"},model:{value:e.loanReminder.fundsAmount,callback:function(t){e.$set(e.loanReminder,"fundsAmount",t)},expression:"loanReminder.fundsAmount"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择账号类型"},model:{value:e.loanReminder.fundsAccountType,callback:function(t){e.$set(e.loanReminder,"fundsAccountType",t)},expression:"loanReminder.fundsAccountType"}},[a("el-option",{attrs:{label:"银行账户",value:"银行账户"}}),a("el-option",{attrs:{label:"微信",value:"微信"}}),a("el-option",{attrs:{label:"支付宝",value:"支付宝"}}),a("el-option",{attrs:{label:"其他",value:"其他"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号"}},[a("el-input",{attrs:{disabled:"其他"!==e.loanReminder.fundsAccountType,placeholder:"请输入账号"},model:{value:e.loanReminder.accountNumber,callback:function(t){e.$set(e.loanReminder,"accountNumber",t)},expression:"loanReminder.accountNumber"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"还款凭据"}},[a("el-upload",{attrs:{data:e.data,action:e.uploadUrl,headers:e.headers,"list-type":"picture-card","file-list":e.loanReminder.fundsImage,"on-preview":e.handlePictureCardPreview,"on-success":function(t,a,l){return e.handleUploadSuccess(t,a,l,"loanReminder.fundsImage")},"on-remove":function(t,a){return e.handleRemove(t,a,"loanReminder.fundsImage")},"on-error":e.handleUploadError}},[a("i",{staticClass:"el-icon-plus"})])],1)],1)],1),a("el-divider",{attrs:{"content-position":"left"}},[a("i",{staticClass:"el-icon-notebook-2"}),e._v(" 日志信息 ")]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"日志类型"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择日志类型"},model:{value:e.loanReminder.urgeStatus,callback:function(t){e.$set(e.loanReminder,"urgeStatus",t)},expression:"loanReminder.urgeStatus"}},[a("el-option",{attrs:{label:"继续跟踪",value:"1"}}),a("el-option",{attrs:{label:"约定还款",value:"2"}}),a("el-option",{attrs:{label:"无法跟进",value:"3"}}),a("el-option",{attrs:{label:"暂时无需跟进",value:"4"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"下次跟进时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"选择跟进时间"},model:{value:e.loanReminder.trackingTime,callback:function(t){e.$set(e.loanReminder,"trackingTime",t)},expression:"loanReminder.trackingTime"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"日志描述"}},[a("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入日志描述",maxlength:"500","show-word-limit":""},model:{value:e.loanReminder.urgeDescribe,callback:function(t){e.$set(e.loanReminder,"urgeDescribe",t)},expression:"loanReminder.urgeDescribe"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.cancel}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")])],1),a("el-dialog",{attrs:{visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})])],1)},i=[],r=a("3835"),n=(a("a15b"),a("d81d"),a("e9c4"),a("b64b"),a("d3b7"),a("0643"),a("a573"),a("6af6")),o=a("5f87"),s=a("413c"),u={name:"LitigationLogForm",components:{litigationStatus:n["a"]},props:{action:{type:String,default:"/common/ossupload"},data:{type:Object,default:function(){}}},data:function(){return{title:"提交法诉日志",visible:!1,loanReminder:{},litigationLog:{},uploadUrl:"/prod-api"+this.action,headers:{Authorization:"Bearer "+Object(o["a"])()},dialogImageUrl:"",dialogVisible:!1}},watch:{data:{handler:function(e){e&&(console.log("newVal",e),this.loanReminder={loanId:e.流程序号,customerName:e.贷款人,channel:e.出单渠道,bank:e.放款银行,identity:this.$store.state.user.roles[0],repaymentStatus:"",fundsRepayment:"",fundsAmount:"",fundsImage:[],fundsAccountType:"",accountNumber:"",urgeStatus:"",trackingTime:"",urgeDescribe:"",status:2},this.litigationLog={loanId:e.流程序号,litigationId:e.序号,docName:"",docNumber:"",docUploadUrl:[],docEffectiveDate:"",openDate:"",status:""})},immediate:!0,deep:!0}},methods:{handleUploadSuccess:function(e,t,a,l){var i=l.split("."),n=Object(r["a"])(i,2),o=n[0],s=n[1];this[o][s]=a},handleRemove:function(e,t,a){var l=a.split("."),i=Object(r["a"])(l,2),n=i[0],o=i[1];this[n][o]=t},handleUploadError:function(){this.$modal.msgError("上传失败，请重试"),this.$modal.closeLoading()},handlePictureCardPreview:function(e){this.dialogImageUrl=e.url,this.dialogVisible=!0},submitForm:function(){var e=this,t=JSON.parse(JSON.stringify(this.loanReminder)),a=JSON.parse(JSON.stringify(this.litigationLog));t.fundsImage=t.fundsImage.map((function(e){return e.response})).join(","),a.docUploadUrl=a.docUploadUrl.map((function(e){return e.response})).join(","),t.fundsAccountType="其他"===t.fundsAccountType?t.accountNumber:t.fundsAccountType,console.log("提交表单数据：",this.loanReminder),console.log("提交表单数据：",this.litigationLog),Object(s["h"])({loanReminder:t,litigationLog:a}).then((function(t){e.$modal.msgSuccess("提交成功"),e.visible=!1,e.resetForm()}))},cancel:function(){this.visible=!1,this.resetForm()},resetForm:function(){this.loanReminder={fundsImage:[]},this.litigationLog={docUploadUrl:[]}},openDialog:function(){this.visible=!0},handleExceed:function(e,t){this.$message.warning("只能上传一个文件")}}},c=u,d=(a("b36a"),a("2877")),m=Object(d["a"])(c,l,i,!1,null,"073703b3",null);t["default"]=m.exports},"3d7b":function(e,t,a){},"62ee":function(e,t,a){"use strict";a("0da6")},6634:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticClass:"dialogBox",attrs:{title:"发起找车",visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-descriptions",{attrs:{column:2,border:""}},[a("el-descriptions-item",{attrs:{label:"贷款人"}},[e._v(" "+e._s(e.formData.customerName||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"联系电话"}},[e._v(" "+e._s(e.formData.mobilePhone||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"出单渠道"}},[e._v(" "+e._s(e.formData.jgName||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"业务员"}},[e._v(" "+e._s(e.formData.mangerId||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"车辆牌号"}},[e._v(" "+e._s(e.formData.plateNo||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(" "+e._s(e.formData.carStatus||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"车辆位置",span:2}},[e._v(" "+e._s(e.formData.carDetailAddress||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(" "+e._s(e.formData.gpsStatus||"-")+" ")])],1),a("el-form",{ref:"teamForm",staticStyle:{"margin-top":"30px"},attrs:{model:e.form,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"指定派车团队",prop:"teamId",rules:[{required:!0,message:"请选择派车团队",trigger:"change"}]}},[a("el-select",{staticStyle:{width:"40%","margin-left":"16px"},attrs:{placeholder:"请选择派车团队",clearable:""},model:{value:e.form.teamId,callback:function(t){e.$set(e.form,"teamId",t)},expression:"form.teamId"}},e._l(e.car_team,(function(e){return a("el-option",{key:e.id,attrs:{label:e.teamName,value:e.id}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.handleClose}},[e._v("取 消")])],1)],1)},i=[],r=a("a1e7"),n=a("bd52"),o=a("2bf4"),s={name:"DispatchVehicleForm",props:{loanId:{type:String,default:""}},data:function(){return{dialogVisible:!1,submitLoading:!1,formData:{},form:{teamId:null},car_team:[]}},watch:{loanId:{handler:function(e){var t=this;e&&Object(n["h"])(e).then((function(e){t.formData=e.data}))},deep:!0,immediate:!0}},created:function(){this.getCarTeam()},methods:{openDialog:function(){this.dialogVisible=!0,this.resetForm()},handleClose:function(){this.dialogVisible=!1,this.resetForm()},resetForm:function(){var e=this;this.form={teamId:null},this.$nextTick((function(){e.$refs.teamForm&&e.$refs.teamForm.clearValidate()}))},getCarTeam:function(){var e=this;Object(r["d"])({status:1}).then((function(t){e.car_team=t.rows||[]})).catch((function(){e.car_team=[]}))},submitForm:function(){var e=this;this.form.teamId?(this.form.loanId=this.formData.loanId,this.form.applyNo=this.formData.applyId,Object(o["a"])(this.form).then((function(t){200===t.code?(e.$message.success("提交成功"),e.dialogVisible=!1,e.resetForm()):e.$message.error("提交失败")}))):this.$message.error("请选择派车团队")}}},u=s,c=a("2877"),d=Object(c["a"])(u,l,i,!1,null,null,null);t["a"]=d.exports},"6af6":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-cascader",{attrs:{options:e.litigationStatusTree,props:e.cascaderProps,placeholder:e.placeholder,clearable:""},on:{change:e.handleChange},model:{value:e.proxyValue,callback:function(t){e.proxyValue=t},expression:"proxyValue"}})},i=[],r={name:"LitigationStatusCascader",props:{value:{type:[String,Array],default:""},placeholder:{type:String,default:"法诉状态"}},data:function(){return{litigationStatusTree:[{label:"立案前",value:"立案前",children:[{label:"准备资料",value:"准备资料"},{label:"已邮寄",value:"撤案已邮寄"},{label:"待立案",value:"待立案"}]},{label:"立案-判决",value:"立案-判决",children:[{label:"待出民初号",value:"待出民初号"},{label:"待开庭",value:"待开庭"},{label:"待出法院文书",value:"待出法院文书"}]},{label:"判决-执行",value:"判决-执行",children:[{label:"待出申请书",value:"待出申请书"},{label:"已提交执行书",value:"已提交执行书"}]},{label:"执行后",value:"执行后",children:[{label:"执行中",value:"执行中"},{label:"待送车",value:"待送车"},{label:"待法拍",value:"待法拍"},{label:"继续执行",value:"继续执行"},{label:"执行终本",value:"执行终本"}]},{label:"结案",value:"结案",children:[{label:"法诉减免结清",value:"法诉减免结清"},{label:"法诉全额结清",value:"法诉全额结清"}]},{label:"撤案",value:"撤案"}],cascaderProps:{emitPath:!1,value:"value",label:"label",children:"children",disabled:function(e){return!!e.children&&e.children.length>0}}}},computed:{proxyValue:{get:function(){return this.value},set:function(e){this.$emit("input",e),this.$emit("update:value",e)}}},methods:{handleChange:function(e){this.$listeners&&this.$listeners.change&&this.$emit("change",e)}}},n=r,o=a("2877"),s=Object(o["a"])(n,l,i,!1,null,null,null);t["a"]=s.exports},a1e7:function(e,t,a){"use strict";a.d(t,"d",(function(){return i})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return n})),a.d(t,"e",(function(){return o})),a.d(t,"b",(function(){return s}));var l=a("b775");function i(e){return Object(l["a"])({url:"/car_team/car_team/list",method:"get",params:e})}function r(e){return Object(l["a"])({url:"/car_team/car_team/"+e,method:"get"})}function n(e){return Object(l["a"])({url:"/car_team/car_team",method:"post",data:e})}function o(e){return Object(l["a"])({url:"/car_team/car_team",method:"put",data:e})}function s(e){return Object(l["a"])({url:"/car_team/car_team/"+e,method:"delete"})}},af66:function(e,t,a){"use strict";a("e60a")},b36a:function(e,t,a){"use strict";a("3d7b")},d690:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("litigation-form",{ref:"litigationForm",attrs:{data:e.currentRow}}),a("litigation-fee-form",{ref:"litigationFeeForm",attrs:{data:e.currentRow}}),a("litigation-log-form",{ref:"litigationLogForm",attrs:{data:e.currentRow}}),a("litigation-log-view",{ref:"litigationLogView",attrs:{data:e.currentRow}}),a("dispatch-vehicle-form",{ref:"dispatchVehicleForm",attrs:{loanId:e.dispatchLoanId}}),a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"certId"}},[a("el-input",{attrs:{placeholder:"贷款人身份证号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.certId,callback:function(t){e.$set(e.queryParams,"certId",t)},expression:"queryParams.certId"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"车牌号码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"carStatus"}},[a("el-select",{attrs:{placeholder:"车辆状态",clearable:""},model:{value:e.queryParams.carStatus,callback:function(t){e.$set(e.queryParams,"carStatus",t)},expression:"queryParams.carStatus"}},e._l(e.carStatusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"jgName"}},[a("el-input",{attrs:{placeholder:"录单渠道名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jgName,callback:function(t){e.$set(e.queryParams,"jgName",t)},expression:"queryParams.jgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"litigationClerk"}},[a("el-input",{attrs:{placeholder:"法诉文员",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.litigationClerk,callback:function(t){e.$set(e.queryParams,"litigationClerk",t)},expression:"queryParams.litigationClerk"}})],1),e.showMore?[a("el-form-item",{attrs:{label:"",prop:"caseOwner"}},[a("el-input",{attrs:{placeholder:"案件负责人",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.caseOwner,callback:function(t){e.$set(e.queryParams,"caseOwner",t)},expression:"queryParams.caseOwner"}})],1),a("el-form-item",{attrs:{label:"",prop:"litigationStatus"}},[a("litigation-status",{attrs:{placeholder:"法诉状态"},model:{value:e.queryParams.litigationStatus,callback:function(t){e.$set(e.queryParams,"litigationStatus",t)},expression:"queryParams.litigationStatus"}})],1),a("el-form-item",{attrs:{label:"",prop:"logType"}},[a("el-select",{attrs:{placeholder:"日志类型",clearable:""},model:{value:e.queryParams.logType,callback:function(t){e.$set(e.queryParams,"logType",t)},expression:"queryParams.logType"}},e._l(e.logTypeList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"lawsuitCourt"}},[a("el-select",{attrs:{placeholder:"诉讼法院",clearable:""},model:{value:e.queryParams.lawsuitCourt,callback:function(t){e.$set(e.queryParams,"lawsuitCourt",t)},expression:"queryParams.lawsuitCourt"}},e._l(e.lawsuitCourtList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)]:e._e(),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")]),a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(t){e.showMore=!e.showMore}}},[e._v(" "+e._s(e.showMore?"收起":"更多")+" "),a("i",{class:e.showMore?"el-icon-arrow-up":"el-icon-arrow-down"})])],1)],2),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.litigation_caseList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"法诉文员",align:"center",prop:"法诉文员",width:"100"}}),a("el-table-column",{attrs:{label:"发起法诉日",align:"center",prop:"发起法诉日",width:"110"}}),a("el-table-column",{attrs:{label:"案件启动日",align:"center",prop:"案件启动日",width:"110"}}),a("el-table-column",{attrs:{label:"日志类型",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getUrgeStatusText(t.row.日志类型))+" ")]}}])}),a("el-table-column",{attrs:{label:"日志更新日",align:"center",prop:"日志更新日",width:"110"}}),a("el-table-column",{attrs:{label:"法诉状态",align:"center",prop:"法诉子状态",width:"100"}}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"贷款人",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openUserInfo({customerId:t.row.客户ID,applyId:t.row.申请编号})}}},[e._v(" "+e._s(t.row.贷款人)+" ")])]}}])}),a("el-table-column",{attrs:{label:"身份证",align:"center",prop:"身份证",width:"150"}}),a("el-table-column",{attrs:{label:"车辆牌号",align:"center",prop:"车辆牌号",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.checkCar(t.row.车辆牌号)}}},[e._v(e._s(t.row.车辆牌号))])]}}])}),a("el-table-column",{attrs:{label:"车辆状态",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getCarStatusText(t.row.车辆状态))+" ")]}}])}),a("el-table-column",{attrs:{label:"找车团队",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.找车团队||"未派单")+" ")]}}])}),a("el-table-column",{attrs:{label:"地区",align:"center",prop:"地区",width:"100"}}),a("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"出单渠道",width:"120"}}),a("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"放款银行",width:"120"}}),a("el-table-column",{attrs:{label:"托管类型",align:"center",prop:"托管类型",width:"100"}}),a("el-table-column",{attrs:{label:"欠款余额",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[a("div",[e._v(e._s(e.formatMoney(t.row.剩余金额)))]),a("el-button",{staticStyle:{color:"#409EFF","font-size":"12px"},attrs:{type:"text",size:"mini"},on:{click:function(a){return e.showDebtDetail(t.row)}}},[e._v(" 详情 ")])],1)]}}])}),a("el-table-column",{attrs:{label:"未起诉金额",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[a("div",[e._v(e._s(e.formatMoney(e.calculateUnsuedAmount(t.row))))]),a("el-button",{staticStyle:{color:"#409EFF","font-size":"12px"},attrs:{type:"text",size:"mini"},on:{click:function(a){return e.showUnsuedContentDetail(t.row)}}},[e._v(" 详情 ")])],1)]}}])}),a("el-table-column",{attrs:{label:"起诉金额",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatMoney(t.row.起诉金额))+" ")]}}])}),a("el-table-column",{attrs:{label:"起诉类型",align:"center",prop:"起诉类型",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getLawsuitTypeText(t.row.起诉类型))+" ")]}}])}),a("el-table-column",{attrs:{label:"起诉内容",align:"center",prop:"起诉内容",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getLawsuitContentText(t.row.起诉内容))+" ")]}}])}),a("el-table-column",{attrs:{label:"判决金额",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatMoney(e.getLitigationFeeAmount(t.row.序号,"judgmentAmount")))+" ")]}}])}),a("el-table-column",{attrs:{label:"利息",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatMoney(e.getLitigationFeeAmount(t.row.序号,"interest")))+" ")]}}])}),a("el-table-column",{attrs:{label:"诉讼费",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[a("div",[e._v(e._s(e.formatMoney(e.getLitigationFeeAmount(t.row.序号,"litigation"))))]),a("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text",size:"mini"},on:{click:function(a){return e.viewLitigationFeeDetails(t.row.序号,"litigation")}}},[e._v(" 详情 ")])],1)]}}])}),a("el-table-column",{attrs:{label:"待追偿欠款",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[a("div",[e._v(e._s(e.formatMoney(e.calculateTotalDebt(t.row))))]),a("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text",size:"mini"},on:{click:function(a){return e.viewTotalDebtDetails(t.row)}}},[e._v(" 详情 ")])],1)]}}])}),a("el-table-column",{attrs:{label:"代偿证明发出日",align:"center",prop:"代偿证明发出日",width:"140"}}),a("el-table-column",{attrs:{label:"法院地",align:"center",prop:"法院地",width:"100"}}),a("el-table-column",{attrs:{label:"诉讼法院",align:"center",prop:"诉讼法院",width:"120"}}),a("el-table-column",{attrs:{label:"案件负责人",align:"center",prop:"案件负责人",width:"100"}}),a("el-table-column",{attrs:{label:"诉前调号出具时间",align:"center",prop:"诉前调号出具时间",width:"150"}}),a("el-table-column",{attrs:{label:"诉前调号",align:"center",prop:"诉前调号",width:"120"}}),a("el-table-column",{attrs:{label:"民初号出具时间",align:"center",prop:"民初号出具时间",width:"140"}}),a("el-table-column",{attrs:{label:"民初号",align:"center",prop:"民初号",width:"120"}}),a("el-table-column",{attrs:{label:"开庭时间",align:"center",prop:"开庭时间",width:"110"}}),a("el-table-column",{attrs:{label:"申请执行时间",align:"center",prop:"申请执行时间",width:"130"}}),a("el-table-column",{attrs:{label:"执行号/执保号",align:"center",prop:"执行号/执保号",width:"140"}}),a("el-table-column",{attrs:{label:"车辆出库时间",align:"center",prop:"车辆出库时间",width:"130"}}),a("el-table-column",{attrs:{label:"法拍时间",align:"center",prop:"法拍时间",width:"110"}}),a("el-table-column",{attrs:{label:"车辆评估价",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatMoney(t.row.车辆评估价))+" ")]}}])}),a("el-table-column",{attrs:{label:"拍卖金额",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatMoney(t.row.拍卖金额))+" ")]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center",fixed:"right","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-popover",{attrs:{placement:"left",trigger:"click","popper-class":"custom-popover"}},[a("div",{staticClass:"operation-buttons"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vm_car_order:vm_car_order:edit"],expression:"['vm_car_order:vm_car_order:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.openLitigationLogView(t.row)}}},[e._v(" 查看日志 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vm_car_order:vm_car_order:remove"],expression:"['vm_car_order:vm_car_order:remove']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.openLitigationForm(t.row)}}},[e._v(" 启动法诉 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vm_car_order:vm_car_order:edit"],expression:"['vm_car_order:vm_car_order:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.openLitigationLogForm(t.row)}}},[e._v(" 提交日志 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vm_car_order:vm_car_order:edit"],expression:"['vm_car_order:vm_car_order:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.openLitigationFeeForm(t.row)}}},[e._v(" 法诉费用 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vm_car_order:vm_car_order:edit"],expression:"['vm_car_order:vm_car_order:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.openDispatchVehicleForm(t.row)}}},[e._v(" 派单找车 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vm_car_order:vm_car_order:edit"],expression:"['vm_car_order:vm_car_order:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.openDailyExpenseDialog(t.row)}}},[e._v(" 日常费用 ")])],1),a("el-button",{attrs:{slot:"reference",size:"mini",type:"text"},slot:"reference"},[e._v("更多")])],1)]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("userInfo",{ref:"userInfo",attrs:{visible:e.userInfoVisible,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.userInfoVisible=t}}}),a("car-info",{ref:"carInfo",attrs:{visible:e.carShow,title:"车辆信息",plateNo:e.plateNo,permission:"2"},on:{"update:visible":function(t){e.carShow=t}}}),a("el-dialog",{attrs:{title:"欠款详情",visible:e.debtDetailVisible,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.debtDetailVisible=t}}},[e.currentDebtRow?a("div",{staticStyle:{padding:"10px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("h4",{staticStyle:{"margin-bottom":"15px",color:"#303133","border-bottom":"1px solid #e4e7ed","padding-bottom":"10px"}},[e._v("银行代偿")]),a("div",{staticClass:"debt-item"},[a("span",{staticClass:"debt-label"},[e._v("银行代偿金额:")]),a("span",{staticClass:"debt-value"},[e._v(e._s(e.formatMoney(e.currentDebtRow.银行代偿金额)))])]),a("div",{staticClass:"debt-item"},[a("span",{staticClass:"debt-label"},[e._v("银行催回金额:")]),a("span",{staticClass:"debt-value"},[e._v(e._s(e.formatMoney(e.currentDebtRow.银行催回金额)))])]),a("div",{staticClass:"debt-item"},[a("span",{staticClass:"debt-label"},[e._v("银行剩余未还:")]),a("span",{staticClass:"debt-value debt-remaining"},[e._v(e._s(e.formatMoney(e.currentDebtRow.银行剩余未还代偿金)))])])]),a("el-col",{attrs:{span:12}},[a("h4",{staticStyle:{"margin-bottom":"15px",color:"#303133","border-bottom":"1px solid #e4e7ed","padding-bottom":"10px"}},[e._v("代扣金额")]),a("div",{staticClass:"debt-item"},[a("span",{staticClass:"debt-label"},[e._v("代扣金额:")]),a("span",{staticClass:"debt-value"},[e._v(e._s(e.formatMoney(e.currentDebtRow.代扣金额)))])]),a("div",{staticClass:"debt-item"},[a("span",{staticClass:"debt-label"},[e._v("代扣催回金额:")]),a("span",{staticClass:"debt-value"},[e._v(e._s(e.formatMoney(e.currentDebtRow.代扣催回金额)))])]),a("div",{staticClass:"debt-item"},[a("span",{staticClass:"debt-label"},[e._v("代扣剩余未还:")]),a("span",{staticClass:"debt-value debt-remaining"},[e._v(e._s(e.formatMoney(e.currentDebtRow.代扣剩余未还代偿金)))])])])],1),a("el-row",{staticStyle:{"margin-top":"20px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("h4",{staticStyle:{"margin-bottom":"15px",color:"#303133","border-bottom":"1px solid #e4e7ed","padding-bottom":"10px"}},[e._v("违约金")]),a("div",{staticClass:"debt-item"},[a("span",{staticClass:"debt-label"},[e._v("违约金:")]),a("span",{staticClass:"debt-value"},[e._v(e._s(e.formatMoney(e.currentDebtRow.违约金)))])]),a("div",{staticClass:"debt-item"},[a("span",{staticClass:"debt-label"},[e._v("催回违约金:")]),a("span",{staticClass:"debt-value"},[e._v(e._s(e.formatMoney(e.currentDebtRow.催回违约金金额)))])]),a("div",{staticClass:"debt-item"},[a("span",{staticClass:"debt-label"},[e._v("剩余未还违约金:")]),a("span",{staticClass:"debt-value debt-remaining"},[e._v(e._s(e.formatMoney(e.currentDebtRow.剩余未还违约金金额)))])])]),a("el-col",{attrs:{span:12}},[a("h4",{staticStyle:{"margin-bottom":"15px",color:"#303133","border-bottom":"1px solid #e4e7ed","padding-bottom":"10px"}},[e._v("其他欠款")]),a("div",{staticClass:"debt-item"},[a("span",{staticClass:"debt-label"},[e._v("其他欠款:")]),a("span",{staticClass:"debt-value"},[e._v(e._s(e.formatMoney(e.currentDebtRow.其他欠款)))])]),a("div",{staticClass:"debt-item"},[a("span",{staticClass:"debt-label"},[e._v("催回其他欠款:")]),a("span",{staticClass:"debt-value"},[e._v(e._s(e.formatMoney(e.currentDebtRow.催回其他欠款)))])]),a("div",{staticClass:"debt-item"},[a("span",{staticClass:"debt-label"},[e._v("剩余未还其他欠款:")]),a("span",{staticClass:"debt-value debt-remaining"},[e._v(e._s(e.formatMoney(e.currentDebtRow.剩余未还其他欠款)))])])])],1),a("div",{staticStyle:{"margin-top":"20px","padding-top":"15px","border-top":"2px solid #409EFF","background-color":"#f5f7fa",padding:"15px","border-radius":"4px"}},[a("h4",{staticStyle:{"margin-bottom":"15px",color:"#409EFF"}},[e._v("汇总信息")]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("div",{staticClass:"debt-item total-debt"},[a("span",{staticClass:"debt-label"},[e._v("总欠款金额:")]),a("span",{staticClass:"debt-value"},[e._v(e._s(e.formatMoney(e.currentDebtRow.总欠款金额)))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"debt-item total-debt"},[a("span",{staticClass:"debt-label"},[e._v("已还金额:")]),a("span",{staticClass:"debt-value"},[e._v(e._s(e.formatMoney(e.currentDebtRow.已还金额)))])])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"debt-item total-debt"},[a("span",{staticClass:"debt-label"},[e._v("剩余金额:")]),a("span",{staticClass:"debt-value debt-remaining"},[e._v(e._s(e.formatMoney(e.currentDebtRow.剩余金额)))])])])],1)],1)],1):e._e()])],1)},i=[],r=a("c14f"),n=a("2909"),o=a("1da1"),s=(a("99af"),a("4de4"),a("7db0"),a("caad"),a("a15b"),a("d81d"),a("14d9"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("2532"),a("3ca3"),a("0643"),a("2382"),a("fffc"),a("4e3e"),a("a573"),a("159b"),a("ddb0"),a("413c")),u=a("6af6"),c=a("cfa5"),d=a("0bef"),m=a("3806"),p=a("04d6"),b=a("2eca"),f=a("0f5f"),v=a("6634"),g={name:"Litigation",components:{litigationStatus:u["a"],litigationForm:c["default"],litigationFeeForm:d["default"],litigationLogForm:m["default"],litigationLogView:p["default"],userInfo:b["a"],carInfo:f["a"],dispatchVehicleForm:v["a"]},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,litigation_caseList:[],litigationCostSummary:{},title:"",open:!1,queryParams:{pageNum:1,pageSize:10,customerName:"",certId:"",plateNo:"",carStatus:"",jgName:"",caseOwner:"",litigationStatus:"",logType:"",lawsuitCourt:""},form:{},rules:{},currentRow:{},showMore:!1,userInfoVisible:!1,customerInfo:{customerId:"",applyId:""},carShow:!1,plateNo:"",debtDetailVisible:!1,currentDebtRow:null,carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],litigationStatusList:[{label:"待立案",value:"1"},{label:"已立案",value:"2"},{label:"开庭",value:"3"},{label:"判决",value:"4"},{label:"结案",value:"5"}],logTypeList:[{label:"电话",value:"1"},{label:"短信",value:"2"},{label:"上门",value:"3"},{label:"邮件",value:"4"},{label:"其他",value:"5"}],lawsuitCourtList:[{label:"法院A",value:"A"},{label:"法院B",value:"B"},{label:"法院C",value:"C"}],lawsuitTypeList:[{label:"债转",value:"1"},{label:"债加",value:"2"},{label:"担保物权",value:"3"},{label:"仲裁",value:"4"},{label:"赋强公证",value:"5"},{label:"拍状元",value:"6"},{label:"拍司令",value:"7"},{label:"属地诉讼",value:"8"},{label:"余值起诉",value:"9"},{label:"债权出售",value:"10"},{label:"签约地诉讼",value:"11"},{label:"特殊诉讼通道",value:"12"}],lawsuitContentList:[{label:"银行代偿金额",value:"1"},{label:"代扣金额",value:"2"},{label:"违约金",value:"3"},{label:"其他欠款",value:"4"}],dispatchLoanId:""}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(s["f"])(this.queryParams).then((function(t){e.litigation_caseList=t.rows,e.total=t.total,e.loading=!1,e.loadLitigationCostSummary()}))},loadLitigationCostSummary:function(){var e=this,t=this.litigation_caseList.map((function(e){var t=e.序号;return"string"===typeof t?parseInt(t):Number(t)})).filter((function(e){return e&&!isNaN(e)}));0!==t.length&&(console.log("发送的案件ID列表:",t),Object(s["e"])(t).then((function(t){200===t.code?(e.litigationCostSummary=t.data||{},console.log("获取到的费用汇总数据:",e.litigationCostSummary)):(console.error("获取法诉费用汇总失败:",t.msg),e.litigationCostSummary={})})).catch((function(t){console.error("获取法诉费用汇总失败:",t),e.litigationCostSummary={}})))},getLitigationFeeAmount:function(e,t){var a="string"===typeof e?parseInt(e):Number(e),l=this.litigationCostSummary[a];if(!l)return 0;switch(t){case"judgmentAmount":return Number(l.judgmentAmount||0);case"interest":return Number(l.interest||0);case"litigation":return Number(l.lawyerFee||0)+Number(l.litigationFee||0)+Number(l.preservationFee||0)+Number(l.surveillanceFee||0)+Number(l.announcementFee||0)+Number(l.appraisalFee||0)+Number(l.executionFee||0)+Number(l.penalty||0)+Number(l.guaranteeFee||0)+Number(l.intermediaryFee||0)+Number(l.compensity||0)+Number(l.otherAmountsOwed||0)+Number(l.insurance||0);default:return 0}},viewLitigationFeeDetails:function(e,t){var a="string"===typeof e?parseInt(e):Number(e);if("litigation"===t){var l="诉讼费详情",i=this.formatLitigationFeeDetail(a,["lawyerFee","litigationFee","preservationFee","surveillanceFee","announcementFee","appraisalFee","executionFee","penalty","guaranteeFee","intermediaryFee","compensity","otherAmountsOwed","insurance"]);this.$alert(i,l,{dangerouslyUseHTMLString:!0,confirmButtonText:"确定"})}},formatLitigationFeeDetail:function(e,t){var a=this,l=this.litigationCostSummary[e];if(!l)return"<p>暂无费用数据</p>";var i={judgmentAmount:"判决金额",interest:"利息",lawyerFee:"律师费",litigationFee:"法诉费",preservationFee:"保全费",surveillanceFee:"布控费",announcementFee:"公告费",appraisalFee:"评估费",executionFee:"执行费",penalty:"违约金",guaranteeFee:"担保费",intermediaryFee:"居间费",compensity:"代偿金",otherAmountsOwed:"其他欠款",insurance:"保险费"},r='<div style="text-align: left;">',n=0;return t.forEach((function(e){var t=Number(l[e]||0);t>0&&(r+="<p>".concat(i[e],": ￥").concat(a.formatMoney(t),"</p>"),n+=t)})),t.length>1&&n>0&&(r+="<hr><p><strong>合计: ￥".concat(this.formatMoney(n),"</strong></p>")),r+="</div>",r||"<p>暂无费用数据</p>"},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams={},this.getList()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},openLitigationForm:function(e){this.currentRow=e||{},this.$refs.litigationForm.open&&this.$refs.litigationForm.open()},onLitigationFormConfirm:function(){},openLitigationFeeForm:function(e){this.currentRow=e||{},this.$refs.litigationFeeForm.open&&this.$refs.litigationFeeForm.open()},onLitigationFeeFormConfirm:function(){},openLitigationLogForm:function(e){this.currentRow=JSON.parse(JSON.stringify(e)),this.$refs.litigationLogForm.openDialog&&this.$refs.litigationLogForm.openDialog()},onLitigationLogFormConfirm:function(){},openLitigationLogView:function(e){this.currentRow=e||{},this.$refs.litigationLogView.openDialog&&this.$refs.litigationLogView.openDialog()},openDispatchVehicleForm:function(e){this.dispatchLoanId=e.流程序号,this.$refs.dispatchVehicleForm.openDialog()},calculateTotalDebt:function(e){var t=e.序号,a=this.getLitigationFeeAmount(t,"judgmentAmount"),l=this.getLitigationFeeAmount(t,"interest"),i=this.getLitigationFeeAmount(t,"litigation"),r=this.calculateUnsuedAmount(e),n=Number(a)+Number(l)+Number(i)+Number(r);return n},viewTotalDebtDetails:function(e){var t=e.序号,a=this.getLitigationFeeAmount(t,"judgmentAmount"),l=this.getLitigationFeeAmount(t,"interest"),i=this.getLitigationFeeAmount(t,"litigation"),r=this.calculateUnsuedAmount(e),n=this.calculateTotalDebt(e),o='<div style="text-align: left;">';o+="<p>判决金额: ￥".concat(this.formatMoney(a),"</p>"),o+="<p>利息: ￥".concat(this.formatMoney(l),"</p>"),o+="<p>法诉费用: ￥".concat(this.formatMoney(i),"</p>"),o+="<p>未起诉金额: ￥".concat(this.formatMoney(r),"</p>"),o+="<hr><p><strong>待追偿欠款总计: ￥".concat(this.formatMoney(n),"</strong></p>"),o+="</div>",this.$alert(o,"待追偿欠款详情",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定"})},openDailyExpenseDialog:function(e){this.$refs.litigationFeeForm.openDailyExpenseDialog(e.序号)},openUserInfo:function(e){this.customerInfo=e,this.userInfoVisible=!0},checkCar:function(e){this.plateNo=e,this.carShow=!0},getLawsuitTypeText:function(e){var t=this.lawsuitTypeList.find((function(t){return t.value===e}));return t?t.label:e},getLawsuitContentText:function(e){var t=this;if(!e)return"-";try{var a=JSON.parse(e);if(Array.isArray(a))return a.map((function(e){var a=t.lawsuitContentList.find((function(t){return t.value===e}));return a?a.label:e})).join("、")}catch(i){var l=this.lawsuitContentList.find((function(t){return t.value===e}));return l?l.label:e}return e},getUrgeStatusText:function(e){var t={1:"继续跟踪",2:"约定还款",3:"无法跟进"};return t[e]||e},getCarStatusText:function(e){var t=this.carStatusList.find((function(t){return t.value==e}));return t?t.label:e},showDebtDetail:function(e){this.currentDebtRow=e,this.debtDetailVisible=!0},formatMoney:function(e){return null===e||void 0===e||""===e?"0.00":Number(e).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2})},calculateUnsuedAmount:function(e){var t=Number(e.剩余金额)||0,a=Number(e.起诉金额)||0,l=t-a;return l>0?l:0},showUnsuedContentDetail:function(e){var t=this;return Object(o["a"])(Object(r["a"])().m((function a(){var l,i,o,u,c,d,m;return Object(r["a"])().w((function(a){while(1)switch(a.p=a.n){case 0:return a.p=0,a.n=1,Object(s["d"])(e.流程序号);case 1:l=a.v,200===l.code&&l.data?(i=[],l.data.forEach((function(e){if(t.isLitigationStarted(e)){var a=e.prosecutionContent;if(a)try{var l=JSON.parse(a);Array.isArray(l)?i.push.apply(i,Object(n["a"])(l)):i.push(a)}catch(r){i.push(a)}}})),o=Object(n["a"])(new Set(i)),u=t.lawsuitContentList,c=u.filter((function(e){return!o.includes(e.value)})),d='<div style="text-align: left;">',m=0,c.length>0?(d+='<h4 style="margin-bottom: 15px; color: #303133;">未起诉内容：</h4>',c.forEach((function(a){var l=0;switch(a.value){case"1":l=Number(e.银行剩余未还代偿金)||0;break;case"2":l=Number(e.代扣剩余未还代偿金)||0;break;case"3":l=Number(e.剩余未还违约金金额)||0;break;case"4":l=Number(e.剩余未还其他欠款)||0;break}d+="<p>".concat(a.label,": ￥").concat(t.formatMoney(l),"</p>"),m+=l})),d+="<hr><p><strong>未起诉金额合计: ￥".concat(t.formatMoney(m),"</strong></p>")):d+="<p>所有起诉内容都已被选择</p>",d+="</div>",t.$alert(d,"未起诉内容详情",{dangerouslyUseHTMLString:!0,confirmButtonText:"确定"})):t.$message.error("获取法诉案件数据失败"),a.n=3;break;case 2:a.p=2,a.v,t.$message.error("获取未起诉内容详情失败");case 3:return a.a(2)}}),a,null,[[0,2]])})))()},isLitigationStarted:function(e){var t=null!=e.litigationType&&""!==e.litigationType,a=null!=e.prosecutionType&&""!==e.prosecutionType,l=null!=e.prosecutionContent&&""!==e.prosecutionContent,i=null!=e.litigationStartDay&&""!==e.litigationStartDay;return t&&a&&l&&i}}},h=g,y=(a("af66"),a("62ee"),a("2877")),_=Object(y["a"])(h,l,i,!1,null,"5b2327da",null);t["default"]=_.exports},d6fd:function(e,t,a){"use strict";a("1791")},db56:function(e,t,a){"use strict";a("fae4")},e60a:function(e,t,a){},fae4:function(e,t,a){}}]);