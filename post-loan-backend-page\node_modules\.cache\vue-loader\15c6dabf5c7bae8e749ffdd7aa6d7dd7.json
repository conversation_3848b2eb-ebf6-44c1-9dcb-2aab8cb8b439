{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogView.vue?vue&type=template&id=134e98f7&scoped=true", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogView.vue", "mtime": 1754374146667}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753353054666}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXY+CiAgPGVsLWRpYWxvZyA6dmlzaWJsZS5zeW5jPSJ2aXNpYmxlIiB0aXRsZT0i5pel5b+X5p+l55yLIiB3aWR0aD0iODAwcHgiIEBjbG9zZT0iaGFuZGxlQ2FuY2VsIj4KICAgIDwhLS0g6L+b5bqm5p2h5oiW5pKk6K+J54q25oCBIC0tPgogICAgPGRpdiB2LWlmPSJpc1dpdGhkcmF3biIgc3R5bGU9InRleHQtYWxpZ246IGNlbnRlcjsgbWFyZ2luLWJvdHRvbTogMjRweDsgcGFkZGluZzogMjBweDsgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsgYm9yZGVyLXJhZGl1czogNHB4OyI+CiAgICAgIDxzcGFuIHN0eWxlPSJmb250LXNpemU6IDE2cHg7IGNvbG9yOiAjOTA5Mzk5OyBmb250LXdlaWdodDogYm9sZDsiPuW3suaSpOahiDwvc3Bhbj4KICAgIDwvZGl2PgogICAgPGVsLXN0ZXBzIHYtZWxzZSA6YWN0aXZlPSJhY3RpdmVTdGVwIiBmaW5pc2gtc3RhdHVzPSJzdWNjZXNzIiBwcm9jZXNzLXN0YXR1cz0icHJvY2VzcyIgYWxpZ24tY2VudGVyIHN0eWxlPSJtYXJnaW4tYm90dG9tOiAyNHB4IiBjbGFzcz0iY3VzdG9tLXN0ZXBzIj4KICAgICAgPGVsLXN0ZXAgdi1mb3I9IihpdGVtLCBpZHgpIGluIHN0YXR1c1N0ZXBzIiA6a2V5PSJpZHgiIDp0aXRsZT0iaXRlbSIgLz4KICAgIDwvZWwtc3RlcHM+CgogICAgPCEtLSDml6Xlv5fooajmoLwgLS0+CiAgICA8ZWwtdGFibGUgOmRhdGE9ImxvZ0xpc3QiIGJvcmRlciBzdHlsZT0id2lkdGg6IDEwMCU7IG1hcmdpbi1ib3R0b206IDI0cHgiPgogICAgICA8ZWwtdGFibGUtY29sdW1uIHByb3A9ImNyZWF0ZVRpbWUiIGxhYmVsPSLml7bpl7QiIHdpZHRoPSIxNjAiIC8+CiAgICAgIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0iY3JlYXRlQnkiIGxhYmVsPSLot5/ouKrkuroiIHdpZHRoPSIxMjAiIC8+CiAgICAgIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0ic3RhdHVzIiBsYWJlbD0i6Lef6Liq5Yqo5L2cIiB3aWR0aD0iMTIwIiAvPgogICAgICA8ZWwtdGFibGUtY29sdW1uIHByb3A9InJlbWFyayIgbGFiZWw9IuaPj+i/sCIgLz4KICAgIDwvZWwtdGFibGU+CgogICAgPHNwYW4gc2xvdD0iZm9vdGVyIiBjbGFzcz0iZGlhbG9nLWZvb3RlciI+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJoYW5kbGVVcmdlTG9nIj7lgqzorrDml6Xlv5c8L2VsLWJ1dHRvbj4KICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9ImhhbmRsZUNvbmZpcm0iPuehruiupDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iaGFuZGxlQ2FuY2VsIj7lj5bmtog8L2VsLWJ1dHRvbj4KICAgIDwvc3Bhbj4KICA8L2VsLWRpYWxvZz4KCiAgPCEtLSDlgqzorrDml6Xlv5fnu4Tku7YgLS0+CiAgPGxvYW4tcmVtaW5kZXItbG9nIHJlZj0ibG9hblJlbWluZGVyTG9nIiA6bG9hbi1pZD0icmVtaW5kZXJMb2dMb2FuSWQiIC8+CjwvZGl2Pgo="}, null]}