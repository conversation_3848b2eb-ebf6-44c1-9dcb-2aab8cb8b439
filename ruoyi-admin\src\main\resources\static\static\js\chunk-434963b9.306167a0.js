(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-434963b9"],{"0f5f":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.carInfo.plateNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车架号"}},[e._v(e._s(e.carInfo.identityNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"发动机号"}},[e._v(e._s(e.carInfo.engineNumber||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(e._s(e.carStatusLabel))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆位置"}},[e._v(e._s(e.carInfo.carAddress||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(e._s(e.gpsStatusLabel))]):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看车牌照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"行驶证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看行驶证照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"登记证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看登记证照片")])],1):e._e()],1)],1)},r=[],o=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),a("bd52")),n={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var e=this,t=this.carStatusList.find((function(t){return t.value===e.carInfo.carStatus}));return t?t.label:"未知状态"},gpsStatusLabel:function(){var e=this,t=this.gpsStatusList.find((function(t){return t.value===e.carInfo.gpsStatus}));return t?t.label:"未知状态"}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,e&&this.plateNo&&Object(o["e"])(this.plateNo).then((function(e){t.carInfo=e.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},i=n,s=a("2877"),u=Object(s["a"])(i,l,r,!1,null,null,null);t["a"]=u.exports},1791:function(e,t,a){},"2bf4":function(e,t,a){"use strict";a.d(t,"d",(function(){return r})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return n})),a.d(t,"e",(function(){return i})),a.d(t,"b",(function(){return s}));var l=a("b775");function r(e){return Object(l["a"])({url:"/car_order/car_order/list",method:"get",params:e})}function o(e){return Object(l["a"])({url:"/car_order/car_order/"+e,method:"get"})}function n(e){return Object(l["a"])({url:"/car_order/car_order",method:"post",data:e})}function i(e){return Object(l["a"])({url:"/car_order/car_order",method:"put",data:e})}function s(e){return Object(l["a"])({url:"/car_order/car_order/"+e,method:"delete"})}},"2eca":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[e._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},r=[],o=a("bd52"),n={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(o["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},i=n,s=(a("d6fd"),a("2877")),u=Object(s["a"])(i,l,r,!1,null,"8a3d4978",null);t["a"]=u.exports},3470:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"certId"}},[a("el-input",{attrs:{placeholder:"贷款人身份证号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.certId,callback:function(t){e.$set(e.queryParams,"certId",t)},expression:"queryParams.certId"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"salesman"}},[a("el-input",{attrs:{placeholder:"业务员姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.salesman,callback:function(t){e.$set(e.queryParams,"salesman",t)},expression:"queryParams.salesman"}})],1),a("el-form-item",{attrs:{label:"",prop:"jgName"}},[a("el-input",{attrs:{placeholder:"录单渠道名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jgName,callback:function(t){e.$set(e.queryParams,"jgName",t)},expression:"queryParams.jgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"partnerId"}},[a("el-select",{attrs:{placeholder:"放款银行",clearable:""},model:{value:e.queryParams.partnerId,callback:function(t){e.$set(e.queryParams,"partnerId",t)},expression:"queryParams.partnerId"}},e._l(e.bankList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"petitionName"}},[a("el-input",{attrs:{placeholder:"上访员姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.petitionName,callback:function(t){e.$set(e.queryParams,"petitionName",t)},expression:"queryParams.petitionName"}})],1),a("el-form-item",{attrs:{label:"",prop:"followStatus"}},[a("el-select",{attrs:{placeholder:"跟催类型",clearable:""},model:{value:e.queryParams.followStatus,callback:function(t){e.$set(e.queryParams,"followStatus",t)},expression:"queryParams.followStatus"}},e._l(e.followUpList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"dispatchStatus"}},[a("el-select",{attrs:{placeholder:"派单状态",clearable:""},model:{value:e.queryParams.dispatchStatus,callback:function(t){e.$set(e.queryParams,"dispatchStatus",t)},expression:"queryParams.dispatchStatus"}},e._l(e.dispatchList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"指派时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.queryParams.allocationTime,callback:function(t){e.$set(e.queryParams,"allocationTime",t)},expression:"queryParams.allocationTime"}})],1),a("el-form-item",{attrs:{label:"车辆状态"}},[a("el-select",{attrs:{placeholder:"车辆状态",clearable:""},model:{value:e.queryParams.carStatus,callback:function(t){e.$set(e.queryParams,"carStatus",t)},expression:"queryParams.carStatus"}},e._l(e.carStatusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"是否派单找车"}},[a("el-select",{attrs:{placeholder:"是否派单找车",clearable:""},model:{value:e.queryParams.isFindCar,callback:function(t){e.$set(e.queryParams,"isFindCar",t)},expression:"queryParams.isFindCar"}},[a("el-option",{attrs:{label:"未派单",value:0}}),a("el-option",{attrs:{label:"已派单",value:1}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vw_account_loanList}},[a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"逾期状态",align:"center",prop:"slippageStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(" "+e._s(1==t.row.slippageStatus?"提醒":2==t.row.slippageStatus?"电催":3==t.row.slippageStatus?"上访":4==t.row.slippageStatus?"逾期30-60":"逾期60+")+" ")])]}}])}),a("el-table-column",{attrs:{label:"还款状态",align:"center",prop:"repaymentStatus",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(" "+e._s(1==t.row.repaymentStatus?"还款中":2==t.row.repaymentStatus?"已完结":3==t.row.repaymentStatus?"提前结清":4==t.row.repaymentStatus?"逾期催回结清":5==t.row.repaymentStatus?"逾期减免结清":6==t.row.repaymentStatus?"逾期未还款":7==t.row.repaymentStatus?"逾期还款中":8==t.row.repaymentStatus?"代偿未还款":9==t.row.repaymentStatus?"代偿还款中":10==t.row.repaymentStatus?"代偿减免结清":11==t.row.repaymentStatus?"代偿全额结清":"未知状态")+" ")])]}}])}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"customerName"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openUserInfo({customerId:t.row.customerId,applyId:t.row.applyId})}}},[e._v(" "+e._s(t.row.customerName)+" ")])]}}])}),a("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"jgName"}}),a("el-table-column",{attrs:{label:"业务员",align:"center",prop:"nickName"}}),a("el-table-column",{attrs:{label:"车牌号码",align:"center",prop:"plateNo"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openCarInfo(t.row.plateNo)}}},[e._v(e._s(t.row.plateNo))])]}}])}),a("el-table-column",{attrs:{label:"车辆状态",align:"center",prop:"carStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.carStatus?a("span",[e._v(" "+e._s((e.carStatusList.find((function(e){return e.value===String(t.row.carStatus)}))||{}).label||"")+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"派车团队",align:"center",prop:"carTeamName"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.carTeamName?a("span",[e._v(e._s(t.row.carTeamName))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"位置",align:"center",prop:"carDetailAddress"}}),a("el-table-column",{attrs:{label:"GPS状态",align:"center",prop:"gpsStatus"}}),a("el-table-column",{attrs:{label:"派车单状态",align:"center",prop:"loanStatus",width:"130"}}),a("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"orgName"}}),a("el-table-column",{attrs:{label:"逾期天数",align:"center",prop:"boverdueDays",width:"130"}}),a("el-table-column",{attrs:{label:"首期逾期金额",align:"center",prop:"foverdueAmount",width:"130"}}),a("el-table-column",{attrs:{label:"银行逾期金额",align:"center",prop:"boverdueAmount",width:"130"}}),a("el-table-column",{attrs:{label:"代扣逾期金额",align:"center",prop:"doverdueAmount",width:"130"}}),a("el-table-column",{attrs:{label:"银行结清金额",align:"center",prop:"bSettleAmount",width:"130"}}),a("el-table-column",{attrs:{label:"代扣结清金额",align:"center",prop:"dSettleAmount",width:"130"}}),a("el-table-column",{attrs:{label:"上访员",align:"center",prop:"petitionUser",width:"130"}}),a("el-table-column",{attrs:{label:"跟催类型",align:"center",prop:"urgeType"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.urgeType?a("span",[e._v(" "+e._s(1==t.row.urgeType?"继续联系":2==t.row.urgeType?"约定还款":"无法跟进")+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"催记日期",align:"center",prop:"assignTime",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.assignTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"分配时间",align:"center",prop:"petitionTime",width:"130"}}),a("el-table-column",{attrs:{label:"下次跟进时间",align:"center",prop:"nextFollowTime",width:"130"}}),a("el-table-column",{attrs:{label:"预扣款时间",align:"center",prop:"preDeductionTime",width:"130"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-popover",{attrs:{placement:"left",trigger:"click","popper-class":"custom-popover"}},[a("div",{staticClass:"operation-buttons"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_account_loan:vw_account_loan:edit"],expression:"['vw_account_loan:vw_account_loan:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.initiate(t.row)}}},[e._v(" 发起代偿 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_account_loan:vw_account_loan:edit"],expression:"['vw_account_loan:vw_account_loan:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.openDispatchVehicleForm(t.row)}}},[e._v(" 发起找车 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_account_loan:vw_account_loan:edit"],expression:"['vw_account_loan:vw_account_loan:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.logView(t.row)}}},[e._v(" 查看催记 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["daily_expense_approval:daily_expense_approval:add"],expression:"['daily_expense_approval:daily_expense_approval:add']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.openDailyExpenseDialog(t.row)}}},[e._v(" 提交日常费用 ")])],1),a("el-button",{attrs:{slot:"reference",size:"mini",type:"text"},slot:"reference"},[e._v("更多")])],1)]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{staticClass:"dialogBox",attrs:{"close-on-click-modal":!1,title:"发起代偿",visible:e.commuteopen,width:"900px","append-to-body":""},on:{"update:visible":function(t){e.commuteopen=t}}},[a("div",{staticClass:"settle_money",on:{click:e.trialSub}},[e._v("发起试算")]),a("el-form",{ref:"trialForm",attrs:{model:e.trialForm,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"贷款人",prop:"customerName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.customerName,callback:function(t){e.$set(e.trialForm,"customerName",t)},expression:"trialForm.customerName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"出单渠道",prop:"orgName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.orgName,callback:function(t){e.$set(e.trialForm,"orgName",t)},expression:"trialForm.orgName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"放款银行",prop:"bank"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.bank,callback:function(t){e.$set(e.trialForm,"bank",t)},expression:"trialForm.bank"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"放款金额",prop:"loanAmount"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.loanAmount,callback:function(t){e.$set(e.trialForm,"loanAmount",t)},expression:"trialForm.loanAmount"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"剩余本金"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialFormprincipal,callback:function(t){e.trialFormprincipal=t},expression:"trialFormprincipal"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"银行逾期金额"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialFormboverdueAmount,callback:function(t){e.trialFormboverdueAmount=t},expression:"trialFormboverdueAmount"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"银行利息"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForminterest,callback:function(t){e.trialForminterest=t},expression:"trialForminterest"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"代偿总金额"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialFormall,callback:function(t){e.trialFormall=t},expression:"trialFormall"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"风险金划比例",prop:"fxjProportion"}},[a("el-input",{on:{input:e.fxjCount},scopedSlots:e._u([{key:"append",fn:function(){return[e._v("%")]},proxy:!0}]),model:{value:e.trialForm.fxjProportion,callback:function(t){e.$set(e.trialForm,"fxjProportion",t)},expression:"trialForm.fxjProportion"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"风险金划金额",prop:"fxjMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.fxjMoney,callback:function(t){e.$set(e.trialForm,"fxjMoney",t)},expression:"trialForm.fxjMoney"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"账号类型",prop:"fxjAccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:""},model:{value:e.trialForm.fxjAccount,callback:function(t){e.$set(e.trialForm,"fxjAccount",t)},expression:"trialForm.fxjAccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"渠道转入比例",prop:"qdProportion"}},[a("el-input",{on:{input:e.qdCount},scopedSlots:e._u([{key:"append",fn:function(){return[e._v("%")]},proxy:!0}]),model:{value:e.trialForm.qdProportion,callback:function(t){e.$set(e.trialForm,"qdProportion",t)},expression:"trialForm.qdProportion"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"渠道转入金额",prop:"qdMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.qdMoney,callback:function(t){e.$set(e.trialForm,"qdMoney",t)},expression:"trialForm.qdMoney"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"账号类型",prop:"qdAccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:""},model:{value:e.trialForm.qdAccount,callback:function(t){e.$set(e.trialForm,"qdAccount",t)},expression:"trialForm.qdAccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"广明借比例",prop:"gmjProportion"}},[a("el-input",{on:{input:e.gmjCount},scopedSlots:e._u([{key:"append",fn:function(){return[e._v("%")]},proxy:!0}]),model:{value:e.trialForm.gmjProportion,callback:function(t){e.$set(e.trialForm,"gmjProportion",t)},expression:"trialForm.gmjProportion"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"广明借额",prop:"gmjMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.gmjMoney,callback:function(t){e.$set(e.trialForm,"gmjMoney",t)},expression:"trialForm.gmjMoney"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"账号类型",prop:"gmjAccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:""},model:{value:e.trialForm.gmjAccount,callback:function(t){e.$set(e.trialForm,"gmjAccount",t)},expression:"trialForm.gmjAccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"科技借比例",prop:"kjczProportion"}},[a("el-input",{on:{input:e.kjjCount},scopedSlots:e._u([{key:"append",fn:function(){return[e._v("%")]},proxy:!0}]),model:{value:e.trialForm.kjczProportion,callback:function(t){e.$set(e.trialForm,"kjczProportion",t)},expression:"trialForm.kjczProportion"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"科技借额",prop:"kjjMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.kjjMoney,callback:function(t){e.$set(e.trialForm,"kjjMoney",t)},expression:"trialForm.kjjMoney"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"账号类型",prop:"kjjAccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:""},model:{value:e.trialForm.kjjAccount,callback:function(t){e.$set(e.trialForm,"kjjAccount",t)},expression:"trialForm.kjjAccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"科技出资比例",prop:"kjczProportion"}},[a("el-input",{on:{input:e.kjczCount},scopedSlots:e._u([{key:"append",fn:function(){return[e._v("%")]},proxy:!0}]),model:{value:e.trialForm.kjczProportion,callback:function(t){e.$set(e.trialForm,"kjczProportion",t)},expression:"trialForm.kjczProportion"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"科技出资金额",prop:"kjczMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.kjczMoney,callback:function(t){e.$set(e.trialForm,"kjczMoney",t)},expression:"trialForm.kjczMoney"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"账号类型",prop:"kjczAccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:""},model:{value:e.trialForm.kjczAccount,callback:function(t){e.$set(e.trialForm,"kjczAccount",t)},expression:"trialForm.kjczAccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"守邦出资比例",prop:"sbczProportion"}},[a("el-input",{on:{input:e.sbczCount},scopedSlots:e._u([{key:"append",fn:function(){return[e._v("%")]},proxy:!0}]),model:{value:e.trialForm.sbczProportion,callback:function(t){e.$set(e.trialForm,"sbczProportion",t)},expression:"trialForm.sbczProportion"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"守邦出资金额",prop:"sbczMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.sbczMoney,callback:function(t){e.$set(e.trialForm,"sbczMoney",t)},expression:"trialForm.sbczMoney"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"账号类型",prop:"sbczAccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:""},model:{value:e.trialForm.sbczAccount,callback:function(t){e.$set(e.trialForm,"sbczAccount",t)},expression:"trialForm.sbczAccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"代扣剩余未还金额"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialFormdoverdueAmount,callback:function(t){e.trialFormdoverdueAmount=t},expression:"trialFormdoverdueAmount"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"违约金"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialFormliquidatedDamages,callback:function(t){e.trialFormliquidatedDamages=t},expression:"trialFormliquidatedDamages"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"登记其他欠款金额",prop:"otherDebt"}},[a("el-input",{attrs:{type:"number"},on:{input:e.handleInput3},model:{value:e.trialFormotherDebt,callback:function(t){e.trialFormotherDebt=t},expression:"trialFormotherDebt"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"总账款"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialFormtotal,callback:function(t){e.trialFormtotal=t},expression:"trialFormtotal"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[1!=e.trialForm.examineStatus?a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]):e._e(),a("el-button",{on:{click:function(t){return e.cancel("commuteopen")}}},[e._v("取 消")])],1)],1),a("userInfo",{ref:"userInfo",attrs:{visible:e.userInfoVisible,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.userInfoVisible=t}}}),a("carInfo",{ref:"carInfo",attrs:{visible:e.carInfoVisible,title:"车辆信息",plateNo:e.plateNo,permission:"2"},on:{"update:visible":function(t){e.carInfoVisible=t}}}),a("loan-reminder-log",{ref:"loanReminderLog",attrs:{"loan-id":e.currentRow.loanId}}),a("DispatchVehicleForm",{ref:"dispatchVehicleForm",attrs:{loanId:e.dispatchLoanId}}),a("el-dialog",{attrs:{title:e.getDailyExpenseDialogTitle(),visible:e.dailyExpenseDialogVisible,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.dailyExpenseDialogVisible=t}}},[a("el-form",{ref:"dailyExpenseForm",attrs:{model:e.dailyExpenseForm,rules:e.dailyExpenseRules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"费用类型",prop:"expenseType"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择费用类型"},model:{value:e.dailyExpenseForm.expenseType,callback:function(t){e.$set(e.dailyExpenseForm,"expenseType",t)},expression:"dailyExpenseForm.expenseType"}},[a("el-option",{attrs:{label:"油费",value:"oil_fee"}}),a("el-option",{attrs:{label:"路费",value:"road_fee"}}),a("el-option",{attrs:{label:"餐费",value:"meal_fee"}}),a("el-option",{attrs:{label:"住宿费",value:"accommodation_fee"}}),a("el-option",{attrs:{label:"交通费",value:"transport_fee"}}),a("el-option",{attrs:{label:"停车费",value:"parking_fee"}}),a("el-option",{attrs:{label:"通讯费",value:"communication_fee"}}),a("el-option",{attrs:{label:"其他",value:"other"}})],1)],1),a("el-form-item",{attrs:{label:"费用金额",prop:"expenseAmount"}},[a("el-input",{attrs:{placeholder:"请输入费用金额"},model:{value:e.dailyExpenseForm.expenseAmount,callback:function(t){e.$set(e.dailyExpenseForm,"expenseAmount",t)},expression:"dailyExpenseForm.expenseAmount"}},[a("template",{slot:"prepend"},[e._v("￥")])],2)],1),a("el-form-item",{attrs:{label:"费用发生日期",prop:"expenseDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择费用发生日期","value-format":"yyyy-MM-dd"},model:{value:e.dailyExpenseForm.expenseDate,callback:function(t){e.$set(e.dailyExpenseForm,"expenseDate",t)},expression:"dailyExpenseForm.expenseDate"}})],1),a("el-form-item",{attrs:{label:"费用说明",prop:"expenseDescription"}},[a("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入费用说明"},model:{value:e.dailyExpenseForm.expenseDescription,callback:function(t){e.$set(e.dailyExpenseForm,"expenseDescription",t)},expression:"dailyExpenseForm.expenseDescription"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitDailyExpense}},[e._v("确 定")]),a("el-button",{on:{click:e.cancelDailyExpense}},[e._v("取 消")])],1)],1)],1)},r=[],o=a("5530"),n=(a("b0c0"),a("a9e3"),a("b680"),a("b775"));function i(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/list",method:"get",params:e})}var s=a("a1e7"),u=a("2bf4"),c=a("5c96"),m=a("bd52"),p=a("a6de"),d=a("2eca"),b=a("0f5f"),f=a("7954"),y=a("6634"),v={name:"Vw_account_loan",components:{userInfo:d["a"],carInfo:b["a"],LoanReminderLog:f["a"],DispatchVehicleForm:y["a"]},data:function(){return{loading:!0,showSearch:!0,total:0,vw_account_loanList:[],title:"",commuteopen:!1,trialForm:{},trialFormprincipal:0,trialFormboverdueAmount:0,trialForminterest:0,trialFormall:0,trialFormdoverdueAmount:0,trialFormliquidatedDamages:0,trialFormtotal:0,trialFormotherDebt:0,accountList:[],queryParams:{pageNum:1,pageSize:15,customerName:null,applyNo:null,plateNo:null,salesman:null,jgName:null,partnerId:null,petitionName:null,followStatus:null,dispatchStatus:null,allocationTime:null,startTime:null,endTime:null,slippageStatus:3,teamId:null,garageId:null,libraryStatus:null,inboundTime:null,outboundTime:null,locatingCommission:null,GPS:null,keyStatus:null,keyTime:null,collectionMethod:null,status:null,keyProvince:null,keyCity:null,keyBorough:null,keyAddress:null,keyDetailAddress:null,carStatus:null,isFindCar:null},bankList:[{value:"EO00000010",label:"苏银金租"},{value:"IO00000006",label:"浙商银行"},{value:"IO00000007",label:"中关村银行"},{value:"IO00000008",label:"蓝海银行"},{value:"IO00000009",label:"华瑞银行"},{value:"IO00000010",label:"皖新租赁"}],followUpList:[{label:"无法跟进",value:1},{label:"约定还款",value:2},{label:"继续联系",value:3}],dispatchList:[{label:"待派单",value:1},{label:"找车中",value:2},{label:"已入库",value:3},{label:"未派单",value:4},{label:"已撤销",value:5}],carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],form:{customerName:"",mobilePhone:"",contractId:"",customerId:"",plateNo:"",carStatus:"",carDetailAddress:"",gpsStatus:"",followUpType:"",applyNo:"",teamId:null,garageId:null,libraryStatus:null,inboundTime:"",outboundTime:"",locatingCommission:null,keyStatus:null,keyTime:"",collectionMethod:null,status:null,keyProvince:"",keyCity:"",keyBorough:"",keyAddress:"",keyDetailAddress:""},rules:{},radio:0,customerInfo:{customerId:"",applyId:""},userInfoVisible:!1,plateNo:"",carInfoVisible:!1,currentRow:{},dispatchLoanId:null,dailyExpenseDialogVisible:!1,currentLoanRow:{},dailyExpenseForm:{litigationCaseId:null,loanId:null,status:2,expenseType:"",expenseAmount:"",expenseDate:"",expenseDescription:"",receiptUrl:"",applicantId:"",applicantName:"",applicationTime:"",approvalStatus:"0"},dailyExpenseRules:{expenseType:[{required:!0,message:"请选择费用类型",trigger:"change"}],expenseAmount:[{required:!0,message:"请输入费用金额",trigger:"blur"},{pattern:/^\d+(\.\d{1,2})?$/,message:"请输入正确的金额格式",trigger:"blur"}],expenseDate:[{required:!0,message:"请选择费用发生日期",trigger:"change"}],expenseDescription:[{required:!0,message:"请输入费用说明",trigger:"blur"}]}}},created:function(){this.getList(),this.getCarTeam(),this.getBankList()},methods:{getList:function(){var e=this;this.loading=!0,i(this.queryParams).then((function(t){e.vw_account_loanList=t.rows,e.total=t.total,e.loading=!1})).catch((function(){e.loading=!1}))},fxjCount:function(e){this.trialForm.fxjMoney=Number(Number(e)*this.trialFormall)/100},qdCount:function(e){this.trialForm.qdMoney=Number(Number(e)*this.trialFormall)/100},gmjCount:function(e){this.trialForm.gmjMoney=Number(Number(e)*this.trialFormall)/100},kjjCount:function(e){this.trialForm.kjjMoney=Number(Number(e)*this.trialFormall)/100},kjczCount:function(e){this.trialForm.kjczMoney=Number(Number(e)*this.trialFormall)/100},sbczCount:function(e){this.trialForm.sbczMoney=Number(Number(e)*this.trialFormall)/100},handleInput3:function(e){this.trialFormotherDebt=Number(e),this.trialFormtotal=Number(this.trialFormall+this.trialFormdoverdueAmount+this.trialFormliquidatedDamages+this.trialFormotherDebt).toFixed(2)},trialSub:function(){var e=this,t={applyId:this.trialForm.applyId,id:this.trialForm.id,loanId:this.trialForm.loanId,loanAmount:this.trialForm.loanAmount,partnerId:this.trialForm.partnerId};Object(m["f"])(t).then((function(t){e.trialFormprincipal=t.data.principal||0,e.trialForm.defaultInterest=t.data.defaultInterest||0,e.trialForminterest=t.data.interest||0,e.trialFormall=t.data.btotalMoney||0,e.trialFormdoverdueAmount=t.data.dtotalMoney||0,e.trialFormliquidatedDamages=t.data.liquidatedDamages||0,e.trialFormtotal=Number(e.trialFormall+e.trialFormdoverdueAmount+e.trialFormliquidatedDamages+e.trialFormotherDebt).toFixed(2)}))},getBankList:function(){var e=this;Object(m["i"])().then((function(t){e.accountList=t.rows}))},getCarTeam:function(){var e=this;this.loading=!0,Object(s["d"])({status:1}).then((function(t){e.car_team=t.rows,e.loading=!1})).catch((function(){e.loading=!1}))},cancel:function(e){"commuteopen"===e&&(this.commuteopen=!1),this.reset()},reset:function(){var e;null===(e=this.$refs.form)||void 0===e||e.resetFields(),this.form={},this.queryParams.carStatus=null,this.queryParams.isFindCar=null},handleQuery:function(){this.queryParams.allocationTime&&(this.queryParams.startTime=this.queryParams.allocationTime[0],this.queryParams.endTime=this.queryParams.allocationTime[1]),this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.customerName=null,this.queryParams.certId=null,this.queryParams.plateNo=null,this.queryParams.salesman=null,this.queryParams.jgName=null,this.queryParams.partnerId=null,this.queryParams.petitionName=null,this.queryParams.followStatus=null,this.queryParams.dispatchStatus=null,this.queryParams.allocationTime=null,this.queryParams.startTime=null,this.queryParams.endTime=null,this.queryParams.carStatus=null,this.queryParams.isFindCar=null,this.handleQuery()},handleSelectionChange:function(e){},handleUpdate:function(e){this.open=!0,this.title="修改VIEW"},initiate:function(e){var t=this,a={loanId:e.loanId};Object(m["o"])(a).then((function(a){a.data?(t.trialForm=a.data,t.trialForm.loanAmount=e.contractAmt||0,t.trialForm.dtotalMoney=a.data.trialBalance?a.data.trialBalance.dtotalMoney:0,t.trialFormprincipal=a.data.trialBalance?a.data.trialBalance.principal:0,t.trialFormboverdueAmount=e.boverdueAmount||0,t.trialForminterest=a.data.trialBalance?a.data.trialBalance.interest:0,t.trialFormall=a.data.trialBalance?a.data.trialBalance.btotalMoney:0,t.trialFormdoverdueAmount=a.data.trialBalance?a.data.trialBalance.dtotalMoney:0,t.trialFormliquidatedDamages=a.data.trialBalance?a.data.trialBalance.liquidatedDamages:0,t.trialFormotherDebt=a.data.otherDebt?a.data.otherDebt:0,console.log(t.trialFormall,t.trialFormdoverdueAmount,t.trialFormliquidatedDamages,t.trialFormotherDebt),t.trialFormtotal=Number(t.trialFormall+t.trialFormdoverdueAmount+t.trialFormliquidatedDamages+t.trialFormotherDebt).toFixed(2)):t.addTrial(e),t.commuteopen=!0}))},submitForm:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return console.log("表单验证失败"),!1;Object(u["a"])(e.form).then((function(t){200===t.code?(Object(c["Message"])({message:"找车订单添加成功",type:"success",duration:3e3}),e.getList(),e.reset()):Object(c["Message"])({message:"添加失败: "+t.message,type:"error",duration:3e3})})).catch((function(e){Object(c["Message"])({message:"服务器错误: "+e.message,type:"error",duration:3e3})}))}))},openUserInfo:function(e){this.customerInfo=e,this.userInfoVisible=!0},openCarInfo:function(e){this.plateNo=e,this.carInfoVisible=!0},logView:function(e){this.currentRow=e,this.$refs.loanReminderLog.openLogDialog()},openDispatchVehicleForm:function(e){this.dispatchLoanId=e.loanId,this.$refs.dispatchVehicleForm.openDialog()},openDailyExpenseDialog:function(e){var t,a;this.resetDailyExpenseForm(),this.dailyExpenseForm.applicantId=String((null===(t=this.$store.state.user)||void 0===t?void 0:t.id)||""),this.dailyExpenseForm.applicantName=String((null===(a=this.$store.state.user)||void 0===a?void 0:a.name)||"");var l=new Date;this.dailyExpenseForm.applicationTime=l.toISOString().split("T")[0],this.dailyExpenseForm.loanId=e.loanId,this.dailyExpenseForm.status=2,this.currentLoanRow=e,this.dailyExpenseDialogVisible=!0},resetDailyExpenseForm:function(){var e;this.dailyExpenseForm={litigationCaseId:null,loanId:null,status:2,expenseType:"",expenseAmount:"",expenseDate:"",expenseDescription:"",receiptUrl:"",applicantId:"",applicantName:"",applicationTime:"",approvalStatus:"0"},this.currentLoanRow={},null===(e=this.$refs.dailyExpenseForm)||void 0===e||e.resetFields()},cancelDailyExpense:function(){this.dailyExpenseDialogVisible=!1,this.resetDailyExpenseForm()},submitDailyExpense:function(){var e=this;this.$refs.dailyExpenseForm.validate((function(t){if(t){var a=Object(o["a"])({},e.dailyExpenseForm);if(!a.loanId)return void e.$message.error("缺少贷款ID，请重新选择记录");a.status=2,Object(p["a"])(a).then((function(t){200===t.code?(e.$message.success("日常费用申请提交成功"),e.dailyExpenseDialogVisible=!1,e.resetDailyExpenseForm()):e.$message.error("提交失败："+(t.msg||"未知错误"))})).catch((function(t){console.error("提交失败:",t),e.$message.error("提交失败，请稍后重试")}))}}))},getDailyExpenseDialogTitle:function(){return this.currentLoanRow&&this.currentLoanRow.customerName?"提交日常费用 - ".concat(this.currentLoanRow.customerName):"提交日常费用"}}},h=v,g=(a("f58d"),a("a022"),a("2877")),_=Object(g["a"])(h,l,r,!1,null,null,null);t["default"]=_.exports},"52dd":function(e,t,a){},6634:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticClass:"dialogBox",attrs:{title:"发起找车",visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-descriptions",{attrs:{column:2,border:""}},[a("el-descriptions-item",{attrs:{label:"贷款人"}},[e._v(" "+e._s(e.formData.customerName||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"联系电话"}},[e._v(" "+e._s(e.formData.mobilePhone||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"出单渠道"}},[e._v(" "+e._s(e.formData.jgName||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"业务员"}},[e._v(" "+e._s(e.formData.mangerId||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"车辆牌号"}},[e._v(" "+e._s(e.formData.plateNo||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(" "+e._s(e.formData.carStatus||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"车辆位置",span:2}},[e._v(" "+e._s(e.formData.carDetailAddress||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(" "+e._s(e.formData.gpsStatus||"-")+" ")])],1),a("el-form",{ref:"teamForm",staticStyle:{"margin-top":"30px"},attrs:{model:e.form,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"指定派车团队",prop:"teamId",rules:[{required:!0,message:"请选择派车团队",trigger:"change"}]}},[a("el-select",{staticStyle:{width:"40%","margin-left":"16px"},attrs:{placeholder:"请选择派车团队",clearable:""},model:{value:e.form.teamId,callback:function(t){e.$set(e.form,"teamId",t)},expression:"form.teamId"}},e._l(e.car_team,(function(e){return a("el-option",{key:e.id,attrs:{label:e.teamName,value:e.id}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.handleClose}},[e._v("取 消")])],1)],1)},r=[],o=a("a1e7"),n=a("bd52"),i=a("2bf4"),s={name:"DispatchVehicleForm",props:{loanId:{type:String,default:""}},data:function(){return{dialogVisible:!1,submitLoading:!1,formData:{},form:{teamId:null},car_team:[]}},watch:{loanId:{handler:function(e){var t=this;e&&Object(n["h"])(e).then((function(e){t.formData=e.data}))},deep:!0,immediate:!0}},created:function(){this.getCarTeam()},methods:{openDialog:function(){this.dialogVisible=!0,this.resetForm()},handleClose:function(){this.dialogVisible=!1,this.resetForm()},resetForm:function(){var e=this;this.form={teamId:null},this.$nextTick((function(){e.$refs.teamForm&&e.$refs.teamForm.clearValidate()}))},getCarTeam:function(){var e=this;Object(o["d"])({status:1}).then((function(t){e.car_team=t.rows||[]})).catch((function(){e.car_team=[]}))},submitForm:function(){var e=this;this.form.teamId?(this.form.loanId=this.formData.loanId,this.form.applyNo=this.formData.applyId,Object(i["a"])(this.form).then((function(t){200===t.code?(e.$message.success("提交成功"),e.dialogVisible=!1,e.resetForm()):e.$message.error("提交失败")}))):this.$message.error("请选择派车团队")}}},u=s,c=a("2877"),m=Object(c["a"])(u,l,r,!1,null,null,null);t["a"]=m.exports},a022:function(e,t,a){"use strict";a("a405")},a1e7:function(e,t,a){"use strict";a.d(t,"d",(function(){return r})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return n})),a.d(t,"e",(function(){return i})),a.d(t,"b",(function(){return s}));var l=a("b775");function r(e){return Object(l["a"])({url:"/car_team/car_team/list",method:"get",params:e})}function o(e){return Object(l["a"])({url:"/car_team/car_team/"+e,method:"get"})}function n(e){return Object(l["a"])({url:"/car_team/car_team",method:"post",data:e})}function i(e){return Object(l["a"])({url:"/car_team/car_team",method:"put",data:e})}function s(e){return Object(l["a"])({url:"/car_team/car_team/"+e,method:"delete"})}},a405:function(e,t,a){},a6de:function(e,t,a){"use strict";a.d(t,"c",(function(){return r})),a.d(t,"d",(function(){return o})),a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var l=a("b775");function r(e){return Object(l["a"])({url:"/daily_expense_approval/daily_expense_approval/list",method:"get",params:e})}function o(e){return Object(l["a"])({url:"/daily_expense_approval/daily_expense_approval/pendingList",method:"get",params:e})}function n(e){return Object(l["a"])({url:"/daily_expense_approval/daily_expense_approval",method:"post",data:e})}function i(e,t){return Object(l["a"])({url:"/daily_expense_approval/daily_expense_approval/approve/"+e,method:"post",data:t})}},d6fd:function(e,t,a){"use strict";a("1791")},f58d:function(e,t,a){"use strict";a("52dd")}}]);