{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: 'LitigationStatusCascader',\n  props: {\n    value: {\n      type: [String, Array],\n      default: ''\n    },\n    placeholder: {\n      type: String,\n      default: '法诉状态'\n    }\n  },\n  data: function data() {\n    return {\n      litigationStatusTree: [{\n        label: '暂不起诉',\n        value: '暂不起诉',\n        children: [{\n          label: '暂不起诉',\n          value: '暂不起诉'\n        }, {\n          label: '撤案',\n          value: '撤案'\n        }]\n      }, {\n        label: '立案前',\n        value: '立案前',\n        children: [{\n          label: '准备资料',\n          value: '准备资料'\n        }, {\n          label: '已邮寄',\n          value: '已邮寄'\n        }, {\n          label: '待立案',\n          value: '待立案'\n        }]\n      }, {\n        label: '立案-判决',\n        value: '立案-判决',\n        children: [{\n          label: '获取案件号',\n          value: '获取案件号'\n        }, {\n          label: '待出民初号',\n          value: '待出民初号'\n        }, {\n          label: '待开庭',\n          value: '待开庭'\n        }, {\n          label: '待出法院文书',\n          value: '待出法院文书'\n        }]\n      }, {\n        label: '判决-执行',\n        value: '判决-执行',\n        children: [{\n          label: '待执行',\n          value: '待执行'\n        }, {\n          label: '待出申请书',\n          value: '待出申请书'\n        }, {\n          label: '已提交执行书',\n          value: '已提交执行书'\n        }]\n      }, {\n        label: '执行后',\n        value: '执行后',\n        children: [{\n          label: '执行中',\n          value: '执行中'\n        }, {\n          label: '执行终本',\n          value: '执行终本'\n        }, {\n          label: '继续执行',\n          value: '继续执行'\n        }, {\n          label: '待送车',\n          value: '待送车'\n        }, {\n          label: '待法拍',\n          value: '待法拍'\n        }]\n      }, {\n        label: '结案',\n        value: '结案',\n        children: [{\n          label: '法诉减免结清',\n          value: '法诉减免结清'\n        }, {\n          label: '法诉全额结清',\n          value: '法诉全额结清'\n        }]\n      }],\n      cascaderProps: {\n        emitPath: false,\n        value: 'value',\n        label: 'label',\n        children: 'children',\n        // 只允许选择子节点（有children的父节点不可选）\n        disabled: function disabled(node) {\n          return !!node.children && node.children.length > 0;\n        }\n      }\n    };\n  },\n  computed: {\n    proxyValue: {\n      get: function get() {\n        return this.value;\n      },\n      set: function set(val) {\n        this.$emit('input', val);\n        this.$emit('update:value', val);\n      }\n    }\n  },\n  methods: {\n    handleChange: function handleChange(val) {\n      if (this.$listeners && this.$listeners.change) {\n        this.$emit('change', val);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "value", "type", "String", "Array", "default", "placeholder", "data", "litigationStatusTree", "label", "children", "cascaderProps", "emitPath", "disabled", "node", "length", "computed", "proxyValue", "get", "set", "val", "$emit", "methods", "handleChange", "$listeners", "change"], "sources": ["src/layout/components/Dialog/litigationStatus.vue"], "sourcesContent": ["<template>\r\n  <el-cascader\r\n    v-model=\"proxyValue\"\r\n    :options=\"litigationStatusTree\"\r\n    :props=\"cascaderProps\"\r\n    :placeholder=\"placeholder\"\r\n    clearable\r\n    @change=\"handleChange\" />\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'LitigationStatusCascader',\r\n  props: {\r\n    value: {\r\n      type: [String, Array],\r\n      default: '',\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: '法诉状态',\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      litigationStatusTree: [\r\n        {\r\n          label: '暂不起诉',\r\n          value: '暂不起诉',\r\n          children: [\r\n            { label: '暂不起诉', value: '暂不起诉' },\r\n            { label: '撤案', value: '撤案' },\r\n          ],\r\n        },\r\n        {\r\n          label: '立案前',\r\n          value: '立案前',\r\n          children: [\r\n            { label: '准备资料', value: '准备资料' },\r\n            { label: '已邮寄', value: '已邮寄' },\r\n            { label: '待立案', value: '待立案' },\r\n          ],\r\n        },\r\n        {\r\n          label: '立案-判决',\r\n          value: '立案-判决',\r\n          children: [\r\n            { label: '获取案件号', value: '获取案件号' },\r\n            { label: '待出民初号', value: '待出民初号' },\r\n            { label: '待开庭', value: '待开庭' },\r\n            { label: '待出法院文书', value: '待出法院文书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '判决-执行',\r\n          value: '判决-执行',\r\n          children: [\r\n            { label: '待执行', value: '待执行' },\r\n            { label: '待出申请书', value: '待出申请书' },\r\n            { label: '已提交执行书', value: '已提交执行书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '执行后',\r\n          value: '执行后',\r\n          children: [\r\n            { label: '执行中', value: '执行中' },\r\n            { label: '执行终本', value: '执行终本' },\r\n            { label: '继续执行', value: '继续执行' },\r\n            { label: '待送车', value: '待送车' },\r\n            { label: '待法拍', value: '待法拍' },\r\n          ],\r\n        },\r\n        {\r\n          label: '结案',\r\n          value: '结案',\r\n          children: [\r\n            { label: '法诉减免结清', value: '法诉减免结清' },\r\n            { label: '法诉全额结清', value: '法诉全额结清' },\r\n          ],\r\n        },\r\n      ],\r\n      cascaderProps: {\r\n        emitPath: false,\r\n        value: 'value',\r\n        label: 'label',\r\n        children: 'children',\r\n        // 只允许选择子节点（有children的父节点不可选）\r\n        disabled: node => !!node.children && node.children.length > 0,\r\n      },\r\n    }\r\n  },\r\n  computed: {\r\n    proxyValue: {\r\n      get() {\r\n        return this.value\r\n      },\r\n      set(val) {\r\n        this.$emit('input', val)\r\n        this.$emit('update:value', val)\r\n      },\r\n    },\r\n  },\r\n  methods: {\r\n    handleChange(val) {\r\n      if (this.$listeners && this.$listeners.change) {\r\n        this.$emit('change', val)\r\n      }\r\n    },\r\n  },\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;iCAWA;EACAA,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,KAAA;MACAC,OAAA;IACA;IACAC,WAAA;MACAJ,IAAA,EAAAC,MAAA;MACAE,OAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,oBAAA,GACA;QACAC,KAAA;QACAR,KAAA;QACAS,QAAA,GACA;UAAAD,KAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,KAAA;UAAAR,KAAA;QAAA;MAEA,GACA;QACAQ,KAAA;QACAR,KAAA;QACAS,QAAA,GACA;UAAAD,KAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,KAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,KAAA;UAAAR,KAAA;QAAA;MAEA,GACA;QACAQ,KAAA;QACAR,KAAA;QACAS,QAAA,GACA;UAAAD,KAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,KAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,KAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,KAAA;UAAAR,KAAA;QAAA;MAEA,GACA;QACAQ,KAAA;QACAR,KAAA;QACAS,QAAA,GACA;UAAAD,KAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,KAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,KAAA;UAAAR,KAAA;QAAA;MAEA,GACA;QACAQ,KAAA;QACAR,KAAA;QACAS,QAAA,GACA;UAAAD,KAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,KAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,KAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,KAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,KAAA;UAAAR,KAAA;QAAA;MAEA,GACA;QACAQ,KAAA;QACAR,KAAA;QACAS,QAAA,GACA;UAAAD,KAAA;UAAAR,KAAA;QAAA,GACA;UAAAQ,KAAA;UAAAR,KAAA;QAAA;MAEA,EACA;MACAU,aAAA;QACAC,QAAA;QACAX,KAAA;QACAQ,KAAA;QACAC,QAAA;QACA;QACAG,QAAA,WAAAA,SAAAC,IAAA;UAAA,SAAAA,IAAA,CAAAJ,QAAA,IAAAI,IAAA,CAAAJ,QAAA,CAAAK,MAAA;QAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,UAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAjB,KAAA;MACA;MACAkB,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,UAAAD,GAAA;QACA,KAAAC,KAAA,iBAAAD,GAAA;MACA;IACA;EACA;EACAE,OAAA;IACAC,YAAA,WAAAA,aAAAH,GAAA;MACA,SAAAI,UAAA,SAAAA,UAAA,CAAAC,MAAA;QACA,KAAAJ,KAAA,WAAAD,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}