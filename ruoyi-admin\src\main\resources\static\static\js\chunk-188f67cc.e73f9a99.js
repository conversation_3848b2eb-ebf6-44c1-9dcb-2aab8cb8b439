(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-188f67cc"],{"0f5f":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.carInfo.plateNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车架号"}},[e._v(e._s(e.carInfo.identityNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"发动机号"}},[e._v(e._s(e.carInfo.engineNumber||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(e._s(e.carStatusLabel))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆位置"}},[e._v(e._s(e.carInfo.carAddress||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(e._s(e.gpsStatusLabel))]):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看车牌照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"行驶证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看行驶证照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"登记证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看登记证照片")])],1):e._e()],1)],1)},l=[],o=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),a("bd52")),r={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var e=this,t=this.carStatusList.find((function(t){return t.value===e.carInfo.carStatus}));return t?t.label:"未知状态"},gpsStatusLabel:function(){var e=this,t=this.gpsStatusList.find((function(t){return t.value===e.carInfo.gpsStatus}));return t?t.label:"未知状态"}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,e&&this.plateNo&&Object(o["e"])(this.plateNo).then((function(e){t.carInfo=e.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},i=r,s=a("2877"),u=Object(s["a"])(i,n,l,!1,null,null,null);t["a"]=u.exports},1791:function(e,t,a){},1964:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"certId"}},[a("el-input",{attrs:{placeholder:"贷款人身份证号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.certId,callback:function(t){e.$set(e.queryParams,"certId",t)},expression:"queryParams.certId"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"请输入车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"salesman"}},[a("el-input",{attrs:{placeholder:"业务员姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.salesman,callback:function(t){e.$set(e.queryParams,"salesman",t)},expression:"queryParams.salesman"}})],1),a("el-form-item",{attrs:{label:"",prop:"orgName"}},[a("el-input",{attrs:{placeholder:"请输入出单渠道",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.orgName,callback:function(t){e.$set(e.queryParams,"orgName",t)},expression:"queryParams.orgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"bank"}},[a("el-select",{attrs:{placeholder:"放款银行",clearable:""},model:{value:e.queryParams.bank,callback:function(t){e.$set(e.queryParams,"bank",t)},expression:"queryParams.bank"}},e._l(e.lendingBankList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.label}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"nickName"}},[a("el-input",{attrs:{placeholder:"申请人",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.nickName,callback:function(t){e.$set(e.queryParams,"nickName",t)},expression:"queryParams.nickName"}})],1),a("el-form-item",{attrs:{label:"",prop:"examineStatus"}},[a("el-select",{attrs:{placeholder:"打款状态",clearable:""},model:{value:e.queryParams.examineStatus,callback:function(t){e.$set(e.queryParams,"examineStatus",t)},expression:"queryParams.examineStatus"}},e._l(e.examineStatusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vw_loan_compensationList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"customerName"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openUserInfo({customerId:t.row.customerId,applyId:t.row.applyId})}}},[e._v(" "+e._s(t.row.customerName)+" ")])]}}])}),a("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"orgName"}}),a("el-table-column",{attrs:{label:"业务员",align:"center",prop:"salesman"}}),a("el-table-column",{attrs:{label:"车牌号",align:"center",prop:"plateNo",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openCarInfo(t.row.plateNo)}}},[e._v(e._s(t.row.plateNo))])]}}])}),a("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"bank"}}),a("el-table-column",{attrs:{label:"代偿金额",align:"center",prop:"loanAmount"}}),a("el-table-column",{attrs:{label:"申请人",align:"center",prop:"createBy"}}),a("el-table-column",{attrs:{label:"申请时间",align:"center",prop:"createDate"}}),a("el-table-column",{attrs:{label:"审批时间",align:"center",prop:"approveTime",width:"120"}}),a("el-table-column",{attrs:{label:"打款状态",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.payoutsTime&&t.row.payouts?a("span",[e._v("已打款")]):a("span",[e._v("待打款")])]}}])}),a("el-table-column",{attrs:{label:"打款时间",align:"center",prop:"payoutsTime",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.payoutsTime?a("span",[e._v(e._s(t.row.payoutsTime.substr(0,10)))]):a("span")]}}])}),a("el-table-column",{attrs:{label:"实付金额",align:"center",prop:"payouts",width:"100"}}),a("el-table-column",{attrs:{label:"流水号",align:"center",prop:"kjjMoney",width:"100"}}),a("el-table-column",{attrs:{label:"风险金划扣金额",align:"center",prop:"kjczMoney",width:"110"}}),a("el-table-column",{attrs:{label:"实入日期",align:"center",prop:"sbczMoney",width:"110"}}),a("el-table-column",{attrs:{label:"实入金额",align:"center",prop:"overdueDays"}}),a("el-table-column",{attrs:{label:"流水号",align:"center",prop:"overdueDays"}}),a("el-table-column",{attrs:{label:"划扣日期",align:"center",prop:"overdueDays"}}),a("el-table-column",{attrs:{label:"渠道转入金额",align:"center",prop:"overdueDays"}}),a("el-table-column",{attrs:{label:"账号类型",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"实入日期",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"实入金额",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"流水号",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"广明借款金额",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"账号类型",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"实入日期",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"实入金额",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"流水号",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"科技出资金额",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"实入日期",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"实入金额",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"流水号",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"首邦出资金额",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"账号类型",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"实入日期",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"实入金额",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"流水号",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_loan_compensation:vw_loan_compensation:edit"],expression:"['vw_loan_compensation:vw_loan_compensation:edit']"}],staticStyle:{"margin-left":"10px"},attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleEntryRegistration(t.row)}}},[e._v(" 入账登记 ")]),t.row.payoutsTime&&t.row.payouts?e._e():a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_loan_compensation:vw_loan_compensation:edit"],expression:"['vw_loan_compensation:vw_loan_compensation:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handlePayment(t.row)}}},[e._v(" 打款 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:"入账登记",visible:e.openEntry,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.openEntry=t}}},[a("el-form",{ref:"form",staticStyle:{"text-align":"left"},attrs:{model:e.form,"label-width":"90px"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"金额类型"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择类型"},on:{change:e.handleTypeChange},model:{value:e.form.amountType,callback:function(t){e.$set(e.form,"amountType",t)},expression:"form.amountType"}},e._l(e.typeList,(function(e){return a("el-option",{key:e.label,attrs:{label:e.label,value:e.label}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"入账时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择入账时间","value-format":"yyyy-MM-dd"},model:{value:e.form.entryTime,callback:function(t){e.$set(e.form,"entryTime",t)},expression:"form.entryTime"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"应付金额"}},[a("el-input",{style:{background:"#f5f7fa","text-align":"right"},attrs:{placeholder:"自动带出",disabled:""},model:{value:e.form.amountDue,callback:function(t){e.$set(e.form,"amountDue",t)},expression:"form.amountDue"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"实付金额"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,precision:2,controls:!1,placeholder:"请输入实付金额","input-style":{"text-align":"right"}},on:{input:e.handleAmountChange},model:{value:e.form.actualAmount,callback:function(t){e.$set(e.form,"actualAmount",t)},expression:"form.actualAmount"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"差价"}},[a("el-input",{style:{background:"#f5f7fa",color:Number(e.form.difference)<0?"#f56c6c":"#606266","text-align":"right"},attrs:{placeholder:"自动计算",disabled:""},model:{value:e.form.difference,callback:function(t){e.$set(e.form,"difference",t)},expression:"form.difference"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"转入账号"}},[a("el-input",{style:{background:"#f5f7fa"},attrs:{placeholder:"自动带出",disabled:""},model:{value:e.form.transferAccount,callback:function(t){e.$set(e.form,"transferAccount",t)},expression:"form.transferAccount"}})],1)],1)],1),a("el-row",[a("el-col",{staticStyle:{"text-align":"right"},attrs:{span:24}},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:function(t){return e.cancel("openEntry")}}},[e._v("取 消")])],1)],1)],1)],1),a("el-dialog",{attrs:{title:"打款",visible:e.openPayment,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.openPayment=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"选择打款时间"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"请选择打款时间","value-format":"yyyy-MM-dd"},model:{value:e.form.payoutsTime,callback:function(t){e.$set(e.form,"payoutsTime",t)},expression:"form.payoutsTime"}})],1),a("el-form-item",{attrs:{label:"实付金额"}},[a("el-input-number",{attrs:{min:0,precision:2,controls:!1,placeholder:"请输入实付金额"},model:{value:e.form.payouts,callback:function(t){e.$set(e.form,"payouts",t)},expression:"form.payouts"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitPayment}},[e._v("确 定")]),a("el-button",{on:{click:function(t){return e.cancel("openPayment")}}},[e._v("取 消")])],1)],1),a("userInfo",{ref:"userInfo",attrs:{visible:e.userInfoVisible,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.userInfoVisible=t}}}),a("carInfo",{ref:"carInfo",attrs:{visible:e.carInfoVisible,title:"车辆信息",plateNo:e.plateNo,permission:"2"},on:{"update:visible":function(t){e.carInfoVisible=t}}})],1)},l=[],o=(a("7db0"),a("d81d"),a("a9e3"),a("d3b7"),a("0643"),a("fffc"),a("a573"),a("d96a")),r=a("bd52"),i=a("b775");function s(e){return Object(i["a"])({url:"/account_entry/account_entry",method:"post",data:e})}var u=a("2eca"),c=a("0f5f"),m={name:"Vw_loan_compensation_check",components:{userInfo:u["a"],carInfo:c["a"]},data:function(){return{loading:!1,showSearch:!0,total:0,vw_loan_compensationList:[],accountList:[],form:{},temp:{},openEntry:!1,openPayment:!1,title:"",queryParams:{pageNum:1,pageSize:10},lendingBankList:[{value:"EO00000010",label:"苏银金租"},{value:"IO00000006",label:"浙商银行"},{value:"IO00000007",label:"中关村银行"},{value:"IO00000008",label:"蓝海银行"},{value:"IO00000009",label:"华瑞银行"},{value:"IO00000010",label:"皖新租赁"}],examineStatusList:[{label:"待打款",value:0},{label:"已打款",value:1}],typeList:[{label:"风险金划扣金额已到账",value:"fxjMoney",account:"fxjAccount"},{label:"渠道转入金额已到账",value:"qdMoney",account:"qdAccount"},{label:"广明借款金额已到账",value:"gmjMoney",account:"gmjAccount"},{label:"科技借款",value:"kjjMoney",account:"kjjAccount"},{label:"科技出资",value:"kjczMoney",account:"kjczAccount"},{label:"首邦出资",value:"sbczMoney",account:"sbczAccount"}],customerInfo:{customerId:"",applyId:""},userInfoVisible:!1,plateNo:"",carInfoVisible:!1}},created:function(){this.getList(),this.getBankList()},methods:{getList:function(){var e=this;this.loading=!0,Object(o["e"])(this.queryParams).then((function(t){e.vw_loan_compensationList=t.rows||[],e.total=t.total,e.loading=!1}))},handleEntryRegistration:function(e){this.temp=e,this.form={amountDue:"",actualAmount:"",difference:"",transferAccount:"",entryTime:"",amountType:""},this.form.loanCompensationId=Number(e.id),this.form.loanId=e.loanId,this.openEntry=!0,this.title="入账登记"},handlePayment:function(e){this.temp=e,this.form={payoutsTime:"",payouts:""},this.form.id=Number(e.id),this.openPayment=!0,this.title="打款"},handleTypeChange:function(e){var t=this.typeList.find((function(t){return t.label===e})),a=t.value,n=t.account;this.temp&&void 0!==this.temp[a]?(this.form.amountDue=this.temp[a],this.form.transferAccount=this.temp[n]):(this.form.amountDue="",this.form.transferAccount=""),this.handleAmountChange()},handleAmountChange:function(){this.form.actualAmount&&this.form.amountDue?this.form.difference=Number(this.form.actualAmount)-Number(this.form.amountDue):this.form.difference=""},submitForm:function(){var e=this;this.form.amountType?this.form.entryTime?this.form.actualAmount?(console.log(this.form),s(this.form).then((function(t){e.$message.success("登记成功"),e.openEntry=!1,e.getList(),e.reset()}))):this.$message.error("实付金额不能为空"):this.$message.error("入账时间不能为空"):this.$message.error("入账金额类型不能为空")},submitPayment:function(){var e=this;this.form.payoutsTime?this.form.payouts||0===this.form.payouts?Object(o["f"])(this.form).then((function(t){e.$message.success("打款成功"),e.openPayment=!1,e.getList(),e.reset()})):this.$message.error("实付金额不能为空"):this.$message.error("打款时间不能为空")},cancel:function(e){this[e]=!1,this.reset()},reset:function(){this.form={},this.temp=null},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},getBankList:function(){var e=this;Object(r["i"])().then((function(t){e.accountList=t.rows}))},resetQuery:function(){this.queryParams={pageNum:1,pageSize:15,customerName:"",plateNo:"",salesman:"",orgName:"",bank:"",nickName:"",examineStatus:""},this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},openUserInfo:function(e){this.customerInfo=e,this.userInfoVisible=!0},openCarInfo:function(e){this.plateNo=e,this.carInfoVisible=!0}}},p=m,d=a("2877"),f=Object(d["a"])(p,n,l,!1,null,"1e7f0f4e",null);t["default"]=f.exports},"2eca":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[e._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},l=[],o=a("bd52"),r={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(o["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},i=r,s=(a("d6fd"),a("2877")),u=Object(s["a"])(i,n,l,!1,null,"8a3d4978",null);t["a"]=u.exports},bd52:function(e,t,a){"use strict";a.d(t,"l",(function(){return l})),a.d(t,"n",(function(){return o})),a.d(t,"h",(function(){return r})),a.d(t,"i",(function(){return i})),a.d(t,"o",(function(){return s})),a.d(t,"e",(function(){return u})),a.d(t,"s",(function(){return c})),a.d(t,"t",(function(){return m})),a.d(t,"a",(function(){return p})),a.d(t,"A",(function(){return d})),a.d(t,"g",(function(){return f})),a.d(t,"k",(function(){return b})),a.d(t,"w",(function(){return h})),a.d(t,"z",(function(){return y})),a.d(t,"f",(function(){return _})),a.d(t,"x",(function(){return v})),a.d(t,"c",(function(){return g})),a.d(t,"b",(function(){return w})),a.d(t,"v",(function(){return k})),a.d(t,"y",(function(){return I})),a.d(t,"j",(function(){return x})),a.d(t,"q",(function(){return S})),a.d(t,"B",(function(){return O})),a.d(t,"m",(function(){return j})),a.d(t,"r",(function(){return N})),a.d(t,"p",(function(){return P})),a.d(t,"d",(function(){return q})),a.d(t,"u",(function(){return L}));var n=a("b775");function l(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/list",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/listBySlippageStatus3",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/byLoanId/"+e,method:"get"})}function i(){return Object(n["a"])({url:"/bank_account/bank_account/bank?pageSize=30",method:"get"})}function s(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation/detail",method:"get",params:e})}function u(e){return Object(n["a"])({url:"/vw_car/vw_car/"+e,method:"get"})}function c(e){return Object(n["a"])({url:"/loan_reminder/loan_reminder/list",method:"get",params:e})}function m(e){return Object(n["a"])({url:"/loan_reminder/loan_reminder",method:"post",data:e})}function p(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan",method:"put",data:e})}function f(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/"+e,method:"delete"})}function b(e,t){return Object(n["a"])({url:"/vw_customer_info/vw_customer_info/"+e+"?applyNo="+t,method:"get"})}function h(e){return Object(n["a"])({url:"/loan_settle/loan_settle/detail",method:"get",params:e})}function y(e){return Object(n["a"])({url:"/trial_balance/trial_balance/submit",method:"post",data:e})}function _(e){return Object(n["a"])({url:"/trial_balance/trial_balance/submitdc",method:"post",data:e})}function v(e){return Object(n["a"])({url:"/loan_settle/loan_settle",method:"post",data:e})}function g(e,t){return Object(n["a"])({url:"/loan_settle/loan_settle/process",method:"post",data:{loanId:e,status:t}})}function w(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation/initiate",method:"post",data:e})}function k(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation",method:"put",data:e})}function I(e){return Object(n["a"])({url:"/loan_settle/loan_settle",method:"put",data:e})}function x(e){return Object(n["a"])({url:"/system/user/listByRoleId",method:"get",params:{roleId:e}})}function S(e){return Object(n["a"])({url:"/loan_list/loan_list/batchUpdateUrgeUser",method:"put",data:e})}function O(e){return Object(n["a"])({url:"/loan_list/loan_list",method:"put",data:e})}function j(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/extension_list",method:"get",params:e})}function N(e){return Object(n["a"])({url:"/loan_extension/loan_extension/extension_detail",method:"get",params:{loan_id:e}})}function P(e){return Object(n["a"])({url:"/loan_extension/loan_extension/approve",method:"put",data:e})}function q(e){return Object(n["a"])({url:"/loan_list/loan_list/batchAssignPetitionUser",method:"put",data:e})}function L(e){return Object(n["a"])({url:"/loan_list/loan_list/revokePetitionUser/".concat(e),method:"put"})}},d6fd:function(e,t,a){"use strict";a("1791")},d96a:function(e,t,a){"use strict";a.d(t,"d",(function(){return l})),a.d(t,"e",(function(){return o})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return i})),a.d(t,"f",(function(){return s})),a.d(t,"b",(function(){return u}));var n=a("b775");function l(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation/list",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/vw_loan_compensation/vw_loan_compensation/listGte3",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation/"+e,method:"get"})}function i(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation/initlate",method:"post",data:e})}function s(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation",method:"put",data:e})}function u(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation/"+e,method:"delete"})}}}]);