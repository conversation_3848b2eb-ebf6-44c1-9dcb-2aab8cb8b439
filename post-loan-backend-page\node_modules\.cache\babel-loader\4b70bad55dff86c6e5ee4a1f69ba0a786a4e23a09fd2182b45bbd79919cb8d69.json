{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es.array.map.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.map.js\");\nrequire(\"core-js/modules/esnext.iterator.some.js\");\nvar _litigation = require(\"@/api/litigation/litigation\");\nvar _loan_reminder = require(\"@/api/loan_reminder/loan_reminder\");\nvar _loanReminderLog = _interopRequireDefault(require(\"@/layout/components/Dialog/loanReminderLog.vue\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default2 = exports.default = {\n  name: 'LitigationLogView',\n  components: {\n    LoanReminderLog: _loanReminderLog.default\n  },\n  props: {\n    data: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  watch: {\n    data: {\n      handler: function handler(newVal) {\n        console.log('newVal:', newVal);\n        if (newVal && newVal.id) {\n          this.loadLitigationLogs(newVal.id, newVal.流程序号);\n        }\n      }\n    }\n  },\n  data: function data() {\n    return {\n      visible: false,\n      // 诉讼状态树结构\n      reminderLogLoanId: '',\n      litigationStatusTree: [{\n        label: '立案前',\n        value: '立案前',\n        children: [{\n          label: '准备资料',\n          value: '准备资料'\n        }, {\n          label: '已邮寄',\n          value: '撤案已邮寄'\n        }, {\n          label: '待立案',\n          value: '待立案'\n        }]\n      }, {\n        label: '立案-判决',\n        value: '立案-判决',\n        children: [{\n          label: '待出民初号',\n          value: '待出民初号'\n        }, {\n          label: '待开庭',\n          value: '待开庭'\n        }, {\n          label: '待出法院文书',\n          value: '待出法院文书'\n        }]\n      }, {\n        label: '判决-执行',\n        value: '判决-执行',\n        children: [{\n          label: '待出申请书',\n          value: '待出申请书'\n        }, {\n          label: '已提交执行书',\n          value: '已提交执行书'\n        }]\n      }, {\n        label: '执行后',\n        value: '执行后',\n        children: [{\n          label: '执行中',\n          value: '执行中'\n        }, {\n          label: '待送车',\n          value: '待送车'\n        }, {\n          label: '待法拍',\n          value: '待法拍'\n        }, {\n          label: '继续执行',\n          value: '继续执行'\n        }, {\n          label: '执行终本',\n          value: '执行终本'\n        }]\n      }, {\n        label: '结案',\n        value: '结案',\n        children: [{\n          label: '法诉减免结清',\n          value: '法诉减免结清'\n        }, {\n          label: '法诉全额结清',\n          value: '法诉全额结清'\n        }]\n      }, {\n        label: '撤案',\n        value: '撤案'\n      }],\n      // 当前激活的步骤索引\n      activeStep: 0,\n      logList: []\n    };\n  },\n  computed: {\n    // 从状态树中提取父节点的label作为进度条步骤\n    statusSteps: function statusSteps() {\n      return this.litigationStatusTree.map(function (item) {\n        return item.label;\n      });\n    }\n  },\n  methods: {\n    // 根据状态设置激活的步骤\n    setActiveStepByStatus: function setActiveStepByStatus(status) {\n      if (!status) {\n        this.activeStep = 0;\n        return;\n      }\n\n      // 查找状态对应的父节点索引\n      var parentIndex = this.findParentIndexByStatus(status);\n      this.activeStep = parentIndex >= 0 ? parentIndex : 0;\n    },\n    // 根据状态找到父节点的索引\n    findParentIndexByStatus: function findParentIndexByStatus(status) {\n      for (var i = 0; i < this.litigationStatusTree.length; i++) {\n        var item = this.litigationStatusTree[i];\n\n        // 如果是父节点本身\n        if (item.label === status || item.value === status) {\n          return i;\n        }\n\n        // 如果有子节点，在子节点中查找\n        if (item.children && item.children.length > 0) {\n          var childFound = item.children.some(function (child) {\n            return child.label === status || child.value === status;\n          });\n          if (childFound) {\n            return i;\n          }\n        }\n      }\n      return -1;\n    },\n    openDialog: function openDialog() {\n      this.visible = true;\n    },\n    handleUrgeLog: function handleUrgeLog() {\n      // 打开催记日志对话框，传入当前的 data\n      this.reminderLogLoanId = String(this.data.流程序号);\n      this.$refs.loanReminderLog.openLogDialog();\n    },\n    handleConfirm: function handleConfirm() {\n      return;\n    },\n    handleCancel: function handleCancel() {\n      this.visible = false;\n    }\n  }\n};", "map": {"version": 3, "names": ["_litigation", "require", "_loan_reminder", "_loanReminderLog", "_interopRequireDefault", "name", "components", "LoanReminderLog", "props", "data", "type", "Object", "default", "watch", "handler", "newVal", "console", "log", "id", "loadLitigationLogs", "流程序号", "visible", "reminderLogLoanId", "litigationStatusTree", "label", "value", "children", "activeStep", "logList", "computed", "statusSteps", "map", "item", "methods", "setActiveStepByStatus", "status", "parentIndex", "findParentIndexByStatus", "i", "length", "childFound", "some", "child", "openDialog", "handleUrgeLog", "String", "$refs", "loanReminderLog", "openLogDialog", "handleConfirm", "handleCancel"], "sources": ["src/views/litigation/litigation/modules/litigationLogView.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :visible.sync=\"visible\" title=\"日志查看\" width=\"800px\" @close=\"handleCancel\">\r\n      <!-- 进度条 -->\r\n      <el-steps :active=\"activeStep\" finish-status=\"success\" process-status=\"process\" align-center style=\"margin-bottom: 24px\" class=\"custom-steps\">\r\n        <el-step v-for=\"(item, idx) in statusSteps\" :key=\"idx\" :title=\"item\" />\r\n      </el-steps>\r\n\r\n      <!-- 日志表格 -->\r\n      <el-table :data=\"logList\" border style=\"width: 100%; margin-bottom: 24px\">\r\n        <el-table-column prop=\"createTime\" label=\"时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ formatDateTime(scope.row.createTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"createBy\" label=\"跟踪人\" width=\"120\" />\r\n        <el-table-column prop=\"status\" label=\"跟踪动作\" width=\"120\" />\r\n        <el-table-column label=\"文书信息\" width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.docName || scope.row.docNumber\">\r\n              <div v-if=\"scope.row.docName\" style=\"margin-bottom: 4px;\">\r\n                <el-tag size=\"mini\" type=\"info\">{{ scope.row.docName }}</el-tag>\r\n              </div>\r\n              <div v-if=\"scope.row.docNumber\" style=\"font-size: 12px; color: #666;\">\r\n                文书号：{{ scope.row.docNumber }}\r\n              </div>\r\n            </div>\r\n            <span v-else style=\"color: #999;\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"开庭时间\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.openDate\">{{ formatDate(scope.row.openDate) }}</span>\r\n            <span v-else style=\"color: #999;\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"文书生效时间\" width=\"140\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.docEffectiveDate\">{{ formatDateTime(scope.row.docEffectiveDate) }}</span>\r\n            <span v-else style=\"color: #999;\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"文书附件\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              v-if=\"scope.row.docUploadUrl\"\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"viewDocuments(scope.row.docUploadUrl)\"\r\n            >\r\n              查看附件\r\n            </el-button>\r\n            <span v-else style=\"color: #999;\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"urgeDescribe\" label=\"描述\" min-width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.urgeDescribe\">{{ scope.row.urgeDescribe }}</span>\r\n            <span v-else style=\"color: #999;\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleUrgeLog\">催记日志</el-button>\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确认</el-button>\r\n        <el-button @click=\"handleCancel\">取消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 催记日志组件 -->\r\n    <loan-reminder-log ref=\"loanReminderLog\" :loan-id=\"reminderLogLoanId\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listLitigation_log } from '@/api/litigation/litigation'\r\nimport { listLoan_reminder } from '@/api/loan_reminder/loan_reminder'\r\nimport LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'\r\n\r\nexport default {\r\n  name: 'LitigationLogView',\r\n  components: {\r\n    LoanReminderLog,\r\n  },\r\n  props: {\r\n    data: {\r\n      type: Object,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        console.log('newVal:', newVal)\r\n        if (newVal && newVal.id) {\r\n          this.loadLitigationLogs(newVal.id, newVal.流程序号)\r\n        }\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      // 诉讼状态树结构\r\n      reminderLogLoanId: '',\r\n      litigationStatusTree: [\r\n        {\r\n          label: '立案前',\r\n          value: '立案前',\r\n          children: [\r\n            { label: '准备资料', value: '准备资料' },\r\n            { label: '已邮寄', value: '撤案已邮寄' },\r\n            { label: '待立案', value: '待立案' },\r\n          ],\r\n        },\r\n        {\r\n          label: '立案-判决',\r\n          value: '立案-判决',\r\n          children: [\r\n            { label: '待出民初号', value: '待出民初号' },\r\n            { label: '待开庭', value: '待开庭' },\r\n            { label: '待出法院文书', value: '待出法院文书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '判决-执行',\r\n          value: '判决-执行',\r\n          children: [\r\n            { label: '待出申请书', value: '待出申请书' },\r\n            { label: '已提交执行书', value: '已提交执行书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '执行后',\r\n          value: '执行后',\r\n          children: [\r\n            { label: '执行中', value: '执行中' },\r\n            { label: '待送车', value: '待送车' },\r\n            { label: '待法拍', value: '待法拍' },\r\n            { label: '继续执行', value: '继续执行' },\r\n            { label: '执行终本', value: '执行终本' },\r\n          ],\r\n        },\r\n        {\r\n          label: '结案',\r\n          value: '结案',\r\n          children: [\r\n            { label: '法诉减免结清', value: '法诉减免结清' },\r\n            { label: '法诉全额结清', value: '法诉全额结清' },\r\n          ],\r\n        },\r\n        { label: '撤案', value: '撤案' },\r\n      ],\r\n      // 当前激活的步骤索引\r\n      activeStep: 0,\r\n      logList: [],\r\n    }\r\n  },\r\n  computed: {\r\n    // 从状态树中提取父节点的label作为进度条步骤\r\n    statusSteps() {\r\n      return this.litigationStatusTree.map(item => item.label)\r\n    },\r\n  },\r\n  methods: {\r\n    // 根据状态设置激活的步骤\r\n    setActiveStepByStatus(status) {\r\n      if (!status) {\r\n        this.activeStep = 0\r\n        return\r\n      }\r\n\r\n      // 查找状态对应的父节点索引\r\n      const parentIndex = this.findParentIndexByStatus(status)\r\n      this.activeStep = parentIndex >= 0 ? parentIndex : 0\r\n    },\r\n\r\n    // 根据状态找到父节点的索引\r\n    findParentIndexByStatus(status) {\r\n      for (let i = 0; i < this.litigationStatusTree.length; i++) {\r\n        const item = this.litigationStatusTree[i]\r\n\r\n        // 如果是父节点本身\r\n        if (item.label === status || item.value === status) {\r\n          return i\r\n        }\r\n\r\n        // 如果有子节点，在子节点中查找\r\n        if (item.children && item.children.length > 0) {\r\n          const childFound = item.children.some(child => child.label === status || child.value === status)\r\n          if (childFound) {\r\n            return i\r\n          }\r\n        }\r\n      }\r\n      return -1\r\n    },\r\n\r\n    openDialog() {\r\n      this.visible = true\r\n    },\r\n    handleUrgeLog() {\r\n      // 打开催记日志对话框，传入当前的 data\r\n      this.reminderLogLoanId = String(this.data.流程序号)\r\n      this.$refs.loanReminderLog.openLogDialog()\r\n    },\r\n    handleConfirm() {\r\n      return\r\n    },\r\n    handleCancel() {\r\n      this.visible = false\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 自定义步骤条样式 - 激活节点为蓝色 */\r\n::v-deep .custom-steps .el-step__head.is-process {\r\n  color: #409eff;\r\n  border-color: #409eff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__head.is-process .el-step__icon {\r\n  background-color: #409eff;\r\n  border-color: #409eff;\r\n  color: #fff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__title.is-process {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;AA4EA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,gBAAA,GAAAC,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAI,IAAA;EACAC,UAAA;IACAC,eAAA,EAAAA;EACA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,KAAA;IACAJ,IAAA;MACAK,OAAA,WAAAA,QAAAC,MAAA;QACAC,OAAA,CAAAC,GAAA,YAAAF,MAAA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAG,EAAA;UACA,KAAAC,kBAAA,CAAAJ,MAAA,CAAAG,EAAA,EAAAH,MAAA,CAAAK,IAAA;QACA;MACA;IACA;EACA;EACAX,IAAA,WAAAA,KAAA;IACA;MACAY,OAAA;MACA;MACAC,iBAAA;MACAC,oBAAA,GACA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACA;MACAE,UAAA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAP,oBAAA,CAAAQ,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAR,KAAA;MAAA;IACA;EACA;EACAS,OAAA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,MAAA;MACA,KAAAA,MAAA;QACA,KAAAR,UAAA;QACA;MACA;;MAEA;MACA,IAAAS,WAAA,QAAAC,uBAAA,CAAAF,MAAA;MACA,KAAAR,UAAA,GAAAS,WAAA,QAAAA,WAAA;IACA;IAEA;IACAC,uBAAA,WAAAA,wBAAAF,MAAA;MACA,SAAAG,CAAA,MAAAA,CAAA,QAAAf,oBAAA,CAAAgB,MAAA,EAAAD,CAAA;QACA,IAAAN,IAAA,QAAAT,oBAAA,CAAAe,CAAA;;QAEA;QACA,IAAAN,IAAA,CAAAR,KAAA,KAAAW,MAAA,IAAAH,IAAA,CAAAP,KAAA,KAAAU,MAAA;UACA,OAAAG,CAAA;QACA;;QAEA;QACA,IAAAN,IAAA,CAAAN,QAAA,IAAAM,IAAA,CAAAN,QAAA,CAAAa,MAAA;UACA,IAAAC,UAAA,GAAAR,IAAA,CAAAN,QAAA,CAAAe,IAAA,WAAAC,KAAA;YAAA,OAAAA,KAAA,CAAAlB,KAAA,KAAAW,MAAA,IAAAO,KAAA,CAAAjB,KAAA,KAAAU,MAAA;UAAA;UACA,IAAAK,UAAA;YACA,OAAAF,CAAA;UACA;QACA;MACA;MACA;IACA;IAEAK,UAAA,WAAAA,WAAA;MACA,KAAAtB,OAAA;IACA;IACAuB,aAAA,WAAAA,cAAA;MACA;MACA,KAAAtB,iBAAA,GAAAuB,MAAA,MAAApC,IAAA,CAAAW,IAAA;MACA,KAAA0B,KAAA,CAAAC,eAAA,CAAAC,aAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAA7B,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}