(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-49c94efe"],{"0f5f":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.carInfo.plateNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车架号"}},[e._v(e._s(e.carInfo.identityNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"发动机号"}},[e._v(e._s(e.carInfo.engineNumber||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(e._s(e.carStatusLabel))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆位置"}},[e._v(e._s(e.carInfo.carAddress||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(e._s(e.gpsStatusLabel))]):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看车牌照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"行驶证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看行驶证照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"登记证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看登记证照片")])],1):e._e()],1)],1)},l=[],r=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),a("bd52")),o={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var e=this,t=this.carStatusList.find((function(t){return t.value===e.carInfo.carStatus}));return t?t.label:"未知状态"},gpsStatusLabel:function(){var e=this,t=this.gpsStatusList.find((function(t){return t.value===e.carInfo.gpsStatus}));return t?t.label:"未知状态"}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,e&&this.plateNo&&Object(r["e"])(this.plateNo).then((function(e){t.carInfo=e.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},s=o,i=a("2877"),u=Object(i["a"])(s,n,l,!1,null,null,null);t["a"]=u.exports},1791:function(e,t,a){},"2eca":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[e._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},l=[],r=a("bd52"),o={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(r["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},s=o,i=(a("d6fd"),a("2877")),u=Object(i["a"])(s,n,l,!1,null,"8a3d4978",null);t["a"]=u.exports},b3c3:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人姓名、账户",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"certId"}},[a("el-input",{attrs:{placeholder:"贷款人身份证号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.certId,callback:function(t){e.$set(e.queryParams,"certId",t)},expression:"queryParams.certId"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"车牌号码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"nickName"}},[a("el-input",{attrs:{placeholder:"业务员",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.nickName,callback:function(t){e.$set(e.queryParams,"nickName",t)},expression:"queryParams.nickName"}})],1),a("el-form-item",{attrs:{label:"",prop:"jgName"}},[a("el-input",{attrs:{placeholder:"录单渠道名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jgName,callback:function(t){e.$set(e.queryParams,"jgName",t)},expression:"queryParams.jgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"repaymentStatus"}},[a("el-select",{attrs:{placeholder:"还款状态",clearable:""},model:{value:e.queryParams.repaymentStatus,callback:function(t){e.$set(e.queryParams,"repaymentStatus",t)},expression:"queryParams.repaymentStatus"}},e._l(e.repaymentList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"orgName"}},[a("el-select",{attrs:{placeholder:"放款银行",clearable:""},model:{value:e.queryParams.orgName,callback:function(t){e.$set(e.queryParams,"orgName",t)},expression:"queryParams.orgName"}},e._l(e.bankList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"productName"}},[a("el-input",{attrs:{placeholder:"产品名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.productName,callback:function(t){e.$set(e.queryParams,"productName",t)},expression:"queryParams.productName"}})],1),a("el-form-item",{attrs:{label:"还款时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.queryParams.allocationTime,callback:function(t){e.$set(e.queryParams,"allocationTime",t)},expression:"queryParams.allocationTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vw_loan_officeList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"序号",align:"center",width:"60",fixed:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"customerName"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openUserInfo({customerId:t.row.customerId,applyId:t.row.applyId})}}},[e._v(" "+e._s(t.row.customerName)+" ")])]}}])}),a("el-table-column",{attrs:{label:"业务员",align:"center",prop:"nickName"}}),a("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"jgName",width:"130px"}}),a("el-table-column",{attrs:{label:"车牌号码",align:"center",prop:"plateNo",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openCarInfo(t.row.plateNo)}}},[e._v(e._s(t.row.plateNo))])]}}])}),a("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"orgName",width:"100px"}}),a("el-table-column",{attrs:{label:"产品名称",align:"center",prop:"productName",width:"100px"}}),a("el-table-column",{attrs:{label:"剩余账单金额",align:"center",prop:"bremainingAmounts",width:"130px"}}),a("el-table-column",{attrs:{label:"剩余代扣金额",align:"center",prop:"dremainingAmounts",width:"100px"}}),a("el-table-column",{attrs:{label:"逾期金额",align:"center",width:"130px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s((t.row.boverdueAmount+t.row.doverdueAmount).toFixed(2)))])]}}])}),a("el-table-column",{attrs:{label:"本期金额",align:"center",width:"130px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s((t.row.bnowMoney+t.row.dnowMoney+t.row.boverdueAmount+t.row.doverdueAmount).toFixed(2)))])]}}])}),a("el-table-column",{attrs:{label:"还款状态",align:"center",prop:"repaymentStatus",width:"130px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(" "+e._s("1"==t.row.repaymentStatus?"还款中":"2"==t.row.repaymentStatus?"已完结":"3"==t.row.repaymentStatus?"提前结清":"4"==t.row.repaymentStatus?"逾期催回结清":"5"==t.row.repaymentStatus?"逾期减免结清":"6"==t.row.repaymentStatus?"逾期未还款":"7"==t.row.repaymentStatus?"逾期还款中":"8"==t.row.repaymentStatus?"代偿未还款":"9"==t.row.repaymentStatus?"代偿还款中":"10"==t.row.repaymentStatus?"代偿减免结清":"代偿全额结清")+" ")])]}}])}),a("el-table-column",{attrs:{label:"银行期次",align:"center",prop:"bcurrentPeriods"}}),a("el-table-column",{attrs:{label:"还款日",align:"center",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.brepaymentDate?a.brepaymentDate.substring(0,10):"")+" ")]}}])}),a("el-table-column",{attrs:{label:"实还银行金额",align:"center",prop:"realReturnMoney",width:"130px"}}),a("el-table-column",{attrs:{label:"银行实还时间",align:"center",prop:"breturnTime",width:"130px"}}),a("el-table-column",{attrs:{label:"代扣期次",align:"center",prop:"dcurrentPeriods"}}),a("el-table-column",{attrs:{label:"代扣实还时间",align:"center",prop:"lawTime",width:"130px"}}),a("el-table-column",{attrs:{label:"实还代扣金额",align:"center",prop:"dreturnTime",width:"130px"}}),a("el-table-column",{attrs:{label:"账号类型",align:"center",prop:"lawTime"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_account_loan:vw_account_loan:list"],expression:"['vw_account_loan:vw_account_loan:list']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.planBth(t.row)}}},[e._v("还款账单")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:"贷后文员列表",visible:e.dhopen,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.dhopen=t}}},[a("el-form",{ref:"dhForm",attrs:{model:e.dh,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"贷后文员",prop:"userName"}},[a("el-select",{attrs:{placeholder:"请选择贷后文员",clearable:""},on:{change:e.dhChange},model:{value:e.dh.userName,callback:function(t){e.$set(e.dh,"userName",t)},expression:"dh.userName"}},e._l(e.dhList,(function(e){return a("el-option",{key:e.nickName,attrs:{label:e.nickName,value:e.nickName}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitDh}},[e._v("确 定")]),a("el-button",{on:{click:e.canceldh}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"法诉文员列表",visible:e.fsopen,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.fsopen=t}}},[a("el-form",{ref:"fsForm",attrs:{model:e.fs,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"法诉文员",prop:"userName"}},[a("el-select",{attrs:{placeholder:"请选择法诉文员",clearable:""},model:{value:e.fs.userName,callback:function(t){e.$set(e.fs,"userName",t)},expression:"fs.userName"}},e._l(e.fsList,(function(e){return a("el-option",{key:e.roleId,attrs:{label:e.nickName,value:e.roleId}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitFs}},[e._v("确 定")]),a("el-button",{on:{click:e.cancelfs}},[e._v("取 消")])],1)],1),a("userInfo",{ref:"userInfo",attrs:{visible:e.userInfoVisible,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.userInfoVisible=t}}}),a("carInfo",{ref:"carInfo",attrs:{visible:e.carInfoVisible,title:"车辆信息",plateNo:e.plateNo,permission:"2"},on:{"update:visible":function(t){e.carInfoVisible=t}}})],1)},l=[],r=(a("d81d"),a("14d9"),a("a9e3"),a("d3b7"),a("0643"),a("a573"),a("b775"));function o(e){return Object(r["a"])({url:"/vw_account_loan/vw_account_loan/list",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/vw_loan_office/vw_loan_office",method:"post",data:e})}function i(e){return Object(r["a"])({url:"/vw_loan_office/vw_loan_office",method:"put",data:e})}var u=a("2eca"),c=a("0f5f"),m={name:"Vw_loan_office",components:{userInfo:u["a"],carInfo:c["a"]},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,vw_loan_officeList:[],title:"",open:!1,dhopen:!1,fsopen:!1,queryParams:{pageNum:1,pageSize:15,customerName:null,certId:null,plateNo:null,nickName:null,jgName:null,repaymentStatus:null,orgName:null,productName:null,allocationTime:null,startTime:null,endTime:null},form:{},rules:{},dhList:[],fsList:[],dh:{userName:null,loanId:null},fs:{userName:null,loanId:null},bankList:[{value:"EO00000010",label:"苏银金租"},{value:"IO00000006",label:"浙商银行"},{value:"IO00000007",label:"中关村银行"},{value:"IO00000008",label:"蓝海银行"},{value:"IO00000009",label:"华瑞银行"},{value:"IO00000010",label:"皖新租赁"}],repaymentList:[{value:"1",label:"还款中"},{value:"2",label:"已完结"},{value:"3",label:"提前结清"},{value:"4",label:"逾期催回结清"},{value:"5",label:"逾期减免结清"},{value:"6",label:"逾期未还款"},{value:"7",label:"逾期还款中"},{value:"8",label:"代偿未还款"},{value:"9",label:"代偿还款中"},{value:"10",label:"代偿减免结清"},{value:"11",label:"代偿全额结清"}],customerInfo:{customerId:"",applyId:""},userInfoVisible:!1,plateNo:"",carInfoVisible:!1}},created:function(){this.getList()},methods:{planBth:function(e){var t={partnerId:e.partnerId,applyId:e.applyId};"IO00000008"==e.partnerId?this.$router.push({path:"/repayment/repayment_plan/lhindex",query:t}):"EO00000010"==e.partnerId?this.$router.push({path:"/repayment/repayment_plan/syindex",query:t}):"IO00000006"==e.partnerId?this.$router.push({path:"/repayment/repayment_plan/zsindex",query:t}):"IO00000007"==e.partnerId?this.$router.push({path:"/repayment/repayment_plan/zgcindex",query:t}):"IO00000009"==e.partnerId?this.$router.push({path:"/repayment/repayment_plan/hrindex",query:t}):this.$router.push({path:"/repayment/repayment_plan/wxindex",query:t})},submitFs:function(){Array.isArray(this.fs.loanId)?this.fs.loanId=this.fs.loanId.map((function(e){return Number(e)})):this.fs.loanId=Number(this.fs.loanId)},submitDh:function(){Array.isArray(this.dh.loanId)?this.dh.loanId=this.dh.loanId.map((function(e){return Number(e)})):this.dh.loanId=Number(this.dh.loanId)},dhChange:function(e){this.dh.userName=e},cancelfs:function(){this.fsopen=!1},canceldh:function(){this.dhopen=!1,this.dh.userName=null,this.dh.loanId=null},getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.vw_loan_officeList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,urgeName:null,applyId:null,customerId:null,customerName:null,partnerId:null,orgId:null,dhUser:null,followUp:null,urgeUser:null,petitionUser:null,lawUser:null,period:null,realReturnMoney:null,reminderDate:null,isPetition:null,badDebt:null,status:null,billStatus:null,slippageStatus:null,followStatus:null,isExtension:null,extensionDate:null,delFlag:null,createBy:null,createTime:null,updateBy:null,updateTime:null,name:null,primaryPerson:null,deputyPerson:null,phone:null},this.resetForm("form")},handleQuery:function(){this.queryParams.allocationTime&&(this.queryParams.startTime=this.queryParams.allocationTime[0],this.queryParams.endTime=this.queryParams.allocationTime[1]),this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.customerName=null,this.queryParams.certId=null,this.queryParams.plateNo=null,this.queryParams.nickName=null,this.queryParams.jgName=null,this.queryParams.repaymentStatus=null,this.queryParams.orgName=null,this.queryParams.productName=null,this.queryParams.allocationTime=null,this.queryParams.startTime=null,this.queryParams.endTime=null,this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?i(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):s(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},openUserInfo:function(e){this.customerInfo=e,this.userInfoVisible=!0},openCarInfo:function(e){this.plateNo=e,this.carInfoVisible=!0}}},p=m,d=a("2877"),f=Object(d["a"])(p,n,l,!1,null,null,null);t["default"]=f.exports},bd52:function(e,t,a){"use strict";a.d(t,"l",(function(){return l})),a.d(t,"n",(function(){return r})),a.d(t,"h",(function(){return o})),a.d(t,"i",(function(){return s})),a.d(t,"o",(function(){return i})),a.d(t,"e",(function(){return u})),a.d(t,"s",(function(){return c})),a.d(t,"t",(function(){return m})),a.d(t,"a",(function(){return p})),a.d(t,"A",(function(){return d})),a.d(t,"g",(function(){return f})),a.d(t,"k",(function(){return b})),a.d(t,"w",(function(){return h})),a.d(t,"z",(function(){return y})),a.d(t,"f",(function(){return _})),a.d(t,"x",(function(){return v})),a.d(t,"c",(function(){return g})),a.d(t,"b",(function(){return w})),a.d(t,"v",(function(){return I})),a.d(t,"y",(function(){return N})),a.d(t,"j",(function(){return k})),a.d(t,"q",(function(){return x})),a.d(t,"B",(function(){return P})),a.d(t,"m",(function(){return S})),a.d(t,"r",(function(){return O})),a.d(t,"p",(function(){return q})),a.d(t,"d",(function(){return j})),a.d(t,"u",(function(){return L}));var n=a("b775");function l(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/list",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/listBySlippageStatus3",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/byLoanId/"+e,method:"get"})}function s(){return Object(n["a"])({url:"/bank_account/bank_account/bank?pageSize=30",method:"get"})}function i(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation/detail",method:"get",params:e})}function u(e){return Object(n["a"])({url:"/vw_car/vw_car/"+e,method:"get"})}function c(e){return Object(n["a"])({url:"/loan_reminder/loan_reminder/list",method:"get",params:e})}function m(e){return Object(n["a"])({url:"/loan_reminder/loan_reminder",method:"post",data:e})}function p(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan",method:"put",data:e})}function f(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/"+e,method:"delete"})}function b(e,t){return Object(n["a"])({url:"/vw_customer_info/vw_customer_info/"+e+"?applyNo="+t,method:"get"})}function h(e){return Object(n["a"])({url:"/loan_settle/loan_settle/detail",method:"get",params:e})}function y(e){return Object(n["a"])({url:"/trial_balance/trial_balance/submit",method:"post",data:e})}function _(e){return Object(n["a"])({url:"/trial_balance/trial_balance/submitdc",method:"post",data:e})}function v(e){return Object(n["a"])({url:"/loan_settle/loan_settle",method:"post",data:e})}function g(e,t){return Object(n["a"])({url:"/loan_settle/loan_settle/process",method:"post",data:{loanId:e,status:t}})}function w(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation/initiate",method:"post",data:e})}function I(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation",method:"put",data:e})}function N(e){return Object(n["a"])({url:"/loan_settle/loan_settle",method:"put",data:e})}function k(e){return Object(n["a"])({url:"/system/user/listByRoleId",method:"get",params:{roleId:e}})}function x(e){return Object(n["a"])({url:"/loan_list/loan_list/batchUpdateUrgeUser",method:"put",data:e})}function P(e){return Object(n["a"])({url:"/loan_list/loan_list",method:"put",data:e})}function S(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/extension_list",method:"get",params:e})}function O(e){return Object(n["a"])({url:"/loan_extension/loan_extension/extension_detail",method:"get",params:{loan_id:e}})}function q(e){return Object(n["a"])({url:"/loan_extension/loan_extension/approve",method:"put",data:e})}function j(e){return Object(n["a"])({url:"/loan_list/loan_list/batchAssignPetitionUser",method:"put",data:e})}function L(e){return Object(n["a"])({url:"/loan_list/loan_list/revokePetitionUser/".concat(e),method:"put"})}},d6fd:function(e,t,a){"use strict";a("1791")}}]);