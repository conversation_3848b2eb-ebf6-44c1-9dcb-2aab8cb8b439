{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogForm.vue", "mtime": 1754379158886}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgbGl0aWdhdGlvblN0YXR1cyBmcm9tICdAL2xheW91dC9jb21wb25lbnRzL0RpYWxvZy9saXRpZ2F0aW9uU3RhdHVzLnZ1ZScNCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAnQC91dGlscy9hdXRoJw0KaW1wb3J0IHsgc3VibWl0TGl0aWdhdGlvbkxvZyB9IGZyb20gJ0AvYXBpL2xpdGlnYXRpb24vbGl0aWdhdGlvbicNCmltcG9ydCB7IGFkZEluc3RhbGxtZW50X2FwcGxpY2F0aW9uX2F1ZGl0IH0gZnJvbSAnQC9hcGkvaW5zdGFsbG1lbnRfYXBwbGljYXRpb25fYXVkaXQvaW5zdGFsbG1lbnRfYXBwbGljYXRpb25fYXVkaXQnDQppbXBvcnQgeyBnZXRfYmFua19hY2NvdW50IH0gZnJvbSAnQC9hcGkvdndfYWNjb3VudF9sb2FuL3Z3X2FjY291bnRfbG9hbicNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnTGl0aWdhdGlvbkxvZ0Zvcm0nLA0KICBjb21wb25lbnRzOiB7DQogICAgbGl0aWdhdGlvblN0YXR1cywNCiAgfSwNCiAgcHJvcHM6IHsNCiAgICBhY3Rpb246IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcvY29tbW9uL29zc3VwbG9hZCcsDQogICAgfSwNCiAgICBkYXRhOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiB7fSwNCiAgICB9LA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB0aXRsZTogJ+aPkOS6pOazleivieaXpeW/lycsDQogICAgICB2aXNpYmxlOiBmYWxzZSwNCiAgICAgIGxvYW5SZW1pbmRlcjoge30sDQogICAgICBsaXRpZ2F0aW9uTG9nOiB7fSwNCiAgICAgIGluc3RhbGxtZW50Rm9ybTogew0KICAgICAgICBsb2FuSWQ6IG51bGwsDQogICAgICAgIGFwcGx5QW1vdW50OiAwLA0KICAgICAgICBwZXJpb2RDb3VudDogMSwNCiAgICAgICAgYmlsbEFtb3VudDogJzAuMDAnLA0KICAgICAgICB0YWlsQW1vdW50OiAwLA0KICAgICAgICByZXBheURheTogMSwNCiAgICAgICAgdGFpbFBheVRpbWU6IG51bGwsDQogICAgICAgIGFjY291bnRUeXBlOiAnJywNCiAgICAgICAgaW5zdGFsbG1lbnRTdGF0dXM6IDIgLy8gMi3ms5Xor4nliIbmnJ8NCiAgICAgIH0sDQogICAgICAvLyDliIbmnJ/ooajljZXpqozor4Hop4TliJkNCiAgICAgIGluc3RhbGxtZW50UnVsZXM6IHsNCiAgICAgICAgYXBwbHlBbW91bnQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl55Sz6K+35YiG5pyf6YeR6aKdJywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgICAgeyB0eXBlOiAnbnVtYmVyJywgbWluOiAwLjAxLCBtZXNzYWdlOiAn55Sz6K+35YiG5pyf6YeR6aKd5b+F6aG75aSn5LqOMCcsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgIHBlcmlvZENvdW50OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWIhuacn+acn+aVsCcsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgdHlwZTogJ251bWJlcicsIG1pbjogMSwgbWF4OiA2MCwgbWVzc2FnZTogJ+WIhuacn+acn+aVsOW/hemhu+WcqDEtNjDmnJ/kuYvpl7QnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICByZXBheURheTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nmr4/mnJ/ov5jmrL7ml6UnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0sDQogICAgICAgIGFjY291bnRUeXBlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqei0puWPt+exu+WeiycsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXSwNCiAgICAgICAgdGFpbFBheVRpbWU6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICB2YWxpZGF0b3I6IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgICAgICAgICAgaWYgKHRoaXMuaW5zdGFsbG1lbnRGb3JtLnRhaWxBbW91bnQgPiAwICYmICF2YWx1ZSkgew0KICAgICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign5pyJ5bC+5qy+5pe25b+F6aG76YCJ5oup5bC+5qy+5pSv5LuY5pe26Ze0JykpDQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgY2FsbGJhY2soKQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgdHJpZ2dlcjogJ2NoYW5nZScNCiAgICAgICAgICB9DQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICB1cGxvYWRVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyB0aGlzLmFjdGlvbiwNCiAgICAgIGhlYWRlcnM6IHsNCiAgICAgICAgQXV0aG9yaXphdGlvbjogJ0JlYXJlciAnICsgZ2V0VG9rZW4oKSwNCiAgICAgIH0sDQogICAgICBkaWFsb2dJbWFnZVVybDogJycsDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgZGF0YTogew0KICAgICAgaGFuZGxlcihuZXdWYWwpIHsNCiAgICAgICAgaWYgKG5ld1ZhbCkgew0KICAgICAgICAgIGNvbnNvbGUubG9nKCduZXdWYWwnLCBuZXdWYWwpDQogICAgICAgICAgdGhpcy5sb2FuUmVtaW5kZXIgPSB7DQogICAgICAgICAgICBsb2FuSWQ6IG5ld1ZhbC7mtYHnqIvluo/lj7csDQogICAgICAgICAgICBjdXN0b21lck5hbWU6IG5ld1ZhbC7otLfmrL7kurosDQogICAgICAgICAgICBjaGFubmVsOiBuZXdWYWwu5Ye65Y2V5rig6YGTLA0KICAgICAgICAgICAgYmFuazogbmV3VmFsLuaUvuasvumTtuihjCwNCiAgICAgICAgICAgIGlkZW50aXR5OiB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnJvbGVzWzBdLA0KICAgICAgICAgICAgcmVwYXltZW50U3RhdHVzOiAnJywNCiAgICAgICAgICAgIGZ1bmRzUmVwYXltZW50OiAnJywNCiAgICAgICAgICAgIGZ1bmRzQW1vdW50OiAnJywNCiAgICAgICAgICAgIGZ1bmRzSW1hZ2U6IFtdLA0KICAgICAgICAgICAgZnVuZHNBY2NvdW50VHlwZTogJycsDQogICAgICAgICAgICBhY2NvdW50TnVtYmVyOiAnJywNCiAgICAgICAgICAgIHVyZ2VTdGF0dXM6ICcnLA0KICAgICAgICAgICAgdHJhY2tpbmdUaW1lOiAnJywNCiAgICAgICAgICAgIHVyZ2VEZXNjcmliZTogJycsDQogICAgICAgICAgICBzdGF0dXM6IDIsDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMubGl0aWdhdGlvbkxvZyA9IHsNCiAgICAgICAgICAgIGxvYW5JZDogbmV3VmFsLua1geeoi+W6j+WPtywNCiAgICAgICAgICAgIGxpdGlnYXRpb25JZDogbmV3VmFsLuW6j+WPtywNCiAgICAgICAgICAgIGRvY05hbWU6ICcnLA0KICAgICAgICAgICAgZG9jTnVtYmVyOiAnJywNCiAgICAgICAgICAgIGRvY1VwbG9hZFVybDogW10sDQogICAgICAgICAgICBkb2NFZmZlY3RpdmVEYXRlOiAnJywNCiAgICAgICAgICAgIG9wZW5EYXRlOiAnJywNCiAgICAgICAgICAgIHN0YXR1czogJycsDQogICAgICAgICAgfQ0KICAgICAgICAgIC8vIOmHjee9ruWIhuacn+ihqOWNlQ0KICAgICAgICAgIHRoaXMuaW5zdGFsbG1lbnRGb3JtID0gew0KICAgICAgICAgICAgbG9hbklkOiBuZXdWYWwgJiYgbmV3VmFsLua1geeoi+W6j+WPtyA/IG5ld1ZhbC7mtYHnqIvluo/lj7cgOiBudWxsLA0KICAgICAgICAgICAgYXBwbHlBbW91bnQ6IDAsDQogICAgICAgICAgICBwZXJpb2RDb3VudDogMSwNCiAgICAgICAgICAgIGJpbGxBbW91bnQ6ICcwLjAwJywNCiAgICAgICAgICAgIHRhaWxBbW91bnQ6IDAsDQogICAgICAgICAgICByZXBheURheTogMSwNCiAgICAgICAgICAgIHRhaWxQYXlUaW1lOiBudWxsLA0KICAgICAgICAgICAgYWNjb3VudFR5cGU6ICcnLA0KICAgICAgICAgICAgaW5zdGFsbG1lbnRTdGF0dXM6IDIgLy8gMi3ms5Xor4nliIbmnJ8NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICBpbW1lZGlhdGU6IHRydWUsDQogICAgICBkZWVwOiB0cnVlLA0KICAgIH0sDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDlpITnkIbov5jmrL7nsbvlnovlj5jljJYNCiAgICBoYW5kbGVSZXBheW1lbnRUeXBlQ2hhbmdlKHZhbHVlKSB7DQogICAgICBpZiAodmFsdWUgPT09ICczJykgew0KICAgICAgICAvLyDpgInmi6nliIbmnJ/ov5jmrL7ml7bvvIzmuIXnqbrlhbbku5bov5jmrL7kv6Hmga8NCiAgICAgICAgdGhpcy5sb2FuUmVtaW5kZXIuZnVuZHNSZXBheW1lbnQgPSAnJw0KICAgICAgICB0aGlzLmxvYW5SZW1pbmRlci5mdW5kc0Ftb3VudCA9ICcnDQogICAgICAgIHRoaXMubG9hblJlbWluZGVyLmZ1bmRzQWNjb3VudFR5cGUgPSAnJw0KICAgICAgICB0aGlzLmxvYW5SZW1pbmRlci5hY2NvdW50TnVtYmVyID0gJycNCiAgICAgICAgdGhpcy5sb2FuUmVtaW5kZXIuZnVuZHNJbWFnZSA9IFtdDQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDpgInmi6nlhbbku5bov5jmrL7nsbvlnovml7bvvIzmuIXnqbrliIbmnJ/ooajljZXpqozor4HnirbmgIENCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIGlmICh0aGlzLiRyZWZzLmluc3RhbGxtZW50Rm9ybVJlZikgew0KICAgICAgICAgICAgdGhpcy4kcmVmcy5pbnN0YWxsbWVudEZvcm1SZWYuY2xlYXJWYWxpZGF0ZSgpDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDliIbmnJ/ooajljZXorqHnrpfmlrnms5UNCiAgICBoYW5kbGVJbnN0YWxsbWVudEZvcm1DaGFuZ2UoKSB7DQogICAgICBjb25zdCBhcHBseUFtb3VudCA9IE51bWJlcih0aGlzLmluc3RhbGxtZW50Rm9ybS5hcHBseUFtb3VudCkgfHwgMA0KICAgICAgY29uc3QgcGVyaW9kQ291bnQgPSBOdW1iZXIodGhpcy5pbnN0YWxsbWVudEZvcm0ucGVyaW9kQ291bnQpIHx8IDENCiAgICAgIGNvbnN0IHRhaWxBbW91bnQgPSBOdW1iZXIodGhpcy5pbnN0YWxsbWVudEZvcm0udGFpbEFtb3VudCkgfHwgMA0KICAgICAgaWYgKGFwcGx5QW1vdW50ID49IDAgJiYgcGVyaW9kQ291bnQgPj0gMSkgew0KICAgICAgICB0aGlzLmluc3RhbGxtZW50Rm9ybS5iaWxsQW1vdW50ID0gKChhcHBseUFtb3VudCAtIHRhaWxBbW91bnQpIC8gcGVyaW9kQ291bnQpLnRvRml4ZWQoMikNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuaW5zdGFsbG1lbnRGb3JtLmJpbGxBbW91bnQgPSAnMC4wMCcNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOmAmueUqOeahOS4iuS8oOaIkOWKn+WkhOeQhuWHveaVsA0KICAgIGhhbmRsZVVwbG9hZFN1Y2Nlc3MocmVzLCBmaWxlLCBmaWxlTGlzdCwgZm9ybUZpZWxkKSB7DQogICAgICBjb25zdCBbb2JqLCBwcm9wXSA9IGZvcm1GaWVsZC5zcGxpdCgnLicpDQogICAgICB0aGlzW29ial1bcHJvcF0gPSBmaWxlTGlzdA0KICAgIH0sDQogICAgLy8g6YCa55So55qE5Yig6Zmk5aSE55CG5Ye95pWwDQogICAgaGFuZGxlUmVtb3ZlKGZpbGUsIGZpbGVMaXN0LCBmb3JtRmllbGQpIHsNCiAgICAgIGNvbnN0IFtvYmosIHByb3BdID0gZm9ybUZpZWxkLnNwbGl0KCcuJykNCiAgICAgIHRoaXNbb2JqXVtwcm9wXSA9IGZpbGVMaXN0DQogICAgfSwNCiAgICAvLyDkuIrkvKDlpLHotKUNCiAgICBoYW5kbGVVcGxvYWRFcnJvcigpIHsNCiAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfkuIrkvKDlpLHotKXvvIzor7fph43or5UnKQ0KICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCkNCiAgICB9LA0KICAgIC8vIOWbvueJh+mihOiniA0KICAgIGhhbmRsZVBpY3R1cmVDYXJkUHJldmlldyhmaWxlKSB7DQogICAgICB0aGlzLmRpYWxvZ0ltYWdlVXJsID0gZmlsZS51cmwNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTooajljZUgKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgY29uc3QgbG9hblJlbWluZGVyQ29weSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5sb2FuUmVtaW5kZXIpKQ0KICAgICAgY29uc3QgbGl0aWdhdGlvbkxvZ0NvcHkgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMubGl0aWdhdGlvbkxvZykpDQogICAgICBsb2FuUmVtaW5kZXJDb3B5LmZ1bmRzSW1hZ2UgPSBsb2FuUmVtaW5kZXJDb3B5LmZ1bmRzSW1hZ2UubWFwKGl0ZW0gPT4gaXRlbS5yZXNwb25zZSkuam9pbignLCcpDQogICAgICBsaXRpZ2F0aW9uTG9nQ29weS5kb2NVcGxvYWRVcmwgPSBsaXRpZ2F0aW9uTG9nQ29weS5kb2NVcGxvYWRVcmwubWFwKGl0ZW0gPT4gaXRlbS5yZXNwb25zZSkuam9pbignLCcpDQogICAgICBsb2FuUmVtaW5kZXJDb3B5LmZ1bmRzQWNjb3VudFR5cGUgPQ0KICAgICAgICBsb2FuUmVtaW5kZXJDb3B5LmZ1bmRzQWNjb3VudFR5cGUgPT09ICflhbbku5YnID8gbG9hblJlbWluZGVyQ29weS5hY2NvdW50TnVtYmVyIDogbG9hblJlbWluZGVyQ29weS5mdW5kc0FjY291bnRUeXBlDQogICAgICAvLyDlsIbml6Xlv5fmj4/ov7Dku44gbG9hblJlbWluZGVyIOWkjeWItuWIsCBsaXRpZ2F0aW9uTG9nDQogICAgICBsaXRpZ2F0aW9uTG9nQ29weS51cmdlRGVzY3JpYmUgPSBsb2FuUmVtaW5kZXJDb3B5LnVyZ2VEZXNjcmliZQ0KDQogICAgICAvLyDlpoLmnpzpgInmi6nkuobliIbmnJ/ov5jmrL7vvIzlhYjmj5DkuqTliIbmnJ/nlLPor7cNCiAgICAgIGlmIChsb2FuUmVtaW5kZXJDb3B5LnJlcGF5bWVudFN0YXR1cyA9PT0gJzMnKSB7DQogICAgICAgIC8vIOaYvuekuuehruiupOWvueivneahhg0KICAgICAgICBjb25zdCBjb25maXJtTWVzc2FnZSA9IGDnoa7orqTmj5DkuqTliIbmnJ/nlLPor7fvvJ9cbueUs+ivt+mHkemine+8miR7dGhpcy5pbnN0YWxsbWVudEZvcm0uYXBwbHlBbW91bnR95YWDXG7liIbmnJ/mnJ/mlbDvvJoke3RoaXMuaW5zdGFsbG1lbnRGb3JtLnBlcmlvZENvdW50feacn1xu5q+P5pyf6YeR6aKd77yaJHt0aGlzLmluc3RhbGxtZW50Rm9ybS5iaWxsQW1vdW50feWFg2ANCiAgICAgICAgdGhpcy4kY29uZmlybShjb25maXJtTWVzc2FnZSwgJ+ehruiupOWIhuacn+eUs+ivtycsIHsNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumuaPkOS6pCcsDQogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuc3VibWl0SW5zdGFsbG1lbnRBcHBsaWNhdGlvbigpLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgLy8g5YiG5pyf55Sz6K+35o+Q5Lqk5oiQ5Yqf5ZCO77yM5YaN5o+Q5Lqk5pel5b+XDQogICAgICAgICAgICB0aGlzLnN1Ym1pdExpdGlnYXRpb25Mb2dEYXRhKGxvYW5SZW1pbmRlckNvcHksIGxpdGlnYXRpb25Mb2dDb3B5KQ0KICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfliIbmnJ/nlLPor7fmj5DkuqTlpLHotKUnKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICAvLyDnlKjmiLflj5bmtojkuobmk43kvZwNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOebtOaOpeaPkOS6pOaXpeW/lw0KICAgICAgICB0aGlzLnN1Ym1pdExpdGlnYXRpb25Mb2dEYXRhKGxvYW5SZW1pbmRlckNvcHksIGxpdGlnYXRpb25Mb2dDb3B5KQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5o+Q5Lqk5YiG5pyf55Sz6K+3ICovDQogICAgc3VibWl0SW5zdGFsbG1lbnRBcHBsaWNhdGlvbigpIHsNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7DQogICAgICAgIC8vIOS9v+eUqEVsZW1lbnQgVUnooajljZXpqozor4ENCiAgICAgICAgdGhpcy4kcmVmcy5pbnN0YWxsbWVudEZvcm1SZWYudmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgICAgaWYgKCF2YWxpZCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K+35a6M5ZaE5YiG5pyf55Sz6K+35L+h5oGvJykNCiAgICAgICAgICAgIHJlamVjdCgpDQogICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDpop3lpJbnmoTkuJrliqHpqozor4ENCiAgICAgICAgICBpZiAoIXRoaXMuaW5zdGFsbG1lbnRGb3JtLmxvYW5JZCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6LS35qy+SUTkuI3og73kuLrnqbrvvIzor7fph43mlrDmiZPlvIDooajljZUnKQ0KICAgICAgICAgICAgcmVqZWN0KCkNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOmqjOivgeavj+acn+i0puWNlemHkemineaYr+WQpuWQiOeQhg0KICAgICAgICAgIGNvbnN0IGJpbGxBbW91bnQgPSBOdW1iZXIodGhpcy5pbnN0YWxsbWVudEZvcm0uYmlsbEFtb3VudCkNCiAgICAgICAgICBpZiAoYmlsbEFtb3VudCA8PSAwKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmr4/mnJ/otKbljZXph5Hpop3lv4XpobvlpKfkuo4w77yM6K+35qOA5p+l55Sz6K+36YeR6aKd5ZKM5pyf5pWwJykNCiAgICAgICAgICAgIHJlamVjdCgpDQogICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDpqozor4HlsL7mrL7pgLvovpENCiAgICAgICAgICBpZiAodGhpcy5pbnN0YWxsbWVudEZvcm0udGFpbEFtb3VudCA+IDAgJiYgIXRoaXMuaW5zdGFsbG1lbnRGb3JtLnRhaWxQYXlUaW1lKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCforr7nva7kuoblsL7mrL7ph5Hpop3ml7bvvIzlv4XpobvpgInmi6nlsL7mrL7mlK/ku5jml7bpl7QnKQ0KICAgICAgICAgICAgcmVqZWN0KCkNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGNvbnNvbGUubG9nKCfmj5DkuqTliIbmnJ/nlLPor7fmlbDmja7vvJonLCB0aGlzLmluc3RhbGxtZW50Rm9ybSkNCg0KICAgICAgICAgIC8vIOiwg+eUqOWIhuacn+eUs+ivt0FQSe+8iOS4juS7o+WBv+WIhuacn+S9v+eUqOebuOWQjOeahEFQSe+8iQ0KICAgICAgICAgIGFkZEluc3RhbGxtZW50X2FwcGxpY2F0aW9uX2F1ZGl0KHRoaXMuaW5zdGFsbG1lbnRGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5YiG5pyf55Sz6K+35o+Q5Lqk5oiQ5YqfJykNCiAgICAgICAgICAgICAgcmVzb2x2ZSgpDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign5YiG5pyf55Sz6K+35o+Q5Lqk5aSx6LSl77yaJyArIChyZXNwb25zZS5tc2cgfHwgJ+acquefpemUmeivrycpKQ0KICAgICAgICAgICAgICByZWplY3QoKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIhuacn+eUs+ivt+aPkOS6pOWksei0pTonLCBlcnJvcikNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfliIbmnJ/nlLPor7fmj5DkuqTlpLHotKXvvIzor7fnqI3lkI7ph43or5UnKQ0KICAgICAgICAgICAgcmVqZWN0KCkNCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLyoqIOaPkOS6pOazleivieaXpeW/l+aVsOaNriAqLw0KICAgIHN1Ym1pdExpdGlnYXRpb25Mb2dEYXRhKGxvYW5SZW1pbmRlckNvcHksIGxpdGlnYXRpb25Mb2dDb3B5KSB7DQogICAgICBjb25zb2xlLmxvZygn5o+Q5Lqk6KGo5Y2V5pWw5o2u77yaJywgbG9hblJlbWluZGVyQ29weSkNCiAgICAgIGNvbnNvbGUubG9nKCfmj5DkuqTooajljZXmlbDmja7vvJonLCBsaXRpZ2F0aW9uTG9nQ29weSkNCiAgICAgIHN1Ym1pdExpdGlnYXRpb25Mb2coeyBsb2FuUmVtaW5kZXI6IGxvYW5SZW1pbmRlckNvcHksIGxpdGlnYXRpb25Mb2c6IGxpdGlnYXRpb25Mb2dDb3B5IH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5o+Q5Lqk5oiQ5YqfJykNCiAgICAgICAgdGhpcy52aXNpYmxlID0gZmFsc2UNCiAgICAgICAgdGhpcy5yZXNldEZvcm0oKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDlj5bmtojmk43kvZwgKi8NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLnZpc2libGUgPSBmYWxzZQ0KICAgICAgdGhpcy5yZXNldEZvcm0oKQ0KICAgICAgcmV0dXJuDQogICAgfSwNCiAgICAvKiog6YeN572u6KGo5Y2VICovDQogICAgcmVzZXRGb3JtKCkgew0KICAgICAgdGhpcy5sb2FuUmVtaW5kZXIgPSB7DQogICAgICAgIGZ1bmRzSW1hZ2U6IFtdLA0KICAgICAgfQ0KICAgICAgdGhpcy5saXRpZ2F0aW9uTG9nID0gew0KICAgICAgICBkb2NVcGxvYWRVcmw6IFtdLA0KICAgICAgfQ0KICAgICAgdGhpcy5pbnN0YWxsbWVudEZvcm0gPSB7DQogICAgICAgIGxvYW5JZDogbnVsbCwNCiAgICAgICAgYXBwbHlBbW91bnQ6IDAsDQogICAgICAgIHBlcmlvZENvdW50OiAxLA0KICAgICAgICBiaWxsQW1vdW50OiAnMC4wMCcsDQogICAgICAgIHRhaWxBbW91bnQ6IDAsDQogICAgICAgIHJlcGF5RGF5OiAxLA0KICAgICAgICB0YWlsUGF5VGltZTogbnVsbCwNCiAgICAgICAgYWNjb3VudFR5cGU6ICcnLA0KICAgICAgICBpbnN0YWxsbWVudFN0YXR1czogMiAvLyAyLeazleivieWIhuacnw0KICAgICAgfQ0KICAgICAgLy8g6YeN572u5YiG5pyf6KGo5Y2V6aqM6K+B54q25oCBDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIGlmICh0aGlzLiRyZWZzLmluc3RhbGxtZW50Rm9ybVJlZikgew0KICAgICAgICAgIHRoaXMuJHJlZnMuaW5zdGFsbG1lbnRGb3JtUmVmLmNsZWFyVmFsaWRhdGUoKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOe7n+S4gOaJk+W8gOW8ueeql+eahOaWueazlSAqLw0KICAgIG9wZW5EaWFsb2coKSB7DQogICAgICB0aGlzLnZpc2libGUgPSB0cnVlDQogICAgICByZXR1cm4NCiAgICB9LA0KICAgIC8qKiDlpITnkIbmlofku7botoXlh7rpmZDliLYgKi8NCiAgICBoYW5kbGVFeGNlZWQoZmlsZXMsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+WPquiDveS4iuS8oOS4gOS4quaWh+S7ticpDQogICAgICByZXR1cm4NCiAgICB9LA0KICB9LA0KfQ0K"}, {"version": 3, "sources": ["litigationLogForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmUA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "litigationLogForm.vue", "sourceRoot": "src/views/litigation/litigation/modules", "sourcesContent": ["<template>\r\n  <el-dialog :title=\"title\" :visible.sync=\"visible\" width=\"800px\" append-to-body @close=\"resetForm\">\r\n    <el-form ref=\"form\" :model=\"loanReminder\" label-width=\"120px\">\r\n      <!-- 第一部分：文书相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-document\"></i>\r\n        贷款信息\r\n      </el-divider>\r\n      <!-- 非填入字段 -->\r\n      <el-descriptions title=\"\" :column=\"3\" border>\r\n        <el-descriptions-item label=\"贷款人\">\r\n          {{ loanReminder.customerName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"出单渠道\">\r\n          {{ loanReminder.channel }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"放款银行\">\r\n          {{ loanReminder.bank }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <!-- 第一部分：文书相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-document\"></i>\r\n        文书信息\r\n      </el-divider>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"变更法诉状态\">\r\n            <litigation-status v-model=\"litigationLog.status\" placeholder=\"请选择法诉状态\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"文书名称\">\r\n            <el-select v-model=\"litigationLog.docName\" placeholder=\"请选择文书名称\" style=\"width: 100%\">\r\n              <el-option label=\"诉前调号\" value=\"诉前调号\" />\r\n              <el-option label=\"民初号\" value=\"民初号\" />\r\n              <el-option label=\"执行号\" value=\"执行号\" />\r\n              <el-option label=\"执保号\" value=\"执保号\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"文书号\">\r\n            <el-input v-model=\"litigationLog.docNumber\" placeholder=\"请输入文书号\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"文书生效\">\r\n            <el-date-picker v-model=\"litigationLog.docEffectiveDate\" type=\"date\" placeholder=\"选择日期\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\" v-if=\"litigationLog.status === '待出法院文书'\">\r\n          <el-form-item label=\"登记开庭时间\">\r\n            <el-date-picker v-model=\"litigationLog.openDate\" type=\"datetime\" placeholder=\"选择开庭时间\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"上传文书\">\r\n            <el-upload\r\n              :data=\"data\"\r\n              :action=\"uploadUrl\"\r\n              :headers=\"headers\"\r\n              :limit=\"1\"\r\n              :file-list=\"litigationLog.docUploadUrl\"\r\n              :on-success=\"(res, file, fileList) => handleUploadSuccess(res, file, fileList, 'litigationLog.docUploadUrl')\"\r\n              :on-remove=\"(file, fileList) => handleRemove(file, fileList, 'litigationLog.docUploadUrl')\"\r\n              :on-error=\"handleUploadError\">\r\n              <el-button size=\"small\" type=\"primary\" :disabled=\"litigationLog.docUploadUrl.length >= 1\">点击上传</el-button>\r\n            </el-upload>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第二部分：还款相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-money\"></i>\r\n        还款信息\r\n      </el-divider>\r\n\r\n      <!-- 还款类型选择 -->\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"还款类型\">\r\n            <el-select v-model=\"loanReminder.repaymentStatus\" placeholder=\"请选择还款类型\" style=\"width: 100%\" @change=\"handleRepaymentTypeChange\">\r\n              <el-option label=\"部分还款\" value=\"2\" />\r\n              <el-option label=\"分期还款\" value=\"3\" />\r\n              <el-option label=\"协商买车\" value=\"4\" />\r\n              <el-option label=\"法诉结清\" value=\"5\" />\r\n              <el-option label=\"法诉减免结清\" value=\"6\" />\r\n              <el-option label=\"拍卖回款\" value=\"7\" />\r\n              <el-option label=\"法院划扣\" value=\"8\" />\r\n              <el-option label=\"其他分配回款\" value=\"9\" />\r\n            </el-select>\r\n            <div v-if=\"loanReminder.repaymentStatus === '3'\" class=\"form-tip\" style=\"color: #409EFF;\">\r\n              <i class=\"el-icon-info\"></i> 已选择分期还款，请填写下方分期申请信息\r\n            </div>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 分期还款表单 -->\r\n      <div v-if=\"loanReminder.repaymentStatus === '3'\" class=\"installment-form-container\">\r\n        <div class=\"installment-form-header\">\r\n          <i class=\"el-icon-s-order\"></i>\r\n          <span>分期申请详情</span>\r\n        </div>\r\n        <el-form ref=\"installmentFormRef\" :model=\"installmentForm\" :rules=\"installmentRules\" label-width=\"120px\" class=\"installment-form\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"申请分期金额\" prop=\"applyAmount\" required>\r\n                <el-input-number\r\n                  v-model=\"installmentForm.applyAmount\"\r\n                  :min=\"0.01\"\r\n                  :max=\"999999999\"\r\n                  :precision=\"2\"\r\n                  :step=\"100\"\r\n                  :controls-position=\"'right'\"\r\n                  placeholder=\"请输入申请分期金额\"\r\n                  @input=\"handleInstallmentFormChange\"\r\n                  style=\"width: 100%\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"分期期数\" prop=\"periodCount\" required>\r\n                <el-input-number\r\n                  v-model=\"installmentForm.periodCount\"\r\n                  :min=\"1\"\r\n                  :max=\"60\"\r\n                  :precision=\"0\"\r\n                  :step=\"1\"\r\n                  :controls-position=\"'right'\"\r\n                  placeholder=\"请输入分期期数\"\r\n                  @input=\"handleInstallmentFormChange\"\r\n                  style=\"width: 100%\" />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"每期账单金额\">\r\n                <el-input v-model=\"installmentForm.billAmount\" placeholder=\"自动计算\" disabled />\r\n                <div class=\"form-tip\">\r\n                  根据申请金额和期数自动计算\r\n                  <span v-if=\"installmentForm.applyAmount > 0 && installmentForm.periodCount > 0\">\r\n                    <br>计算公式：({{ installmentForm.applyAmount }} - {{ installmentForm.tailAmount || 0 }}) ÷ {{ installmentForm.periodCount }} = {{ installmentForm.billAmount }}元/期\r\n                  </span>\r\n                </div>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"每期还款日\" prop=\"repayDay\" required>\r\n                <el-select v-model=\"installmentForm.repayDay\" placeholder=\"请选择每期还款日\" style=\"width: 100%\">\r\n                  <el-option v-for=\"day in 28\" :key=\"day\" :label=\"`每月${day}号`\" :value=\"day\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"尾款金额\">\r\n                <el-input-number\r\n                  v-model=\"installmentForm.tailAmount\"\r\n                  :min=\"0\"\r\n                  :precision=\"2\"\r\n                  :step=\"100\"\r\n                  :controls-position=\"'right'\"\r\n                  placeholder=\"请输入尾款金额（可选）\"\r\n                  @input=\"handleInstallmentFormChange\"\r\n                  style=\"width: 100%\" />\r\n                <div class=\"form-tip\">可选，如有尾款请填写</div>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"尾款支付时间\" :prop=\"installmentForm.tailAmount > 0 ? 'tailPayTime' : ''\">\r\n                <el-date-picker\r\n                  v-model=\"installmentForm.tailPayTime\"\r\n                  type=\"date\"\r\n                  placeholder=\"选择尾款支付时间\"\r\n                  :disabled=\"!installmentForm.tailAmount || installmentForm.tailAmount <= 0\"\r\n                  style=\"width: 100%\" />\r\n                <div class=\"form-tip\">有尾款时必填</div>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"账号类型\" prop=\"accountType\" required>\r\n                <el-select v-model=\"installmentForm.accountType\" placeholder=\"请选择账号类型\" style=\"width: 100%\">\r\n                  <el-option label=\"银行账户\" value=\"银行账户\" />\r\n                  <el-option label=\"微信\" value=\"微信\" />\r\n                  <el-option label=\"支付宝\" value=\"支付宝\" />\r\n                  <el-option label=\"其他\" value=\"其他\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 其他还款类型的表单 -->\r\n      <div v-else-if=\"loanReminder.repaymentStatus && loanReminder.repaymentStatus !== '3'\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"款项明细类型\">\r\n              <el-select v-model=\"loanReminder.fundsRepayment\" placeholder=\"请选择款项明细类型\" style=\"width: 100%\">\r\n                <el-option label=\"律师费\" value=\"律师费\" />\r\n                <el-option label=\"法诉费\" value=\"法诉费\" />\r\n                <el-option label=\"保全费\" value=\"保全费\" />\r\n                <el-option label=\"布控费\" value=\"布控费\" />\r\n                <el-option label=\"公告费\" value=\"公告费\" />\r\n                <el-option label=\"评估费\" value=\"评估费\" />\r\n                <el-option label=\"执行费\" value=\"执行费\" />\r\n                <el-option label=\"违约金\" value=\"违约金\" />\r\n                <el-option label=\"担保费\" value=\"担保费\" />\r\n                <el-option label=\"居间费\" value=\"居间费\" />\r\n                <el-option label=\"代偿金\" value=\"代偿金\" />\r\n                <el-option label=\"判决金额\" value=\"判决金额\" />\r\n                <el-option label=\"利息\" value=\"利息\" />\r\n                <el-option label=\"其他欠款\" value=\"其他欠款\" />\r\n                <el-option label=\"保险费\" value=\"保险费\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"金额\">\r\n              <el-input-number v-model=\"loanReminder.fundsAmount\" :min=\"0\" :precision=\"2\" style=\"width: 100%\" placeholder=\"请输入金额\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\">\r\n              <el-select v-model=\"loanReminder.fundsAccountType\" placeholder=\"请选择账号类型\" style=\"width: 100%\">\r\n                <el-option label=\"银行账户\" value=\"银行账户\" />\r\n                <el-option label=\"微信\" value=\"微信\" />\r\n                <el-option label=\"支付宝\" value=\"支付宝\" />\r\n                <el-option label=\"其他\" value=\"其他\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号\">\r\n              <el-input v-model=\"loanReminder.accountNumber\" :disabled=\"loanReminder.fundsAccountType !== '其他'\" placeholder=\"请输入账号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"还款凭据\">\r\n              <el-upload\r\n                :data=\"data\"\r\n                :action=\"uploadUrl\"\r\n                :headers=\"headers\"\r\n                list-type=\"picture-card\"\r\n                :file-list=\"loanReminder.fundsImage\"\r\n                :on-preview=\"handlePictureCardPreview\"\r\n                :on-success=\"(res, file, fileList) => handleUploadSuccess(res, file, fileList, 'loanReminder.fundsImage')\"\r\n                :on-remove=\"(file, fileList) => handleRemove(file, fileList, 'loanReminder.fundsImage')\"\r\n                :on-error=\"handleUploadError\">\r\n                <i class=\"el-icon-plus\"></i>\r\n              </el-upload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n\r\n\r\n      <!-- 第三部分：日志相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-notebook-2\"></i>\r\n        日志信息\r\n      </el-divider>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"日志类型\">\r\n            <el-select v-model=\"loanReminder.urgeStatus\" placeholder=\"请选择日志类型\" style=\"width: 100%\">\r\n              <el-option label=\"继续跟踪\" value=\"1\" />\r\n              <el-option label=\"约定还款\" value=\"2\" />\r\n              <el-option label=\"无法跟进\" value=\"3\" />\r\n              <el-option label=\"暂时无需跟进\" value=\"4\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"下次跟进时间\">\r\n            <el-date-picker v-model=\"loanReminder.trackingTime\" type=\"datetime\" placeholder=\"选择跟进时间\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"日志描述\">\r\n            <el-input v-model=\"loanReminder.urgeDescribe\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入日志描述\" maxlength=\"500\" show-word-limit />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"cancel\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n    </div>\r\n    <!-- 图片预览 -->\r\n    <el-dialog :visible.sync=\"dialogVisible\">\r\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt=\"\" />\r\n    </el-dialog>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport litigationStatus from '@/layout/components/Dialog/litigationStatus.vue'\r\nimport { getToken } from '@/utils/auth'\r\nimport { submitLitigationLog } from '@/api/litigation/litigation'\r\nimport { addInstallment_application_audit } from '@/api/installment_application_audit/installment_application_audit'\r\nimport { get_bank_account } from '@/api/vw_account_loan/vw_account_loan'\r\n\r\nexport default {\r\n  name: 'LitigationLogForm',\r\n  components: {\r\n    litigationStatus,\r\n  },\r\n  props: {\r\n    action: {\r\n      type: String,\r\n      default: '/common/ossupload',\r\n    },\r\n    data: {\r\n      type: Object,\r\n      default: () => {},\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      title: '提交法诉日志',\r\n      visible: false,\r\n      loanReminder: {},\r\n      litigationLog: {},\r\n      installmentForm: {\r\n        loanId: null,\r\n        applyAmount: 0,\r\n        periodCount: 1,\r\n        billAmount: '0.00',\r\n        tailAmount: 0,\r\n        repayDay: 1,\r\n        tailPayTime: null,\r\n        accountType: '',\r\n        installmentStatus: 2 // 2-法诉分期\r\n      },\r\n      // 分期表单验证规则\r\n      installmentRules: {\r\n        applyAmount: [\r\n          { required: true, message: '请输入申请分期金额', trigger: 'blur' },\r\n          { type: 'number', min: 0.01, message: '申请分期金额必须大于0', trigger: 'blur' }\r\n        ],\r\n        periodCount: [\r\n          { required: true, message: '请输入分期期数', trigger: 'blur' },\r\n          { type: 'number', min: 1, max: 60, message: '分期期数必须在1-60期之间', trigger: 'blur' }\r\n        ],\r\n        repayDay: [\r\n          { required: true, message: '请选择每期还款日', trigger: 'change' }\r\n        ],\r\n        accountType: [\r\n          { required: true, message: '请选择账号类型', trigger: 'change' }\r\n        ],\r\n        tailPayTime: [\r\n          {\r\n            validator: (rule, value, callback) => {\r\n              if (this.installmentForm.tailAmount > 0 && !value) {\r\n                callback(new Error('有尾款时必须选择尾款支付时间'))\r\n              } else {\r\n                callback()\r\n              }\r\n            },\r\n            trigger: 'change'\r\n          }\r\n        ]\r\n      },\r\n      uploadUrl: process.env.VUE_APP_BASE_API + this.action,\r\n      headers: {\r\n        Authorization: 'Bearer ' + getToken(),\r\n      },\r\n      dialogImageUrl: '',\r\n      dialogVisible: false,\r\n    }\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          console.log('newVal', newVal)\r\n          this.loanReminder = {\r\n            loanId: newVal.流程序号,\r\n            customerName: newVal.贷款人,\r\n            channel: newVal.出单渠道,\r\n            bank: newVal.放款银行,\r\n            identity: this.$store.state.user.roles[0],\r\n            repaymentStatus: '',\r\n            fundsRepayment: '',\r\n            fundsAmount: '',\r\n            fundsImage: [],\r\n            fundsAccountType: '',\r\n            accountNumber: '',\r\n            urgeStatus: '',\r\n            trackingTime: '',\r\n            urgeDescribe: '',\r\n            status: 2,\r\n          }\r\n          this.litigationLog = {\r\n            loanId: newVal.流程序号,\r\n            litigationId: newVal.序号,\r\n            docName: '',\r\n            docNumber: '',\r\n            docUploadUrl: [],\r\n            docEffectiveDate: '',\r\n            openDate: '',\r\n            status: '',\r\n          }\r\n          // 重置分期表单\r\n          this.installmentForm = {\r\n            loanId: newVal && newVal.流程序号 ? newVal.流程序号 : null,\r\n            applyAmount: 0,\r\n            periodCount: 1,\r\n            billAmount: '0.00',\r\n            tailAmount: 0,\r\n            repayDay: 1,\r\n            tailPayTime: null,\r\n            accountType: '',\r\n            installmentStatus: 2 // 2-法诉分期\r\n          }\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true,\r\n    },\r\n  },\r\n  methods: {\r\n    // 处理还款类型变化\r\n    handleRepaymentTypeChange(value) {\r\n      if (value === '3') {\r\n        // 选择分期还款时，清空其他还款信息\r\n        this.loanReminder.fundsRepayment = ''\r\n        this.loanReminder.fundsAmount = ''\r\n        this.loanReminder.fundsAccountType = ''\r\n        this.loanReminder.accountNumber = ''\r\n        this.loanReminder.fundsImage = []\r\n      } else {\r\n        // 选择其他还款类型时，清空分期表单验证状态\r\n        this.$nextTick(() => {\r\n          if (this.$refs.installmentFormRef) {\r\n            this.$refs.installmentFormRef.clearValidate()\r\n          }\r\n        })\r\n      }\r\n    },\r\n\r\n    // 分期表单计算方法\r\n    handleInstallmentFormChange() {\r\n      const applyAmount = Number(this.installmentForm.applyAmount) || 0\r\n      const periodCount = Number(this.installmentForm.periodCount) || 1\r\n      const tailAmount = Number(this.installmentForm.tailAmount) || 0\r\n      if (applyAmount >= 0 && periodCount >= 1) {\r\n        this.installmentForm.billAmount = ((applyAmount - tailAmount) / periodCount).toFixed(2)\r\n      } else {\r\n        this.installmentForm.billAmount = '0.00'\r\n      }\r\n    },\r\n    // 通用的上传成功处理函数\r\n    handleUploadSuccess(res, file, fileList, formField) {\r\n      const [obj, prop] = formField.split('.')\r\n      this[obj][prop] = fileList\r\n    },\r\n    // 通用的删除处理函数\r\n    handleRemove(file, fileList, formField) {\r\n      const [obj, prop] = formField.split('.')\r\n      this[obj][prop] = fileList\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError('上传失败，请重试')\r\n      this.$modal.closeLoading()\r\n    },\r\n    // 图片预览\r\n    handlePictureCardPreview(file) {\r\n      this.dialogImageUrl = file.url\r\n      this.dialogVisible = true\r\n    },\r\n    /** 提交表单 */\r\n    submitForm() {\r\n      const loanReminderCopy = JSON.parse(JSON.stringify(this.loanReminder))\r\n      const litigationLogCopy = JSON.parse(JSON.stringify(this.litigationLog))\r\n      loanReminderCopy.fundsImage = loanReminderCopy.fundsImage.map(item => item.response).join(',')\r\n      litigationLogCopy.docUploadUrl = litigationLogCopy.docUploadUrl.map(item => item.response).join(',')\r\n      loanReminderCopy.fundsAccountType =\r\n        loanReminderCopy.fundsAccountType === '其他' ? loanReminderCopy.accountNumber : loanReminderCopy.fundsAccountType\r\n      // 将日志描述从 loanReminder 复制到 litigationLog\r\n      litigationLogCopy.urgeDescribe = loanReminderCopy.urgeDescribe\r\n\r\n      // 如果选择了分期还款，先提交分期申请\r\n      if (loanReminderCopy.repaymentStatus === '3') {\r\n        // 显示确认对话框\r\n        const confirmMessage = `确认提交分期申请？\\n申请金额：${this.installmentForm.applyAmount}元\\n分期期数：${this.installmentForm.periodCount}期\\n每期金额：${this.installmentForm.billAmount}元`\r\n        this.$confirm(confirmMessage, '确认分期申请', {\r\n          confirmButtonText: '确定提交',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.submitInstallmentApplication().then(() => {\r\n            // 分期申请提交成功后，再提交日志\r\n            this.submitLitigationLogData(loanReminderCopy, litigationLogCopy)\r\n          }).catch(() => {\r\n            this.$modal.msgError('分期申请提交失败')\r\n          })\r\n        }).catch(() => {\r\n          // 用户取消了操作\r\n        })\r\n      } else {\r\n        // 直接提交日志\r\n        this.submitLitigationLogData(loanReminderCopy, litigationLogCopy)\r\n      }\r\n    },\r\n\r\n    /** 提交分期申请 */\r\n    submitInstallmentApplication() {\r\n      return new Promise((resolve, reject) => {\r\n        // 使用Element UI表单验证\r\n        this.$refs.installmentFormRef.validate((valid) => {\r\n          if (!valid) {\r\n            this.$message.error('请完善分期申请信息')\r\n            reject()\r\n            return\r\n          }\r\n\r\n          // 额外的业务验证\r\n          if (!this.installmentForm.loanId) {\r\n            this.$message.error('贷款ID不能为空，请重新打开表单')\r\n            reject()\r\n            return\r\n          }\r\n\r\n          // 验证每期账单金额是否合理\r\n          const billAmount = Number(this.installmentForm.billAmount)\r\n          if (billAmount <= 0) {\r\n            this.$message.error('每期账单金额必须大于0，请检查申请金额和期数')\r\n            reject()\r\n            return\r\n          }\r\n\r\n          // 验证尾款逻辑\r\n          if (this.installmentForm.tailAmount > 0 && !this.installmentForm.tailPayTime) {\r\n            this.$message.error('设置了尾款金额时，必须选择尾款支付时间')\r\n            reject()\r\n            return\r\n          }\r\n\r\n          console.log('提交分期申请数据：', this.installmentForm)\r\n\r\n          // 调用分期申请API（与代偿分期使用相同的API）\r\n          addInstallment_application_audit(this.installmentForm).then(response => {\r\n            if (response.code === 200) {\r\n              this.$modal.msgSuccess('分期申请提交成功')\r\n              resolve()\r\n            } else {\r\n              this.$modal.msgError('分期申请提交失败：' + (response.msg || '未知错误'))\r\n              reject()\r\n            }\r\n          }).catch(error => {\r\n            console.error('分期申请提交失败:', error)\r\n            this.$modal.msgError('分期申请提交失败，请稍后重试')\r\n            reject()\r\n          })\r\n        })\r\n      })\r\n    },\r\n\r\n    /** 提交法诉日志数据 */\r\n    submitLitigationLogData(loanReminderCopy, litigationLogCopy) {\r\n      console.log('提交表单数据：', loanReminderCopy)\r\n      console.log('提交表单数据：', litigationLogCopy)\r\n      submitLitigationLog({ loanReminder: loanReminderCopy, litigationLog: litigationLogCopy }).then(res => {\r\n        this.$modal.msgSuccess('提交成功')\r\n        this.visible = false\r\n        this.resetForm()\r\n      })\r\n    },\r\n    /** 取消操作 */\r\n    cancel() {\r\n      this.visible = false\r\n      this.resetForm()\r\n      return\r\n    },\r\n    /** 重置表单 */\r\n    resetForm() {\r\n      this.loanReminder = {\r\n        fundsImage: [],\r\n      }\r\n      this.litigationLog = {\r\n        docUploadUrl: [],\r\n      }\r\n      this.installmentForm = {\r\n        loanId: null,\r\n        applyAmount: 0,\r\n        periodCount: 1,\r\n        billAmount: '0.00',\r\n        tailAmount: 0,\r\n        repayDay: 1,\r\n        tailPayTime: null,\r\n        accountType: '',\r\n        installmentStatus: 2 // 2-法诉分期\r\n      }\r\n      // 重置分期表单验证状态\r\n      this.$nextTick(() => {\r\n        if (this.$refs.installmentFormRef) {\r\n          this.$refs.installmentFormRef.clearValidate()\r\n        }\r\n      })\r\n    },\r\n    /** 统一打开弹窗的方法 */\r\n    openDialog() {\r\n      this.visible = true\r\n      return\r\n    },\r\n    /** 处理文件超出限制 */\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning('只能上传一个文件')\r\n      return\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.el-divider__text {\r\n  padding: 0 15px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.el-upload {\r\n  width: 100%;\r\n}\r\n\r\n/* 分期表单容器样式 */\r\n.installment-form-container {\r\n  margin-top: 20px;\r\n  border: 2px solid #409EFF;\r\n  border-radius: 8px;\r\n  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);\r\n  overflow: hidden;\r\n}\r\n\r\n.installment-form-header {\r\n  background: #409EFF;\r\n  color: white;\r\n  padding: 12px 20px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.installment-form-header i {\r\n  font-size: 18px;\r\n}\r\n\r\n.installment-form {\r\n  padding: 20px;\r\n  background: white;\r\n  margin: 0;\r\n  border: none;\r\n}\r\n\r\n/* 分期表单样式 */\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 4px;\r\n  line-height: 1.2;\r\n}\r\n\r\n/* 必填项标识 */\r\n.el-form-item.is-required .el-form-item__label::before {\r\n  content: '*';\r\n  color: #f56c6c;\r\n  margin-right: 4px;\r\n}\r\n\r\n/* 分期表单区域样式 */\r\n.installment-form .el-row {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 禁用状态的输入框样式 */\r\n.el-input.is-disabled .el-input__inner {\r\n  background-color: #f5f7fa;\r\n  border-color: #e4e7ed;\r\n  color: #c0c4cc;\r\n}\r\n\r\n/* 其他还款表单样式 */\r\n.el-form:not(.installment-form) .el-row {\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n"]}]}