(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2965c15c"],{"0bef":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-dialog",{attrs:{title:"提交法诉费用",visible:e.dialogVisible,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("el-descriptions",{staticStyle:{"margin-bottom":"20px"},attrs:{column:1,border:""}},[i("el-descriptions-item",{attrs:{label:"贷款人"}},[e._v(e._s(e.litigationFee.贷款人))]),i("el-descriptions-item",{attrs:{label:"出单渠道"}},[e._v(e._s(e.litigationFee.出单渠道))]),i("el-descriptions-item",{attrs:{label:"放款银行"}},[e._v(e._s(e.litigationFee.放款银行))])],1),i("div",{staticStyle:{padding:"0 20px"}},[i("div",{staticClass:"fee-rules-container"},[i("div",{staticClass:"fee-rules-header"},[i("i",{staticClass:"el-icon-info"}),i("span",{staticClass:"fee-rules-title"},[e._v("费用提交规则")])]),i("div",{staticClass:"fee-rules-content"},[i("div",{staticClass:"rule-item"},[i("span",{staticClass:"rule-bullet"},[e._v("•")]),i("span",[i("strong",[e._v("判决金额")]),e._v("和"),i("strong",[e._v("利息")]),e._v("每个案件只能提交一次")])]),i("div",{staticClass:"rule-item"},[i("span",{staticClass:"rule-bullet"},[e._v("•")]),i("span",[i("strong",[e._v("日常费用")]),e._v('（如油费、路费、餐费等）请在"日常费用审批"模块中提交')])]),i("div",{staticClass:"rule-item"},[i("span",{staticClass:"rule-bullet"},[e._v("•")]),i("span",[e._v("其他诉讼费用可以多次提交")])])])]),i("div",{staticStyle:{"margin-bottom":"20px"}},[i("div",{staticStyle:{display:"flex","align-items":"flex-start"}},[i("label",{staticStyle:{width:"120px","text-align":"right","margin-right":"10px","font-weight":"bold","line-height":"32px","flex-shrink":"0"}},[e._v("法诉费用：")]),i("div",{staticStyle:{flex:"1"}},[e._l(e.litigationFees,(function(t,a){return i("div",{key:a,staticStyle:{"margin-bottom":"10px"}},[i("el-row",{attrs:{gutter:10,type:"flex",align:"middle"}},[i("el-col",{attrs:{span:8}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择法诉费用类型"},on:{change:function(i){return e.handleLitigationFeeTypeChange(t,a)}},model:{value:t.type,callback:function(i){e.$set(t,"type",i)},expression:"item.type"}},e._l(e.litigationFeeTypeOptions,(function(t){return i("el-option",{key:t.value,attrs:{label:t.label,value:t.value,disabled:e.isTypeDisabled(t.value,a)}})})),1)],1),i("el-col",{attrs:{span:8}},[i("el-input",{attrs:{placeholder:"请输入金额",disabled:""===t.type},on:{input:e.handleLitigationFeeAmountChange},model:{value:t.amount,callback:function(i){e.$set(t,"amount",i)},expression:"item.amount"}},[i("template",{slot:"prepend"},[e._v("￥")])],2)],1),i("el-col",{attrs:{span:4}},[i("el-button",{staticStyle:{width:"54px"},attrs:{type:"danger",icon:"el-icon-delete"},on:{click:function(t){return e.removeLitigationFee(a)}}})],1)],1)],1)})),i("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:e.addLitigationFee}},[e._v("新增法诉费用")]),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"success",size:"small",icon:"el-icon-document"},on:{click:e.openDailyExpenseDialog}},[e._v("申请日常费用")]),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"info",size:"small",icon:"el-icon-view"},on:{click:e.viewDailyExpenseList}},[e._v("查看日常费用")])],2)])]),i("div",{staticStyle:{"margin-bottom":"20px"}},[i("label",{staticStyle:{display:"inline-block",width:"120px","text-align":"right","margin-right":"10px","font-weight":"bold"}},[e._v("合计法诉费用：")]),i("div",{staticStyle:{display:"inline-block",width:"calc(100% - 130px)"}},[i("el-input",{attrs:{placeholder:"请输入合计法诉费用",disabled:""},model:{value:e.litigationFee.totalMoney,callback:function(t){e.$set(e.litigationFee,"totalMoney",t)},expression:"litigationFee.totalMoney"}},[i("template",{slot:"prepend"},[e._v("￥")])],2)],1)])]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),i("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),i("el-dialog",{attrs:{title:"申请日常费用",visible:e.dailyExpenseDialogVisible,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.dailyExpenseDialogVisible=t}}},[i("el-form",{ref:"dailyExpenseForm",attrs:{model:e.dailyExpenseForm,rules:e.dailyExpenseRules,"label-width":"120px"}},[i("el-form-item",{attrs:{label:"费用类型",prop:"expenseType"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择费用类型"},model:{value:e.dailyExpenseForm.expenseType,callback:function(t){e.$set(e.dailyExpenseForm,"expenseType",t)},expression:"dailyExpenseForm.expenseType"}},[i("el-option",{attrs:{label:"油费",value:"oil_fee"}}),i("el-option",{attrs:{label:"路费",value:"road_fee"}}),i("el-option",{attrs:{label:"餐费",value:"meal_fee"}}),i("el-option",{attrs:{label:"住宿费",value:"accommodation_fee"}}),i("el-option",{attrs:{label:"交通费",value:"transport_fee"}}),i("el-option",{attrs:{label:"停车费",value:"parking_fee"}}),i("el-option",{attrs:{label:"通讯费",value:"communication_fee"}}),i("el-option",{attrs:{label:"其他",value:"other"}})],1)],1),i("el-form-item",{attrs:{label:"费用金额",prop:"expenseAmount"}},[i("el-input",{attrs:{placeholder:"请输入费用金额"},model:{value:e.dailyExpenseForm.expenseAmount,callback:function(t){e.$set(e.dailyExpenseForm,"expenseAmount",t)},expression:"dailyExpenseForm.expenseAmount"}},[i("template",{slot:"prepend"},[e._v("￥")])],2)],1),i("el-form-item",{attrs:{label:"费用发生日期",prop:"expenseDate"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"请选择费用发生日期","value-format":"yyyy-MM-dd"},model:{value:e.dailyExpenseForm.expenseDate,callback:function(t){e.$set(e.dailyExpenseForm,"expenseDate",t)},expression:"dailyExpenseForm.expenseDate"}})],1),i("el-form-item",{attrs:{label:"费用说明",prop:"expenseDescription"}},[i("el-input",{attrs:{type:"textarea",placeholder:"请输入费用说明"},model:{value:e.dailyExpenseForm.expenseDescription,callback:function(t){e.$set(e.dailyExpenseForm,"expenseDescription",t)},expression:"dailyExpenseForm.expenseDescription"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:e.cancelDailyExpense}},[e._v("取 消")]),i("el-button",{attrs:{type:"primary"},on:{click:e.submitDailyExpense}},[e._v("确 定")])],1)],1),i("el-dialog",{attrs:{title:"日常费用申请记录",visible:e.dailyExpenseListDialogVisible,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.dailyExpenseListDialogVisible=t}}},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dailyExpenseListLoading,expression:"dailyExpenseListLoading"}],attrs:{data:e.dailyExpenseList}},[i("el-table-column",{attrs:{label:"费用类型",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getExpenseTypeLabel(t.row.expenseType))+" ")]}}])}),i("el-table-column",{attrs:{label:"费用金额",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ￥"+e._s(t.row.expenseAmount)+" ")]}}])}),i("el-table-column",{attrs:{label:"费用发生日期",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.expenseDate)+" ")]}}])}),i("el-table-column",{attrs:{label:"申请时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.applicationTime)+" ")]}}])}),i("el-table-column",{attrs:{label:"审批状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-tag",{attrs:{type:e.getStatusTagType(t.row.approvalStatus)}},[e._v(" "+e._s(e.getStatusText(t.row.approvalStatus))+" ")])]}}])}),i("el-table-column",{attrs:{label:"费用说明",align:"center",prop:"expenseDescription"}})],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.dailyExpenseListDialogVisible=!1}}},[e._v("关 闭")])],1)],1)],1)},l=[],n=i("5530"),s=(i("4de4"),i("7db0"),i("caad"),i("14d9"),i("a434"),i("b0c0"),i("a9e3"),i("d3b7"),i("2532"),i("0643"),i("2382"),i("fffc"),i("4e3e"),i("9d4a"),i("9a9a"),i("159b"),i("413c")),o=i("a6de"),r={name:"LitigationFeeForm",props:{data:{type:Object,default:function(){return{}}}},watch:{data:{handler:function(e){if(e){var t=e.序号;this.litigationFee={litigationCaseId:t?Number(t):null,"贷款人":String(e.贷款人||""),"出单渠道":String(e.出单渠道||""),"放款银行":String(e.放款银行||""),totalMoney:0},this.litigationFees=[],t&&this.checkSubmittedLimitedTypes(t)}},immediate:!0,deep:!0}},data:function(){return{dialogVisible:!1,litigationFee:{litigationCaseId:"","贷款人":"","出单渠道":"","放款银行":"",totalMoney:0},litigationFees:[],litigationFeeTypeOptions:[{label:"律师费",value:"lawyerFee",category:"litigation_fees",multiple:!0},{label:"法诉费",value:"litigationFee",category:"litigation_fees",multiple:!0},{label:"保全费",value:"preservationFee",category:"litigation_fees",multiple:!0},{label:"布控费",value:"surveillanceFee",category:"litigation_fees",multiple:!0},{label:"公告费",value:"announcementFee",category:"litigation_fees",multiple:!0},{label:"评估费",value:"appraisalFee",category:"litigation_fees",multiple:!0},{label:"执行费",value:"executionFee",category:"litigation_fees",multiple:!0},{label:"违约金",value:"penalty",category:"litigation_fees",multiple:!0},{label:"担保费",value:"guaranteeFee",category:"litigation_fees",multiple:!0},{label:"居间费",value:"intermediaryFee",category:"litigation_fees",multiple:!0},{label:"代偿金",value:"compensity",category:"litigation_fees",multiple:!0},{label:"判决金额",value:"judgmentAmount",category:"judgment_interest",multiple:!1},{label:"利息",value:"interest",category:"judgment_interest",multiple:!1},{label:"其他欠款",value:"otherAmountsOwed",category:"litigation_fees",multiple:!0},{label:"保险费",value:"insurance",category:"litigation_fees",multiple:!0}],submittedLimitedTypes:[],dailyExpenseDialogVisible:!1,dailyExpenseForm:{litigationCaseId:"",loanId:null,status:1,expenseType:"",expenseAmount:"",expenseDate:"",expenseDescription:"",applicantId:"",applicantName:"",applicationTime:"",approvalStatus:"0"},dailyExpenseRules:{expenseType:[{required:!0,message:"请选择费用类型",trigger:"change"}],expenseAmount:[{required:!0,message:"请输入费用金额",trigger:"blur"},{pattern:/^\d+(\.\d{1,2})?$/,message:"请输入正确的金额格式",trigger:"blur"}],expenseDate:[{required:!0,message:"请选择费用发生日期",trigger:"change"}]},dailyExpenseListDialogVisible:!1,dailyExpenseList:[],dailyExpenseListLoading:!1}},methods:{addLitigationFee:function(){this.litigationFees.push({type:"",amount:""})},isTypeDisabled:function(e,t){var i=this.litigationFees.some((function(i,a){return i.type===e&&a!==t})),a=this.litigationFeeTypeOptions.find((function(t){return t.value===e})),l=a&&!a.multiple&&this.submittedLimitedTypes.includes(e);return i||l},handleLitigationFeeTypeChange:function(e,t){var i=this.litigationFees.some((function(i,a){return i.type===e.type&&a!==t}));if(i)return this.$message.warning("该费用类型已选择，请勿重复！"),void this.$set(this.litigationFees[t],"type","");var a=this.litigationFeeTypeOptions.find((function(t){return t.value===e.type}));return a&&!a.multiple&&this.submittedLimitedTypes.includes(e.type)?(this.$message.warning("".concat(a.label,"只能提交一次，已经提交过了！")),void this.$set(this.litigationFees[t],"type","")):"dailyReimbursement"===e.type?(this.$message.warning('日常报销费用请在"日常费用审批"模块中提交！'),void this.$set(this.litigationFees[t],"type","")):void 0},handleLitigationFeeAmountChange:function(){this.litigationFee.totalMoney=this.litigationFees.reduce((function(e,t){return e+Number(t.amount||0)}),0)},removeLitigationFee:function(e){this.litigationFees.splice(e,1),this.handleLitigationFeeAmountChange()},submitForm:function(){var e=this;if(0!==this.litigationFees.length){var t=this.litigationFees.some((function(e){return!e.type||!e.amount}));if(t)this.$message.warning("请完善所有费用项目的类型和金额！");else{var i=this.litigationFees.filter((function(e){return"dailyReimbursement"===e.type})),a=this.litigationFees.filter((function(e){return"dailyReimbursement"!==e.type}));if(i.length>0)this.$message.warning('检测到日常报销费用，请在"日常费用审批"模块中提交！');else{var l=Object(n["a"])({},this.litigationFee);l.litigationCaseId?(l.litigationCaseId=Number(l.litigationCaseId),a.forEach((function(e){var t=e.type,i=e.amount;t&&(l[t]=i||0)})),Object(s["b"])(l).then((function(t){200===t.code?(e.$message.success("提交成功"),e.dialogVisible=!1,e.reset(),e.$emit("refresh")):e.$message.error("提交失败："+(t.msg||"未知错误"))})).catch((function(t){console.error("提交失败:",t),e.$message.error("提交失败，请稍后重试")}))):this.$message.error("缺少法诉案件ID，请重新选择案件")}}}else this.$message.warning("请至少添加一项费用！")},cancel:function(){this.dialogVisible=!1,this.reset()},open:function(){this.reset(),this.dialogVisible=!0},checkSubmittedLimitedTypes:function(e){var t=this;e&&Object(s["c"])(e).then((function(e){200===e.code&&(t.submittedLimitedTypes=e.data||[])})).catch((function(e){console.error("检查限制性费用类型失败:",e),t.submittedLimitedTypes=[]}))},openDailyExpenseDialog:function(){var e,t,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.resetDailyExpenseForm(),this.dailyExpenseForm.litigationCaseId=String(i||this.litigationFee.litigationCaseId||""),this.dailyExpenseForm.status=1,this.dailyExpenseForm.applicantId=String((null===(e=this.$store.state.user)||void 0===e?void 0:e.id)||""),this.dailyExpenseForm.applicantName=String((null===(t=this.$store.state.user)||void 0===t?void 0:t.name)||""),this.dailyExpenseForm.applicationTime=(new Date).toISOString().split("T")[0],this.dailyExpenseDialogVisible=!0},cancelDailyExpense:function(){this.dailyExpenseDialogVisible=!1,this.resetDailyExpenseForm()},submitDailyExpense:function(){var e=this;this.$refs.dailyExpenseForm.validate((function(t){if(t){var i=Object(n["a"])({},e.dailyExpenseForm);if(!i.litigationCaseId)return void e.$message.error("缺少法诉案件ID，请重新选择案件");i.status=1,Object(o["a"])(i).then((function(t){200===t.code?(e.$message.success("日常费用申请提交成功"),e.dailyExpenseDialogVisible=!1,e.resetDailyExpenseForm()):e.$message.error("提交失败："+(t.msg||"未知错误"))})).catch((function(t){console.error("提交失败:",t),e.$message.error("提交失败，请稍后重试")}))}}))},resetDailyExpenseForm:function(){this.dailyExpenseForm={litigationCaseId:"",loanId:null,status:1,expenseType:"",expenseAmount:"",expenseDate:"",expenseDescription:"",applicantId:"",applicantName:"",applicationTime:"",approvalStatus:"0"},this.$refs.dailyExpenseForm&&this.$refs.dailyExpenseForm.resetFields()},reset:function(){var e=this.litigationFee.litigationCaseId;this.litigationFee={litigationCaseId:e,"贷款人":this.litigationFee.贷款人,"出单渠道":this.litigationFee.出单渠道,"放款银行":this.litigationFee.放款银行,totalMoney:0},this.litigationFees=[]},viewDailyExpenseList:function(){var e=this;this.dailyExpenseListLoading=!0,this.dailyExpenseListDialogVisible=!0;var t={litigationCaseId:this.litigationFee.litigationCaseId,pageNum:1,pageSize:100};Object(o["c"])(t).then((function(t){200===t.code?e.dailyExpenseList=t.rows||[]:(e.$message.error("获取日常费用列表失败："+(t.msg||"未知错误")),e.dailyExpenseList=[]),e.dailyExpenseListLoading=!1})).catch((function(t){console.error("获取日常费用列表失败:",t),e.$message.error("获取日常费用列表失败，请稍后重试"),e.dailyExpenseList=[],e.dailyExpenseListLoading=!1}))},getExpenseTypeLabel:function(e){var t={oil_fee:"油费",road_fee:"路费",meal_fee:"餐费",accommodation_fee:"住宿费",transport_fee:"交通费",parking_fee:"停车费",communication_fee:"通讯费",other:"其他"};return t[e]||e},getStatusText:function(e){var t={0:"待审批",1:"全部通过",2:"已拒绝",3:"主管审批中",4:"总监审批中",5:"财务主管审批中",6:"总经理审批中"};return t[e]||"未知状态"},getStatusTagType:function(e){var t={0:"warning",1:"success",2:"danger",3:"primary",4:"primary",5:"primary",6:"primary"};return t[e]||"info"}}},p=r,c=(i("2c8e"),i("2877")),u=Object(c["a"])(p,a,l,!1,null,"6e991120",null);t["default"]=u.exports},"1ceb":function(e,t,i){},"2c8e":function(e,t,i){"use strict";i("1ceb")},a6de:function(e,t,i){"use strict";i.d(t,"c",(function(){return l})),i.d(t,"d",(function(){return n})),i.d(t,"a",(function(){return s})),i.d(t,"b",(function(){return o}));var a=i("b775");function l(e){return Object(a["a"])({url:"/daily_expense_approval/daily_expense_approval/list",method:"get",params:e})}function n(e){return Object(a["a"])({url:"/daily_expense_approval/daily_expense_approval/pendingList",method:"get",params:e})}function s(e){return Object(a["a"])({url:"/daily_expense_approval/daily_expense_approval",method:"post",data:e})}function o(e,t){return Object(a["a"])({url:"/daily_expense_approval/daily_expense_approval/approve/"+e,method:"post",data:t})}}}]);