(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a16bdc90"],{"2bf4":function(e,t,a){"use strict";a.d(t,"d",(function(){return l})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return n})),a.d(t,"e",(function(){return i})),a.d(t,"b",(function(){return s}));var r=a("b775");function l(e){return Object(r["a"])({url:"/car_order/car_order/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/car_order/car_order/"+e,method:"get"})}function n(e){return Object(r["a"])({url:"/car_order/car_order",method:"post",data:e})}function i(e){return Object(r["a"])({url:"/car_order/car_order",method:"put",data:e})}function s(e){return Object(r["a"])({url:"/car_order/car_order/"+e,method:"delete"})}},"2f72":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"更新时间",prop:"updateDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择更新时间"},model:{value:e.queryParams.updateDate,callback:function(t){e.$set(e.queryParams,"updateDate",t)},expression:"queryParams.updateDate"}})],1),a("el-form-item",{attrs:{label:"申请编号",prop:"applyNo"}},[a("el-input",{attrs:{placeholder:"请输入申请编号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.applyNo,callback:function(t){e.$set(e.queryParams,"applyNo",t)},expression:"queryParams.applyNo"}})],1),a("el-form-item",{attrs:{label:"找车团队",prop:"teamId"}},[a("el-input",{attrs:{placeholder:"请输入找车团队",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.teamId,callback:function(t){e.$set(e.queryParams,"teamId",t)},expression:"queryParams.teamId"}})],1),a("el-form-item",{attrs:{label:"车库id",prop:"garageId"}},[a("el-input",{attrs:{placeholder:"请输入车库id",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.garageId,callback:function(t){e.$set(e.queryParams,"garageId",t)},expression:"queryParams.garageId"}})],1),a("el-form-item",{attrs:{label:"入库时间",prop:"inboundTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择入库时间"},model:{value:e.queryParams.inboundTime,callback:function(t){e.$set(e.queryParams,"inboundTime",t)},expression:"queryParams.inboundTime"}})],1),a("el-form-item",{attrs:{label:"出库时间",prop:"outboundTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择出库时间"},model:{value:e.queryParams.outboundTime,callback:function(t){e.$set(e.queryParams,"outboundTime",t)},expression:"queryParams.outboundTime"}})],1),a("el-form-item",{attrs:{label:"找车佣金",prop:"locatingCommission"}},[a("el-input",{attrs:{placeholder:"请输入找车佣金",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.locatingCommission,callback:function(t){e.$set(e.queryParams,"locatingCommission",t)},expression:"queryParams.locatingCommission"}})],1),a("el-form-item",{attrs:{label:"GPS状态",prop:"GPS"}},[a("el-input",{attrs:{placeholder:"请输入GPS状态",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.GPS,callback:function(t){e.$set(e.queryParams,"GPS",t)},expression:"queryParams.GPS"}})],1),a("el-form-item",{attrs:{label:"钥匙邮寄时间",prop:"keyTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择钥匙邮寄时间"},model:{value:e.queryParams.keyTime,callback:function(t){e.$set(e.queryParams,"keyTime",t)},expression:"queryParams.keyTime"}})],1),a("el-form-item",{attrs:{label:"收车方式：1-主动交车，2-钥匙开车，3-板车拖车",prop:"collectionMethod"}},[a("el-input",{attrs:{placeholder:"请输入收车方式：1-主动交车，2-钥匙开车，3-板车拖车",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.collectionMethod,callback:function(t){e.$set(e.queryParams,"collectionMethod",t)},expression:"queryParams.collectionMethod"}})],1),a("el-form-item",{attrs:{label:"分配时间",prop:"allocationTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择分配时间"},model:{value:e.queryParams.allocationTime,callback:function(t){e.$set(e.queryParams,"allocationTime",t)},expression:"queryParams.allocationTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_order:car_order:add"],expression:"['car_order:car_order:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_order:car_order:edit"],expression:"['car_order:car_order:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_order:car_order:remove"],expression:"['car_order:car_order:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_order:car_order:export"],expression:"['car_order:car_order:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.car_orderList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"",align:"center",prop:"id"}}),a("el-table-column",{attrs:{label:"更新时间",align:"center",prop:"updateDate",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.updateDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"申请编号",align:"center",prop:"applyNo"}}),a("el-table-column",{attrs:{label:"找车团队",align:"center",prop:"teamId"}}),a("el-table-column",{attrs:{label:"车库id",align:"center",prop:"garageId"}}),a("el-table-column",{attrs:{label:"1-入库，2-出库",align:"center",prop:"libraryStatus"}}),a("el-table-column",{attrs:{label:"入库时间",align:"center",prop:"inboundTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.inboundTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"出库时间",align:"center",prop:"outboundTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.outboundTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"找车佣金",align:"center",prop:"locatingCommission"}}),a("el-table-column",{attrs:{label:"GPS状态",align:"center",prop:"GPS"}}),a("el-table-column",{attrs:{label:"钥匙状态：1-已邮寄，2-已收回，3-未归还",align:"center",prop:"keyStatus"}}),a("el-table-column",{attrs:{label:"钥匙邮寄时间",align:"center",prop:"keyTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.keyTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"收车方式：1-主动交车，2-钥匙开车，3-板车拖车",align:"center",prop:"collectionMethod"}}),a("el-table-column",{attrs:{label:"订单状态，0-发起，1-已分配，2-已完成，3-未完成，4-已撤销",align:"center",prop:"status"}}),a("el-table-column",{attrs:{label:"分配时间",align:"center",prop:"allocationTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.allocationTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_order:car_order:edit"],expression:"['car_order:car_order:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_order:car_order:remove"],expression:"['car_order:car_order:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"创建时间",prop:"createDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择创建时间"},model:{value:e.form.createDate,callback:function(t){e.$set(e.form,"createDate",t)},expression:"form.createDate"}})],1),a("el-form-item",{attrs:{label:"更新时间",prop:"updateDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择更新时间"},model:{value:e.form.updateDate,callback:function(t){e.$set(e.form,"updateDate",t)},expression:"form.updateDate"}})],1),a("el-form-item",{attrs:{label:"申请编号",prop:"applyNo"}},[a("el-input",{attrs:{placeholder:"请输入申请编号"},model:{value:e.form.applyNo,callback:function(t){e.$set(e.form,"applyNo",t)},expression:"form.applyNo"}})],1),a("el-form-item",{attrs:{label:"找车团队",prop:"teamId"}},[a("el-input",{attrs:{placeholder:"请输入找车团队"},model:{value:e.form.teamId,callback:function(t){e.$set(e.form,"teamId",t)},expression:"form.teamId"}})],1),a("el-form-item",{attrs:{label:"车库id",prop:"garageId"}},[a("el-input",{attrs:{placeholder:"请输入车库id"},model:{value:e.form.garageId,callback:function(t){e.$set(e.form,"garageId",t)},expression:"form.garageId"}})],1),a("el-form-item",{attrs:{label:"入库时间",prop:"inboundTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择入库时间"},model:{value:e.form.inboundTime,callback:function(t){e.$set(e.form,"inboundTime",t)},expression:"form.inboundTime"}})],1),a("el-form-item",{attrs:{label:"出库时间",prop:"outboundTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择出库时间"},model:{value:e.form.outboundTime,callback:function(t){e.$set(e.form,"outboundTime",t)},expression:"form.outboundTime"}})],1),a("el-form-item",{attrs:{label:"找车佣金",prop:"locatingCommission"}},[a("el-input",{attrs:{placeholder:"请输入找车佣金"},model:{value:e.form.locatingCommission,callback:function(t){e.$set(e.form,"locatingCommission",t)},expression:"form.locatingCommission"}})],1),a("el-form-item",{attrs:{label:"GPS状态",prop:"GPS"}},[a("el-input",{attrs:{placeholder:"请输入GPS状态"},model:{value:e.form.GPS,callback:function(t){e.$set(e.form,"GPS",t)},expression:"form.GPS"}})],1),a("el-form-item",{attrs:{label:"钥匙邮寄时间",prop:"keyTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择钥匙邮寄时间"},model:{value:e.form.keyTime,callback:function(t){e.$set(e.form,"keyTime",t)},expression:"form.keyTime"}})],1),a("el-form-item",{attrs:{label:"收车方式：1-主动交车，2-钥匙开车，3-板车拖车",prop:"collectionMethod"}},[a("el-input",{attrs:{placeholder:"请输入收车方式：1-主动交车，2-钥匙开车，3-板车拖车"},model:{value:e.form.collectionMethod,callback:function(t){e.$set(e.form,"collectionMethod",t)},expression:"form.collectionMethod"}})],1),a("el-form-item",{attrs:{label:"分配时间",prop:"allocationTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择分配时间"},model:{value:e.form.allocationTime,callback:function(t){e.$set(e.form,"allocationTime",t)},expression:"form.allocationTime"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],o=a("5530"),n=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("2bf4")),i={name:"Car_order",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,car_orderList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,updateDate:null,applyNo:null,teamId:null,garageId:null,libraryStatus:null,inboundTime:null,outboundTime:null,locatingCommission:null,GPS:null,keyStatus:null,keyTime:null,collectionMethod:null,status:null,allocationTime:null},form:{},rules:{}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(n["d"])(this.queryParams).then((function(t){e.car_orderList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,createBy:null,createDate:null,updateBy:null,updateDate:null,applyNo:null,teamId:null,garageId:null,libraryStatus:null,inboundTime:null,outboundTime:null,locatingCommission:null,GPS:null,keyStatus:null,keyTime:null,collectionMethod:null,status:null,allocationTime:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加找车订单"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;Object(n["c"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改找车订单"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?Object(n["e"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(n["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除找车订单编号为"'+a+'"的数据项？').then((function(){return Object(n["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("car_order/car_order/export",Object(o["a"])({},this.queryParams),"car_order_".concat((new Date).getTime(),".xlsx"))}}},s=i,c=a("2877"),u=Object(c["a"])(s,r,l,!1,null,null,null);t["default"]=u.exports}}]);