<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.installment_application.mapper.InstallmentApplicationMapper">
    
    <resultMap type="InstallmentApplication" id="InstallmentApplicationResult">
        <result property="id"    column="id"    />
        <result property="periodCount"    column="period_count"    />
        <result property="billAmount"    column="bill_amount"    />
        <result property="repayDay"    column="repay_day"    />
        <result property="actualPaymentAmount"    column="actual_payment_amount"    />
        <result property="accountType"    column="account_type"    />
        <result property="loanId"    column="loan_id"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateDate"    column="update_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="installmentApplicationId" column="installment_application_id" />
        <result property="installmentStatus" column="installment_status" />
    </resultMap>

    <sql id="selectInstallmentApplicationVo">
        select id, period_count, bill_amount, repay_day, actual_payment_amount, account_type, loan_id, create_date, update_date, create_by, update_by, installment_application_id, installment_status from installment_application
    </sql>

    <select id="selectInstallmentApplicationList" parameterType="InstallmentApplication" resultMap="InstallmentApplicationResult">
        <include refid="selectInstallmentApplicationVo"/>
        <where>  
            <if test="periodCount != null "> and period_count = #{periodCount}</if>
            <if test="billAmount != null "> and bill_amount = #{billAmount}</if>
            <if test="repayDay != null "> and repay_day = #{repayDay}</if>
            <if test="actualPaymentAmount != null "> and actual_payment_amount = #{actualPaymentAmount}</if>
            <if test="accountType != null  and accountType != ''"> and account_type = #{accountType}</if>
            <if test="loanId != null"> and loan_id = #{loanId}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="installmentApplicationId != null"> and installment_application_id = #{installmentApplicationId}</if>
        </where>
    </select>
    
    <select id="selectInstallmentApplicationById" parameterType="Long" resultMap="InstallmentApplicationResult">
        <include refid="selectInstallmentApplicationVo"/>
        where id = #{id}
    </select>

    <insert id="insertInstallmentApplication" parameterType="InstallmentApplication" useGeneratedKeys="true" keyProperty="id">
        insert into installment_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="periodCount != null">period_count,</if>
            <if test="billAmount != null">bill_amount,</if>
            <if test="repayDay != null">repay_day,</if>
            <if test="actualPaymentAmount != null">actual_payment_amount,</if>
            <if test="accountType != null and accountType != ''">account_type,</if>
            <if test="loanId != null">loan_id,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="installmentApplicationId != null">installment_application_id,</if>
            <if test="installmentStatus != null">installment_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="periodCount != null">#{periodCount},</if>
            <if test="billAmount != null">#{billAmount},</if>
            <if test="repayDay != null">#{repayDay},</if>
            <if test="actualPaymentAmount != null">#{actualPaymentAmount},</if>
            <if test="accountType != null and accountType != ''">#{accountType},</if>
            <if test="loanId != null">#{loanId},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="installmentApplicationId != null">#{installmentApplicationId},</if>
            <if test="installmentStatus != null">#{installmentStatus},</if>
         </trim>
    </insert>

    <update id="updateInstallmentApplication" parameterType="InstallmentApplication">
        update installment_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="periodCount != null">period_count = #{periodCount},</if>
            <if test="billAmount != null">bill_amount = #{billAmount},</if>
            <if test="repayDay != null">repay_day = #{repayDay},</if>
            <if test="actualPaymentAmount != null">actual_payment_amount = #{actualPaymentAmount},</if>
            <if test="accountType != null and accountType != ''">account_type = #{accountType},</if>
            <if test="loanId != null">loan_id = #{loanId},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="installmentApplicationId != null">installment_application_id = #{installmentApplicationId},</if>
            <if test="installmentStatus != null">installment_status = #{installmentStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInstallmentApplicationById" parameterType="Long">
        delete from installment_application where id = #{id}
    </delete>

    <delete id="deleteInstallmentApplicationByIds" parameterType="String">
        delete from installment_application where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>