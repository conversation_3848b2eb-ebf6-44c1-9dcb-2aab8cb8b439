(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-34988e34","chunk-6549b7ae","chunk-2d0c02c3"],{"0f5f":function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.carInfo.plateNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车架号"}},[e._v(e._s(e.carInfo.identityNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"发动机号"}},[e._v(e._s(e.carInfo.engineNumber||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(e._s(e.carStatusLabel))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆位置"}},[e._v(e._s(e.carInfo.carAddress||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(e._s(e.gpsStatusLabel))]):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看车牌照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"行驶证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看行驶证照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"登记证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看登记证照片")])],1):e._e()],1)],1)},r=[],l=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),a("bd52")),n={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var e=this,t=this.carStatusList.find((function(t){return t.value===e.carInfo.carStatus}));return t?t.label:"未知状态"},gpsStatusLabel:function(){var e=this,t=this.gpsStatusList.find((function(t){return t.value===e.carInfo.gpsStatus}));return t?t.label:"未知状态"}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,e&&this.plateNo&&Object(l["e"])(this.plateNo).then((function(e){t.carInfo=e.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},i=n,s=a("2877"),u=Object(s["a"])(i,o,r,!1,null,null,null);t["a"]=u.exports},"14c4":function(e,t,a){},1791:function(e,t,a){},"2bf4":function(e,t,a){"use strict";a.d(t,"d",(function(){return r})),a.d(t,"c",(function(){return l})),a.d(t,"a",(function(){return n})),a.d(t,"e",(function(){return i})),a.d(t,"b",(function(){return s}));var o=a("b775");function r(e){return Object(o["a"])({url:"/car_order/car_order/list",method:"get",params:e})}function l(e){return Object(o["a"])({url:"/car_order/car_order/"+e,method:"get"})}function n(e){return Object(o["a"])({url:"/car_order/car_order",method:"post",data:e})}function i(e){return Object(o["a"])({url:"/car_order/car_order",method:"put",data:e})}function s(e){return Object(o["a"])({url:"/car_order/car_order/"+e,method:"delete"})}},"2eca":function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[e._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},r=[],l=a("bd52"),n={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(l["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},i=n,s=(a("d6fd"),a("2877")),u=Object(s["a"])(i,o,r,!1,null,"8a3d4978",null);t["a"]=u.exports},3806:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.visible,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.visible=t},close:e.resetForm}},[a("el-form",{ref:"form",attrs:{model:e.loanReminder,"label-width":"120px"}},[a("el-divider",{attrs:{"content-position":"left"}},[a("i",{staticClass:"el-icon-document"}),e._v(" 贷款信息 ")]),a("el-descriptions",{attrs:{title:"",column:3,border:""}},[a("el-descriptions-item",{attrs:{label:"贷款人"}},[e._v(" "+e._s(e.loanReminder.customerName)+" ")]),a("el-descriptions-item",{attrs:{label:"出单渠道"}},[e._v(" "+e._s(e.loanReminder.channel)+" ")]),a("el-descriptions-item",{attrs:{label:"放款银行"}},[e._v(" "+e._s(e.loanReminder.bank)+" ")])],1),a("el-divider",{attrs:{"content-position":"left"}},[a("i",{staticClass:"el-icon-document"}),e._v(" 文书信息 ")]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"变更法诉状态"}},[a("litigation-status",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择法诉状态"},model:{value:e.litigationLog.status,callback:function(t){e.$set(e.litigationLog,"status",t)},expression:"litigationLog.status"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"文书名称"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择文书名称"},model:{value:e.litigationLog.docName,callback:function(t){e.$set(e.litigationLog,"docName",t)},expression:"litigationLog.docName"}},[a("el-option",{attrs:{label:"诉前调号",value:"诉前调号"}}),a("el-option",{attrs:{label:"民初号",value:"民初号"}}),a("el-option",{attrs:{label:"执行号",value:"执行号"}}),a("el-option",{attrs:{label:"执保号",value:"执保号"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"文书号"}},[a("el-input",{attrs:{placeholder:"请输入文书号"},model:{value:e.litigationLog.docNumber,callback:function(t){e.$set(e.litigationLog,"docNumber",t)},expression:"litigationLog.docNumber"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"文书生效"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期"},model:{value:e.litigationLog.docEffectiveDate,callback:function(t){e.$set(e.litigationLog,"docEffectiveDate",t)},expression:"litigationLog.docEffectiveDate"}})],1)],1),"待出法院文书"===e.litigationLog.status?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"登记开庭时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"选择开庭时间"},model:{value:e.litigationLog.openDate,callback:function(t){e.$set(e.litigationLog,"openDate",t)},expression:"litigationLog.openDate"}})],1)],1):e._e(),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"上传文书"}},[a("el-upload",{attrs:{data:e.data,action:e.uploadUrl,headers:e.headers,limit:1,"file-list":e.litigationLog.docUploadUrl,"on-success":function(t,a,o){return e.handleUploadSuccess(t,a,o,"litigationLog.docUploadUrl")},"on-remove":function(t,a){return e.handleRemove(t,a,"litigationLog.docUploadUrl")},"on-error":e.handleUploadError}},[a("el-button",{attrs:{size:"small",type:"primary",disabled:e.litigationLog.docUploadUrl.length>=1}},[e._v("点击上传")])],1)],1)],1)],1),a("el-divider",{attrs:{"content-position":"left"}},[a("i",{staticClass:"el-icon-money"}),e._v(" 还款信息 ")]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"还款类型"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择还款类型"},model:{value:e.loanReminder.repaymentStatus,callback:function(t){e.$set(e.loanReminder,"repaymentStatus",t)},expression:"loanReminder.repaymentStatus"}},[a("el-option",{attrs:{label:"部分还款",value:"2"}}),a("el-option",{attrs:{label:"分期还款",value:"3"}}),a("el-option",{attrs:{label:"协商买车",value:"4"}}),a("el-option",{attrs:{label:"法诉结清",value:"5"}}),a("el-option",{attrs:{label:"法诉减免结清",value:"6"}}),a("el-option",{attrs:{label:"拍卖回款",value:"7"}}),a("el-option",{attrs:{label:"法院划扣",value:"8"}}),a("el-option",{attrs:{label:"其他分配回款",value:"9"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"款项明细类型"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择款项明细类型"},model:{value:e.loanReminder.fundsRepayment,callback:function(t){e.$set(e.loanReminder,"fundsRepayment",t)},expression:"loanReminder.fundsRepayment"}},[a("el-option",{attrs:{label:"律师费",value:"律师费"}}),a("el-option",{attrs:{label:"法诉费",value:"法诉费"}}),a("el-option",{attrs:{label:"保全费",value:"保全费"}}),a("el-option",{attrs:{label:"布控费",value:"布控费"}}),a("el-option",{attrs:{label:"公告费",value:"公告费"}}),a("el-option",{attrs:{label:"评估费",value:"评估费"}}),a("el-option",{attrs:{label:"执行费",value:"执行费"}}),a("el-option",{attrs:{label:"违约金",value:"违约金"}}),a("el-option",{attrs:{label:"担保费",value:"担保费"}}),a("el-option",{attrs:{label:"居间费",value:"居间费"}}),a("el-option",{attrs:{label:"代偿金",value:"代偿金"}}),a("el-option",{attrs:{label:"判决金额",value:"判决金额"}}),a("el-option",{attrs:{label:"利息",value:"利息"}}),a("el-option",{attrs:{label:"其他欠款",value:"其他欠款"}}),a("el-option",{attrs:{label:"保险费",value:"保险费"}})],1)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"金额"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,precision:2,placeholder:"请输入金额"},model:{value:e.loanReminder.fundsAmount,callback:function(t){e.$set(e.loanReminder,"fundsAmount",t)},expression:"loanReminder.fundsAmount"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择账号类型"},model:{value:e.loanReminder.fundsAccountType,callback:function(t){e.$set(e.loanReminder,"fundsAccountType",t)},expression:"loanReminder.fundsAccountType"}},[a("el-option",{attrs:{label:"银行账户",value:"银行账户"}}),a("el-option",{attrs:{label:"微信",value:"微信"}}),a("el-option",{attrs:{label:"支付宝",value:"支付宝"}}),a("el-option",{attrs:{label:"其他",value:"其他"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号"}},[a("el-input",{attrs:{disabled:"其他"!==e.loanReminder.fundsAccountType,placeholder:"请输入账号"},model:{value:e.loanReminder.accountNumber,callback:function(t){e.$set(e.loanReminder,"accountNumber",t)},expression:"loanReminder.accountNumber"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"还款凭据"}},[a("el-upload",{attrs:{data:e.data,action:e.uploadUrl,headers:e.headers,"list-type":"picture-card","file-list":e.loanReminder.fundsImage,"on-preview":e.handlePictureCardPreview,"on-success":function(t,a,o){return e.handleUploadSuccess(t,a,o,"loanReminder.fundsImage")},"on-remove":function(t,a){return e.handleRemove(t,a,"loanReminder.fundsImage")},"on-error":e.handleUploadError}},[a("i",{staticClass:"el-icon-plus"})])],1)],1)],1),a("el-divider",{attrs:{"content-position":"left"}},[a("i",{staticClass:"el-icon-notebook-2"}),e._v(" 日志信息 ")]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"日志类型"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择日志类型"},model:{value:e.loanReminder.urgeStatus,callback:function(t){e.$set(e.loanReminder,"urgeStatus",t)},expression:"loanReminder.urgeStatus"}},[a("el-option",{attrs:{label:"继续跟踪",value:"1"}}),a("el-option",{attrs:{label:"约定还款",value:"2"}}),a("el-option",{attrs:{label:"无法跟进",value:"3"}}),a("el-option",{attrs:{label:"暂时无需跟进",value:"4"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"下次跟进时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"选择跟进时间"},model:{value:e.loanReminder.trackingTime,callback:function(t){e.$set(e.loanReminder,"trackingTime",t)},expression:"loanReminder.trackingTime"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"日志描述"}},[a("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入日志描述",maxlength:"500","show-word-limit":""},model:{value:e.loanReminder.urgeDescribe,callback:function(t){e.$set(e.loanReminder,"urgeDescribe",t)},expression:"loanReminder.urgeDescribe"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.cancel}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")])],1),a("el-dialog",{attrs:{visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})])],1)},r=[],l=a("3835"),n=(a("a15b"),a("d81d"),a("e9c4"),a("b64b"),a("d3b7"),a("0643"),a("a573"),a("6af6")),i=a("5f87"),s=a("413c"),u={name:"LitigationLogForm",components:{litigationStatus:n["a"]},props:{action:{type:String,default:"/common/ossupload"},data:{type:Object,default:function(){}}},data:function(){return{title:"提交法诉日志",visible:!1,loanReminder:{},litigationLog:{},uploadUrl:"/prod-api"+this.action,headers:{Authorization:"Bearer "+Object(i["a"])()},dialogImageUrl:"",dialogVisible:!1}},watch:{data:{handler:function(e){e&&(console.log("newVal",e),this.loanReminder={loanId:e.流程序号,customerName:e.贷款人,channel:e.出单渠道,bank:e.放款银行,identity:this.$store.state.user.roles[0],repaymentStatus:"",fundsRepayment:"",fundsAmount:"",fundsImage:[],fundsAccountType:"",accountNumber:"",urgeStatus:"",trackingTime:"",urgeDescribe:"",status:2},this.litigationLog={loanId:e.流程序号,litigationId:e.序号,docName:"",docNumber:"",docUploadUrl:[],docEffectiveDate:"",openDate:"",status:""})},immediate:!0,deep:!0}},methods:{handleUploadSuccess:function(e,t,a,o){var r=o.split("."),n=Object(l["a"])(r,2),i=n[0],s=n[1];this[i][s]=a},handleRemove:function(e,t,a){var o=a.split("."),r=Object(l["a"])(o,2),n=r[0],i=r[1];this[n][i]=t},handleUploadError:function(){this.$modal.msgError("上传失败，请重试"),this.$modal.closeLoading()},handlePictureCardPreview:function(e){this.dialogImageUrl=e.url,this.dialogVisible=!0},submitForm:function(){var e=this,t=JSON.parse(JSON.stringify(this.loanReminder)),a=JSON.parse(JSON.stringify(this.litigationLog));t.fundsImage=t.fundsImage.map((function(e){return e.response})).join(","),a.docUploadUrl=a.docUploadUrl.map((function(e){return e.response})).join(","),t.fundsAccountType="其他"===t.fundsAccountType?t.accountNumber:t.fundsAccountType,console.log("提交表单数据：",this.loanReminder),console.log("提交表单数据：",this.litigationLog),Object(s["h"])({loanReminder:t,litigationLog:a}).then((function(t){e.$modal.msgSuccess("提交成功"),e.visible=!1,e.resetForm()}))},cancel:function(){this.visible=!1,this.resetForm()},resetForm:function(){this.loanReminder={fundsImage:[]},this.litigationLog={docUploadUrl:[]}},openDialog:function(){this.visible=!0},handleExceed:function(e,t){this.$message.warning("只能上传一个文件")}}},c=u,m=(a("b36a"),a("2877")),d=Object(m["a"])(c,o,r,!1,null,"073703b3",null);t["default"]=d.exports},"3c56":function(e,t,a){"use strict";a("14c4")},"3d7b":function(e,t,a){},"413c":function(e,t,a){"use strict";a.d(t,"f",(function(){return r})),a.d(t,"a",(function(){return l})),a.d(t,"i",(function(){return n})),a.d(t,"d",(function(){return i})),a.d(t,"h",(function(){return s})),a.d(t,"b",(function(){return u})),a.d(t,"c",(function(){return c})),a.d(t,"e",(function(){return m})),a.d(t,"g",(function(){return d}));var o=a("b775");function r(e){return Object(o["a"])({url:"/vw_litigation_case_full/vw_litigation_case_full/list",method:"get",params:e})}function l(e){return Object(o["a"])({url:"/litigation_case/litigation_case",method:"post",data:e})}function n(e){return Object(o["a"])({url:"/litigation_case/litigation_case",method:"put",data:e})}function i(e){return Object(o["a"])({url:"/litigation_case/litigation_case/byLoanId/"+e,method:"get"})}function s(e){return Object(o["a"])({url:"/common/public/insertLoanReminderAndLitigationLog",method:"post",data:e})}function u(e){return Object(o["a"])({url:"/litigation_cost/litigation_cost",method:"post",data:e})}function c(e){return Object(o["a"])({url:"/litigation_cost/litigation_cost/checkLimitedFees/"+e,method:"get"})}function m(e){return Object(o["a"])({url:"/litigation_cost/litigation_cost/summary",method:"post",data:{caseIds:e}})}function d(e){return Object(o["a"])({url:"/litigation_log/litigation_log/list",method:"get",params:e})}},6634:function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticClass:"dialogBox",attrs:{title:"发起找车",visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-descriptions",{attrs:{column:2,border:""}},[a("el-descriptions-item",{attrs:{label:"贷款人"}},[e._v(" "+e._s(e.formData.customerName||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"联系电话"}},[e._v(" "+e._s(e.formData.mobilePhone||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"出单渠道"}},[e._v(" "+e._s(e.formData.jgName||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"业务员"}},[e._v(" "+e._s(e.formData.mangerId||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"车辆牌号"}},[e._v(" "+e._s(e.formData.plateNo||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(" "+e._s(e.formData.carStatus||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"车辆位置",span:2}},[e._v(" "+e._s(e.formData.carDetailAddress||"-")+" ")]),a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(" "+e._s(e.formData.gpsStatus||"-")+" ")])],1),a("el-form",{ref:"teamForm",staticStyle:{"margin-top":"30px"},attrs:{model:e.form,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"指定派车团队",prop:"teamId",rules:[{required:!0,message:"请选择派车团队",trigger:"change"}]}},[a("el-select",{staticStyle:{width:"40%","margin-left":"16px"},attrs:{placeholder:"请选择派车团队",clearable:""},model:{value:e.form.teamId,callback:function(t){e.$set(e.form,"teamId",t)},expression:"form.teamId"}},e._l(e.car_team,(function(e){return a("el-option",{key:e.id,attrs:{label:e.teamName,value:e.id}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.handleClose}},[e._v("取 消")])],1)],1)},r=[],l=a("a1e7"),n=a("bd52"),i=a("2bf4"),s={name:"DispatchVehicleForm",props:{loanId:{type:String,default:""}},data:function(){return{dialogVisible:!1,submitLoading:!1,formData:{},form:{teamId:null},car_team:[]}},watch:{loanId:{handler:function(e){var t=this;e&&Object(n["h"])(e).then((function(e){t.formData=e.data}))},deep:!0,immediate:!0}},created:function(){this.getCarTeam()},methods:{openDialog:function(){this.dialogVisible=!0,this.resetForm()},handleClose:function(){this.dialogVisible=!1,this.resetForm()},resetForm:function(){var e=this;this.form={teamId:null},this.$nextTick((function(){e.$refs.teamForm&&e.$refs.teamForm.clearValidate()}))},getCarTeam:function(){var e=this;Object(l["d"])({status:1}).then((function(t){e.car_team=t.rows||[]})).catch((function(){e.car_team=[]}))},submitForm:function(){var e=this;this.form.teamId?(this.form.loanId=this.formData.loanId,this.form.applyNo=this.formData.applyId,Object(i["a"])(this.form).then((function(t){200===t.code?(e.$message.success("提交成功"),e.dialogVisible=!1,e.resetForm()):e.$message.error("提交失败")}))):this.$message.error("请选择派车团队")}}},u=s,c=a("2877"),m=Object(c["a"])(u,o,r,!1,null,null,null);t["a"]=m.exports},"6af6":function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-cascader",{attrs:{options:e.litigationStatusTree,props:e.cascaderProps,placeholder:e.placeholder,clearable:""},on:{change:e.handleChange},model:{value:e.proxyValue,callback:function(t){e.proxyValue=t},expression:"proxyValue"}})},r=[],l={name:"LitigationStatusCascader",props:{value:{type:[String,Array],default:""},placeholder:{type:String,default:"法诉状态"}},data:function(){return{litigationStatusTree:[{label:"立案前",value:"立案前",children:[{label:"准备资料",value:"准备资料"},{label:"已邮寄",value:"撤案已邮寄"},{label:"待立案",value:"待立案"}]},{label:"立案-判决",value:"立案-判决",children:[{label:"待出民初号",value:"待出民初号"},{label:"待开庭",value:"待开庭"},{label:"待出法院文书",value:"待出法院文书"}]},{label:"判决-执行",value:"判决-执行",children:[{label:"待出申请书",value:"待出申请书"},{label:"已提交执行书",value:"已提交执行书"}]},{label:"执行后",value:"执行后",children:[{label:"执行中",value:"执行中"},{label:"待送车",value:"待送车"},{label:"待法拍",value:"待法拍"},{label:"继续执行",value:"继续执行"},{label:"执行终本",value:"执行终本"}]},{label:"结案",value:"结案",children:[{label:"法诉减免结清",value:"法诉减免结清"},{label:"法诉全额结清",value:"法诉全额结清"}]},{label:"撤案",value:"撤案"}],cascaderProps:{emitPath:!1,value:"value",label:"label",children:"children",disabled:function(e){return!!e.children&&e.children.length>0}}}},computed:{proxyValue:{get:function(){return this.value},set:function(e){this.$emit("input",e),this.$emit("update:value",e)}}},methods:{handleChange:function(e){this.$listeners&&this.$listeners.change&&this.$emit("change",e)}}},n=l,i=a("2877"),s=Object(i["a"])(n,o,r,!1,null,null,null);t["a"]=s.exports},a1e7:function(e,t,a){"use strict";a.d(t,"d",(function(){return r})),a.d(t,"c",(function(){return l})),a.d(t,"a",(function(){return n})),a.d(t,"e",(function(){return i})),a.d(t,"b",(function(){return s}));var o=a("b775");function r(e){return Object(o["a"])({url:"/car_team/car_team/list",method:"get",params:e})}function l(e){return Object(o["a"])({url:"/car_team/car_team/"+e,method:"get"})}function n(e){return Object(o["a"])({url:"/car_team/car_team",method:"post",data:e})}function i(e){return Object(o["a"])({url:"/car_team/car_team",method:"put",data:e})}function s(e){return Object(o["a"])({url:"/car_team/car_team/"+e,method:"delete"})}},b36a:function(e,t,a){"use strict";a("3d7b")},b802:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("litigation-log-form",{ref:"litigationLogForm",attrs:{data:e.currentRow}}),a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"certId"}},[a("el-input",{attrs:{placeholder:"贷款人身份证号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.certId,callback:function(t){e.$set(e.queryParams,"certId",t)},expression:"queryParams.certId"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"salesman"}},[a("el-input",{attrs:{placeholder:"业务员姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.salesman,callback:function(t){e.$set(e.queryParams,"salesman",t)},expression:"queryParams.salesman"}})],1),a("el-form-item",{attrs:{label:"",prop:"jgName"}},[a("el-input",{attrs:{placeholder:"录单渠道名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jgName,callback:function(t){e.$set(e.queryParams,"jgName",t)},expression:"queryParams.jgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"followUp"}},[a("el-input",{attrs:{placeholder:"跟催员",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.followUp,callback:function(t){e.$set(e.queryParams,"followUp",t)},expression:"queryParams.followUp"}})],1),a("el-form-item",{attrs:{label:"指派时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.queryParams.allocationTime,callback:function(t){e.$set(e.queryParams,"allocationTime",t)},expression:"queryParams.allocationTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vw_account_loanList,"row-key":"id"}},[a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"法诉文员",align:"center",prop:"法诉文员"}}),a("el-table-column",{attrs:{label:"跟催员",align:"center",prop:"跟催员"}}),a("el-table-column",{attrs:{label:"日志类型",align:"center",prop:"日志类型"}}),a("el-table-column",{attrs:{label:"法诉状态",align:"center",prop:"法诉状态"}}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"贷款人"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openUserInfo({customerId:t.row.customerId,applyId:t.row.applyId})}}},[e._v(" "+e._s(t.row.贷款人)+" ")])]}}])}),a("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"出单渠道"}}),a("el-table-column",{attrs:{label:"业务员",align:"center",prop:"业务员"}}),a("el-table-column",{attrs:{label:"车牌号码",align:"center",prop:"车牌号码"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.checkCar(t.row.车牌号码)}}},[e._v(e._s(t.row.车牌号码))])]}}])}),a("el-table-column",{attrs:{label:"车辆状态",align:"center",prop:"车辆状态"}}),a("el-table-column",{attrs:{label:"找车团队",align:"center",prop:"找车团队"}}),a("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"放款银行"}}),a("el-table-column",{attrs:{label:"总欠款金额",align:"center",prop:"总欠款金额"}}),a("el-table-column",{attrs:{label:"已还金额",align:"center",prop:"已还金额"}}),a("el-table-column",{attrs:{label:"剩余金额",align:"center",prop:"剩余金额"}}),a("el-table-column",{attrs:{label:"催回总金额（本次）",align:"center",prop:"催回总金额"}}),a("el-table-column",{attrs:{label:"还款类型",align:"center",prop:"还款类型"}}),a("el-table-column",{attrs:{label:"还款状态",align:"center",prop:"还款状态"}}),a("el-table-column",{attrs:{label:"扣款时间",align:"center",prop:"扣款时间"}}),a("el-table-column",{attrs:{label:"发起法诉时间",align:"center",prop:"发起法诉日"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-popover",{attrs:{placement:"left",trigger:"click","popper-class":"custom-popover"}},[a("div",{staticClass:"operation-buttons"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vm_car_order:vm_car_order:edit"],expression:"['vm_car_order:vm_car_order:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.openLitigationLogForm(t.row)}}},[e._v(" 提交日志 ")]),a("el-button",{staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.derateSettle(t.row)}}},[e._v("法诉减免结清")]),a("el-button",{staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.urgeBackSettle(t.row)}}},[e._v("法诉结清")]),a("el-button",{staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.openDispatchVehicleForm(t.row)}}},[e._v("派单找车")])],1),a("el-button",{attrs:{slot:"reference",size:"mini",type:"text"},slot:"reference"},[e._v("更多")])],1)]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{staticClass:"dialogBox",attrs:{title:"法诉结清",visible:e.urgeBackopen,width:"800px"},on:{"update:visible":function(t){e.urgeBackopen=t}}},[e.urgeform.examineStatus<1?a("div",{staticClass:"settle_money",on:{click:function(t){return e.getUrgeMoney(1)}}},[e._v("获取结清金额")]):e._e(),a("el-form",{ref:"urgeform",attrs:{model:e.urgeform,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"贷款人",prop:"customerName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeform.customerName,callback:function(t){e.$set(e.urgeform,"customerName",t)},expression:"urgeform.customerName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"出单渠道",prop:"orgName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeform.orgName,callback:function(t){e.$set(e.urgeform,"orgName",t)},expression:"urgeform.orgName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"放款银行",prop:"bank"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeform.bank,callback:function(t){e.$set(e.urgeform,"bank",t)},expression:"urgeform.bank"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"放款金额",prop:"loanAmount"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeform.loanAmount,callback:function(t){e.$set(e.urgeform,"loanAmount",t)},expression:"urgeform.loanAmount"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"银行结清金额",prop:"btotalMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeformbtotalMoney,callback:function(t){e.urgeformbtotalMoney=t},expression:"urgeformbtotalMoney"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber1"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.urgeform.accountNumber1,callback:function(t){e.$set(e.urgeform,"accountNumber1",t)},expression:"urgeform.accountNumber1"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"代扣剩余未还金额",prop:"dtotalMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeformdtotalMoney,callback:function(t){e.urgeformdtotalMoney=t},expression:"urgeformdtotalMoney"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber2"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.urgeform.accountNumber2,callback:function(t){e.$set(e.urgeform,"accountNumber2",t)},expression:"urgeform.accountNumber2"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"违约金",prop:"liquidatedDamages"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeformliquidatedDamages,callback:function(t){e.urgeformliquidatedDamages=t},expression:"urgeformliquidatedDamages"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber3"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.urgeform.accountNumber3,callback:function(t){e.$set(e.urgeform,"accountNumber3",t)},expression:"urgeform.accountNumber3"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"其他欠款",prop:"otherDebt"}},[a("el-input",{on:{input:e.handleInput1},model:{value:e.urgeform.otherDebt,callback:function(t){e.$set(e.urgeform,"otherDebt",t)},expression:"urgeform.otherDebt"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber4"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.urgeform.accountNumber4,callback:function(t){e.$set(e.urgeform,"accountNumber4",t)},expression:"urgeform.accountNumber4"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"单期代偿金",prop:"oneCommutation"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeformoneCommutation,callback:function(t){e.urgeformoneCommutation=t},expression:"urgeformoneCommutation"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber5"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.urgeform.accountNumber5,callback:function(t){e.$set(e.urgeform,"accountNumber5",t)},expression:"urgeform.accountNumber5"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"总欠款金额"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeAllMoney,callback:function(t){e.urgeAllMoney=t},expression:"urgeAllMoney"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e.urgeform.examineStatus<=1?a("el-button",{attrs:{type:"primary"},on:{click:e.submitUrge}},[e._v("确 定")]):e._e(),a("el-button",{on:{click:e.cancelurgeBack}},[e._v("取 消")])],1)],1),a("el-dialog",{staticClass:"dialogBox",attrs:{title:"法诉减免结清",visible:e.derateopen,width:"800px"},on:{"update:visible":function(t){e.derateopen=t}}},[e.derateform.examineStatus<1?a("div",{staticClass:"settle_money",on:{click:function(t){return e.getUrgeMoney(2)}}},[e._v("获取结清金额")]):e._e(),a("el-form",{ref:"derateform",attrs:{model:e.derateform,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"贷款人",prop:"customerName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateform.customerName,callback:function(t){e.$set(e.derateform,"customerName",t)},expression:"derateform.customerName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"出单渠道",prop:"orgName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateform.orgName,callback:function(t){e.$set(e.derateform,"orgName",t)},expression:"derateform.orgName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"放款银行",prop:"bank"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateform.bank,callback:function(t){e.$set(e.derateform,"bank",t)},expression:"derateform.bank"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"放款金额",prop:"loanAmount"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateform.loanAmount,callback:function(t){e.$set(e.derateform,"loanAmount",t)},expression:"derateform.loanAmount"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"银行结清金额",prop:"btotalMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateformbtotalMoney,callback:function(t){e.derateformbtotalMoney=t},expression:"derateformbtotalMoney"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber1"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.derateform.accountNumber1,callback:function(t){e.$set(e.derateform,"accountNumber1",t)},expression:"derateform.accountNumber1"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"代扣剩余未还金额",prop:"dtotalMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateformdtotalMoney,callback:function(t){e.derateformdtotalMoney=t},expression:"derateformdtotalMoney"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber2"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.derateform.accountNumber2,callback:function(t){e.$set(e.derateform,"accountNumber2",t)},expression:"derateform.accountNumber2"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"违约金",prop:"liquidatedDamages"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateformliquidatedDamages,callback:function(t){e.derateformliquidatedDamages=t},expression:"derateformliquidatedDamages"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber3"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.derateform.accountNumber3,callback:function(t){e.$set(e.derateform,"accountNumber3",t)},expression:"derateform.accountNumber3"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"其他欠款",prop:"otherDebt"}},[a("el-input",{on:{input:e.handleInput2},model:{value:e.derateform.otherDebt,callback:function(t){e.$set(e.derateform,"otherDebt",t)},expression:"derateform.otherDebt"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber4"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.derateform.accountNumber4,callback:function(t){e.$set(e.derateform,"accountNumber4",t)},expression:"derateform.accountNumber4"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"单期代偿金",prop:"oneCommutation"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateformoneCommutation,callback:function(t){e.derateformoneCommutation=t},expression:"derateformoneCommutation"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber5"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.derateform.accountNumber5,callback:function(t){e.$set(e.derateform,"accountNumber5",t)},expression:"derateform.accountNumber5"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"总欠款金额"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateAllMoney,callback:function(t){e.derateAllMoney=t},expression:"derateAllMoney"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e.derateform.examineStatus<=1?a("el-button",{attrs:{type:"primary"},on:{click:e.submitDerate}},[e._v("确 定")]):e._e(),a("el-button",{on:{click:e.cancelderate}},[e._v("取 消")])],1)],1),a("litigation-log-form",{ref:"litigationLogForm",attrs:{data:e.currentRow}}),a("userInfo",{ref:"userInfo",attrs:{visible:e.userInfoVisible,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.userInfoVisible=t}}}),a("car-info",{ref:"carInfo",attrs:{visible:e.carShow,title:"车辆信息",plateNo:e.plateNo,permission:"2"},on:{"update:visible":function(t){e.carShow=t}}}),a("dispatch-vehicle-form",{ref:"dispatchVehicleForm",attrs:{loanId:e.dispatchLoanId}})],1)},r=[],l=(a("e9c4"),a("a9e3"),a("b680"),a("b64b"),a("413c")),n=a("bd52"),i=a("3806"),s=a("2eca"),u=a("0f5f"),c=a("6634"),m={name:"Vw_account_loan",components:{listLitigation:l["f"],litigationLogForm:i["default"],userInfo:s["a"],carInfo:u["a"],DispatchVehicleForm:c["a"]},data:function(){return{loading:!1,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,vw_account_loanList:[],open:!1,queryParams:{pageNum:1,pageSize:15,customerName:null,certId:null,plateNo:null,salesman:null,jgName:null,followUp:null,allocationTime:null,startTime:null,endTime:null},currentRow:{},urgeBackopen:!1,urgeform:{},derateopen:!1,derateform:{},accountList:[],urgeAllMoney:0,derateAllMoney:0,loanId:null,addId:null,chForm:{},jmForm:{},urgeformbtotalMoney:0,urgeformdtotalMoney:0,urgeformliquidatedDamages:0,urgeformoneCommutation:0,derateformbtotalMoney:0,derateformdtotalMoney:0,derateformliquidatedDamages:0,derateformoneCommutation:0,customerInfo:{customerId:"",applyId:""},userInfoVisible:!1,carShow:!1,plateNo:"",dispatchLoanId:null}},created:function(){this.getList(),this.getBankList()},methods:{handleQuery:function(){this.queryParams.allocationTime&&(this.queryParams.startTime=this.queryParams.allocationTime[0],this.queryParams.endTime=this.queryParams.allocationTime[1]),this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.customerName=null,this.queryParams.certId=null,this.queryParams.plateNo=null,this.queryParams.salesman=null,this.queryParams.jgName=null,this.queryParams.partnerId=null,this.queryParams.followUp=null,this.queryParams.followStatus=null,this.queryParams.allocationTime=null,this.queryParams.startTime=null,this.queryParams.endTime=null,this.handleQuery()},getList:function(){var e=this;this.loading=!0,Object(l["f"])(this.queryParams).then((function(t){e.vw_account_loanList=t.rows,e.total=0,e.loading=!1}))},getBankList:function(){var e=this;Object(n["i"])().then((function(t){e.accountList=t.rows}))},openLitigationLogForm:function(e){this.currentRow=JSON.parse(JSON.stringify(e)),this.$refs.litigationLogForm.openDialog&&this.$refs.litigationLogForm.openDialog()},getUrgeMoney:function(e){var t=this;if(this.addId){var a={};a=1==e?this.chForm:this.jmForm,Object(n["z"])(a).then((function(a){1==e?(t.urgeformbtotalMoney=a.data.btotalMoney||0,t.urgeformdtotalMoney=a.data.dtotalMoney||0,t.urgeformliquidatedDamages=a.data.liquidatedDamages||0,t.urgeformoneCommutation=a.data.oneCommutation||0,t.urgeAllMoney=Number(t.urgeformbtotalMoney+t.urgeformdtotalMoney+t.urgeformliquidatedDamages+(t.urgeform.otherDebt||0)+t.urgeformoneCommutation).toFixed(2)):(t.derateformbtotalMoney=a.data.btotalMoney||0,t.derateformdtotalMoney=a.data.dtotalMoney||0,t.derateformliquidatedDamages=a.data.liquidatedDamages||0,t.derateformoneCommutation=a.data.oneCommutation||0,t.derateAllMoney=Number(t.derateformbtotalMoney+t.derateformdtotalMoney+t.derateformliquidatedDamages+(t.derateform.otherDebt||0)+t.derateformoneCommutation).toFixed(2))}))}else this.postTrial(this.currentRow,e)},urgeBackSettle:function(e){var t=this;this.currentRow=e;var a={loanId:e.loanId,status:1,pageSize:1,pageNum:1};this.loanId=e.loanId,Object(n["w"])(a).then((function(a){a.data?(t.urgeform=a.data,a.data.customerName||(t.urgeform.customerName=e.customerName),a.data.orgName||(t.urgeform.orgName=e.jgName),a.data.bank||(t.urgeform.bank=e.orgName),a.data.loanAmount||(t.urgeform.loanAmount=e.contractAmt)):t.postTrial(e,1),t.urgeBackopen=!0})).catch((function(e){}))},cancelurgeBack:function(){this.urgeform={},this.chForm={},this.urgeformbtotalMoney=0,this.urgeformdtotalMoney=0,this.urgeformliquidatedDamages=0,this.urgeformoneCommutation=0,this.urgeAllMoney=0,this.urgeBackopen=!1},handleInput1:function(e){this.urgeform.otherDebt=Number(e),this.urgeAllMoney=Number(this.urgeformbtotalMoney+this.urgeformdtotalMoney+this.urgeformliquidatedDamages+this.urgeform.otherDebt+this.urgeformoneCommutation).toFixed(2)},submitUrge:function(){var e=this,t={id:this.addId,btotalMoney:this.urgeformbtotalMoney,dtotalMoney:this.urgeformdtotalMoney,liquidatedDamages:this.urgeformliquidatedDamages,otherDebt:this.urgeform.otherDebt||0,oneCommutation:this.urgeformoneCommutation,accountNumber1:this.urgeform.accountNumber1,accountNumber2:this.urgeform.accountNumber2,accountNumber3:this.urgeform.accountNumber3,accountNumber4:this.urgeform.accountNumber4,accountNumber5:this.urgeform.accountNumber5};Object(n["y"])(t).then((function(t){200==t.code&&(e.$modal.msgSuccess("提交成功"),e.urgeBackopen=!1,e.urgeform={},e.urgeformbtotalMoney=0,e.urgeformdtotalMoney=0,e.urgeformliquidatedDamages=0,e.urgeformoneCommutation=0,e.getList())}))},postTrial:function(e,t){var a=this,o={applyId:e.applyId,loanId:e.loanId,customerName:e.customerName,orgName:e.jgName,loanAmount:e.contractAmt,bank:e.orgName,partnerId:e.partnerId,status:t,salesman:e.nickName,overdueDays:e.boverdueDays,examineStatus:0};Object(n["x"])(o).then((function(e){a.addId=e.id,1==t?(a.urgeform=e.data,a.chForm=e.data):(a.derateform=e.data,a.jmForm=e.data)}))},derateSettle:function(e){var t=this;this.currentRow=e;var a={loanId:e.loanId,status:2,pageSize:1,pageNum:1};this.loanId=e.loanId,Object(n["w"])(a).then((function(a){a.data?(t.derateform=a.data,a.data.customerName||(t.derateform.customerName=e.customerName),a.data.orgName||(t.derateform.orgName=e.jgName),a.data.bank||(t.derateform.bank=e.orgName),a.data.loanAmount||(t.derateform.loanAmount=e.contractAmt),t.derateformbtotalMoney=a.data.trialBalance?a.data.trialBalance.btotalMoney:0,t.derateformdtotalMoney=a.data.trialBalance?a.data.trialBalance.dtotalMoney:0,t.derateformoneCommutation=a.data.trialBalance?a.data.trialBalance.oneCommutation:0,t.derateformliquidatedDamages=a.data.trialBalance?a.data.trialBalance.liquidatedDamages:0,t.derateAllMoney=Number(t.derateformbtotalMoney+t.derateformdtotalMoney+t.derateformliquidatedDamages+t.derateformoneCommutation).toFixed(2),t.addId=a.data.id,t.jmForm=a.data):t.postTrial(e,2),t.derateopen=!0})).catch((function(e){}))},cancelderate:function(){this.derateform={},this.jmForm={},this.derateformbtotalMoney=0,this.derateformdtotalMoney=0,this.derateformliquidatedDamages=0,this.derateformoneCommutation=0,this.derateAllMoney=0,this.derateopen=!1},handleInput2:function(e){this.derateform.otherDebt=Number(e),this.derateAllMoney=Number(this.derateformbtotalMoney+this.derateformdtotalMoney+this.derateformliquidatedDamages+this.derateform.otherDebt+this.derateformoneCommutation).toFixed(2)},submitDerate:function(){var e=this;if(!this.derateformbtotalMoney||this.derateform.accountNumber1)if(!this.derateformdtotalMoney||this.derateform.accountNumber2)if(!this.derateformliquidatedDamages||this.derateform.accountNumber3)if(!this.derateform.otherDebt||this.derateform.accountNumber4)if(!this.derateformoneCommutation||this.derateform.accountNumber5){var t={id:this.addId,btotalMoney:this.derateformbtotalMoney,dtotalMoney:this.derateformdtotalMoney,liquidatedDamages:this.derateformliquidatedDamages,otherDebt:this.derateform.otherDebt||0,oneCommutation:this.derateformoneCommutation,accountNumber1:this.derateform.accountNumber1,accountNumber2:this.derateform.accountNumber2,accountNumber3:this.derateform.accountNumber3,accountNumber4:this.derateform.accountNumber4,accountNumber5:this.derateform.accountNumber5};Object(n["y"])(t).then((function(t){200==t.code&&(e.$modal.msgSuccess("提交成功"),e.derateopen=!1,e.derateform={},e.derateformbtotalMoney=0,e.derateformdtotalMoney=0,e.derateformliquidatedDamages=0,e.derateformoneCommutation=0,e.getList())}))}else this.$modal.msgError("请选择单期代偿金账号");else this.$modal.msgError("请选择其他欠款账号");else this.$modal.msgError("请选择违约金账号");else this.$modal.msgError("请选择代扣剩余账号");else this.$modal.msgError("请选择银行结清账号")},openUserInfo:function(e){this.customerInfo=e,this.userInfoVisible=!0},checkCar:function(e){this.plateNo=e,this.carShow=!0},openDispatchVehicleForm:function(e){this.dispatchLoanId=e.流程序号,this.$refs.dispatchVehicleForm.openDialog()}}},d=m,f=(a("3c56"),a("bf35"),a("2877")),p=Object(f["a"])(d,o,r,!1,null,"********",null);t["default"]=p.exports},bd52:function(e,t,a){"use strict";a.d(t,"l",(function(){return r})),a.d(t,"n",(function(){return l})),a.d(t,"h",(function(){return n})),a.d(t,"i",(function(){return i})),a.d(t,"o",(function(){return s})),a.d(t,"e",(function(){return u})),a.d(t,"s",(function(){return c})),a.d(t,"t",(function(){return m})),a.d(t,"a",(function(){return d})),a.d(t,"A",(function(){return f})),a.d(t,"g",(function(){return p})),a.d(t,"k",(function(){return b})),a.d(t,"w",(function(){return g})),a.d(t,"z",(function(){return h})),a.d(t,"f",(function(){return v})),a.d(t,"x",(function(){return y})),a.d(t,"c",(function(){return _})),a.d(t,"b",(function(){return N})),a.d(t,"v",(function(){return k})),a.d(t,"y",(function(){return w})),a.d(t,"j",(function(){return x})),a.d(t,"q",(function(){return I})),a.d(t,"B",(function(){return S})),a.d(t,"m",(function(){return j})),a.d(t,"r",(function(){return O})),a.d(t,"p",(function(){return D})),a.d(t,"d",(function(){return L})),a.d(t,"u",(function(){return M}));var o=a("b775");function r(e){return Object(o["a"])({url:"/vw_account_loan/vw_account_loan/list",method:"get",params:e})}function l(e){return Object(o["a"])({url:"/vw_account_loan/vw_account_loan/listBySlippageStatus3",method:"get",params:e})}function n(e){return Object(o["a"])({url:"/vw_account_loan/vw_account_loan/byLoanId/"+e,method:"get"})}function i(){return Object(o["a"])({url:"/bank_account/bank_account/bank?pageSize=30",method:"get"})}function s(e){return Object(o["a"])({url:"/loan_compensation/loan_compensation/detail",method:"get",params:e})}function u(e){return Object(o["a"])({url:"/vw_car/vw_car/"+e,method:"get"})}function c(e){return Object(o["a"])({url:"/loan_reminder/loan_reminder/list",method:"get",params:e})}function m(e){return Object(o["a"])({url:"/loan_reminder/loan_reminder",method:"post",data:e})}function d(e){return Object(o["a"])({url:"/vw_account_loan/vw_account_loan",method:"post",data:e})}function f(e){return Object(o["a"])({url:"/vw_account_loan/vw_account_loan",method:"put",data:e})}function p(e){return Object(o["a"])({url:"/vw_account_loan/vw_account_loan/"+e,method:"delete"})}function b(e,t){return Object(o["a"])({url:"/vw_customer_info/vw_customer_info/"+e+"?applyNo="+t,method:"get"})}function g(e){return Object(o["a"])({url:"/loan_settle/loan_settle/detail",method:"get",params:e})}function h(e){return Object(o["a"])({url:"/trial_balance/trial_balance/submit",method:"post",data:e})}function v(e){return Object(o["a"])({url:"/trial_balance/trial_balance/submitdc",method:"post",data:e})}function y(e){return Object(o["a"])({url:"/loan_settle/loan_settle",method:"post",data:e})}function _(e,t){return Object(o["a"])({url:"/loan_settle/loan_settle/process",method:"post",data:{loanId:e,status:t}})}function N(e){return Object(o["a"])({url:"/loan_compensation/loan_compensation/initiate",method:"post",data:e})}function k(e){return Object(o["a"])({url:"/loan_compensation/loan_compensation",method:"put",data:e})}function w(e){return Object(o["a"])({url:"/loan_settle/loan_settle",method:"put",data:e})}function x(e){return Object(o["a"])({url:"/system/user/listByRoleId",method:"get",params:{roleId:e}})}function I(e){return Object(o["a"])({url:"/loan_list/loan_list/batchUpdateUrgeUser",method:"put",data:e})}function S(e){return Object(o["a"])({url:"/loan_list/loan_list",method:"put",data:e})}function j(e){return Object(o["a"])({url:"/vw_account_loan/vw_account_loan/extension_list",method:"get",params:e})}function O(e){return Object(o["a"])({url:"/loan_extension/loan_extension/extension_detail",method:"get",params:{loan_id:e}})}function D(e){return Object(o["a"])({url:"/loan_extension/loan_extension/approve",method:"put",data:e})}function L(e){return Object(o["a"])({url:"/loan_list/loan_list/batchAssignPetitionUser",method:"put",data:e})}function M(e){return Object(o["a"])({url:"/loan_list/loan_list/revokePetitionUser/".concat(e),method:"put"})}},bf35:function(e,t,a){"use strict";a("ef84")},d6fd:function(e,t,a){"use strict";a("1791")},ef84:function(e,t,a){}}]);