(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-64546e76"],{4018:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQ<PERSON>y(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"certId"}},[a("el-input",{attrs:{placeholder:"贷款人身份证号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.certId,callback:function(t){e.$set(e.queryParams,"certId",t)},expression:"queryParams.certId"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"partnerId"}},[a("el-select",{attrs:{placeholder:"放款银行",clearable:""},model:{value:e.queryParams.partnerId,callback:function(t){e.$set(e.queryParams,"partnerId",t)},expression:"queryParams.partnerId"}},e._l(e.bankList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"jgName"}},[a("el-input",{attrs:{placeholder:"录单渠道名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jgName,callback:function(t){e.$set(e.queryParams,"jgName",t)},expression:"queryParams.jgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"followUp"}},[a("el-input",{attrs:{placeholder:"跟催员",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.followUp,callback:function(t){e.$set(e.queryParams,"followUp",t)},expression:"queryParams.followUp"}})],1),e.showMore?[a("el-form-item",{attrs:{label:"",prop:"slippageStatus"}},[a("el-select",{attrs:{placeholder:"逾期状态",clearable:""},model:{value:e.queryParams.slippageStatus,callback:function(t){e.$set(e.queryParams,"slippageStatus",t)},expression:"queryParams.slippageStatus"}},e._l(e.slippageList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"isExtension"}},[a("el-select",{attrs:{placeholder:"是否延期",clearable:""},model:{value:e.queryParams.isExtension,callback:function(t){e.$set(e.queryParams,"isExtension",t)},expression:"queryParams.isExtension"}},e._l(e.isExtensionList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"followStatus"}},[a("el-select",{attrs:{placeholder:"跟催类型",clearable:""},model:{value:e.queryParams.followStatus,callback:function(t){e.$set(e.queryParams,"followStatus",t)},expression:"queryParams.followStatus"}},e._l(e.followUpList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"扣款时间"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.allocationTime,callback:function(t){e.$set(e.queryParams,"allocationTime",t)},expression:"queryParams.allocationTime"}})],1)]:e._e(),a("el-form-item",{attrs:{label:"",prop:"carStatus"}},[a("el-select",{attrs:{placeholder:"车辆状态",clearable:""},model:{value:e.queryParams.carStatus,callback:function(t){e.$set(e.queryParams,"carStatus",t)},expression:"queryParams.carStatus"}},e._l(e.carStatusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"isFindCar"}},[a("el-select",{attrs:{placeholder:"是否派单找车",clearable:""},model:{value:e.queryParams.isFindCar,callback:function(t){e.$set(e.queryParams,"isFindCar",t)},expression:"queryParams.isFindCar"}},e._l(e.isFindCarList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")]),a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(t){e.showMore=!e.showMore}}},[e._v(" "+e._s(e.showMore?"收起":"更多")+" "),a("i",{class:e.showMore?"el-icon-arrow-up":"el-icon-arrow-down"})])],1)],2),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vw_account_loanList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"逾期状态",align:"center",prop:"slippageStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.slippageStatus?a("span",[e._v(" "+e._s(1==t.row.slippageStatus?"提醒":2==t.row.slippageStatus?"电催":3==t.row.slippageStatus?"上访":4==t.row.slippageStatus?"逾期30-60":"逾期60+")+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"还款状态",align:"center",prop:"repaymentStatus",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.repaymentStatus?a("span",[e._v(" "+e._s(1==t.row.repaymentStatus?"还款中":2==t.row.repaymentStatus?"已完结":3==t.row.repaymentStatus?"提前结清":4==t.row.repaymentStatus?"逾期催回结清":5==t.row.repaymentStatus?"逾期减免结清":6==t.row.repaymentStatus?"逾期未还款":7==t.row.repaymentStatus?"逾期还款中":8==t.row.repaymentStatus?"代偿未还款":9==t.row.repaymentStatus?"代偿还款中":10==t.row.repaymentStatus?"代偿减免结清":11==t.row.repaymentStatus?"代偿全额结清":"未知状态")+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"延期状态",align:"center",prop:"isExtension"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.isExtension?a("span",{staticStyle:{color:"red"}},[e._v("延期")]):0==t.row.isExtension?a("span",[e._v("未延期")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"跟催员",align:"center",prop:"followUp"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.followUp?a("span",[e._v(e._s(t.row.followUp))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"customerName"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.customerName?a("span",{staticStyle:{color:"#46a6ff",cursor:"pointer"},on:{click:function(a){return e.checkLender({customerId:t.row.customerId,applyId:t.row.applyId})}}},[e._v(" "+e._s(t.row.customerName)+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"贷款人身份证",align:"center",prop:"certId",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.certId?a("span",[e._v(e._s(t.row.certId))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"jgName",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.jgName?a("span",[e._v(e._s(t.row.jgName))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"业务员",align:"center",prop:"nickName"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.nickName?a("span",[e._v(e._s(t.row.nickName))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"车牌号码",align:"center",prop:"plateNo",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.plateNo?a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.checkCar(t.row.plateNo)}}},[e._v(e._s(t.row.plateNo))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"车辆状态",align:"center",prop:"carStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.carStatus?a("span",[e._v(" "+e._s("1"==t.row.carStatus?"省内正常行驶":"2"==t.row.carStatus?"省外正常行驶":"3"==t.row.carStatus?"抵押":"4"==t.row.carStatus?"疑似抵押":"5"==t.row.carStatus?"疑似黑车":"6"==t.row.carStatus?"已入库":"7"==t.row.carStatus?"车在法院":"8"==t.row.carStatus?"已法拍":"协商卖车")+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"车辆位置",align:"center",prop:"carDetailAddress",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.carDetailAddress?a("span",[e._v(e._s(t.row.carDetailAddress))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"GPS状态",align:"center",prop:"gpsStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.gpsStatus?a("span",[e._v(" "+e._s("1"==t.row.gpsStatus?"部分拆除":"2"==t.row.gpsStatus?"全部拆除":"3"==t.row.gpsStatus?"GPS正常":"停车30天以上")+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"派车团队",align:"center",prop:"carTeamName"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.carTeamName?a("span",[e._v(e._s(t.row.carTeamName))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"bank"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.bank?a("span",[e._v(e._s(t.row.bank))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"银行逾期天数",align:"center",prop:"boverdueDays",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.boverdueDays?a("span",[e._v(e._s(t.row.boverdueDays))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"首期逾期金额",align:"center",prop:"foverdueAmount",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.foverdueAmount?a("span",[e._v(e._s(t.row.foverdueAmount))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"银行逾期金额",align:"center",prop:"boverdueAmount",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.boverdueAmount?a("span",[e._v(e._s(t.row.boverdueAmount))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"代扣逾期天数",align:"center",prop:"doverdueDays",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.doverdueDays?a("span",[e._v(e._s(t.row.doverdueDays))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"代扣逾期金额",align:"center",prop:"doverdueAmount",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.doverdueAmount?a("span",[e._v(e._s(t.row.doverdueAmount))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"催回金额",align:"center",prop:"realReturnMoney"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.realReturnMoney?a("span",{staticStyle:{color:"#46a6ff",cursor:"pointer"},on:{click:function(a){return e.checkReminder(t.row.loanId)}}},[e._v(" "+e._s(t.row.realReturnMoney)+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"催记类型",align:"center",prop:"followStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.followStatus?a("span",[e._v(" "+e._s(1==t.row.followStatus?"继续联系":2==t.row.followStatus?"约定还款":"无法跟进")+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"催记提交日期",align:"center",prop:"followDate",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.followDate?a("span",[e._v(e._s(e.parseTime(t.row.followDate,"{y}-{m}-{d}")))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"下次跟进时间",align:"center",prop:"trackingTime",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.trackingTime?a("span",[e._v(" "+e._s(e.parseTime(t.row.trackingTime,"{y}-{m}-{d}"))+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"预计还款日期",align:"center",prop:"appointedTime",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.appointedTime?a("span",[e._v(" "+e._s(e.parseTime(t.row.appointedTime,"{y}-{m}-{d}"))+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"催回日期",align:"center",prop:"reminderDate",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.reminderDate?a("span",[e._v(e._s(e.parseTime(t.row.reminderDate,"{y}-{m}-{d}")))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-popover",{attrs:{placement:"left",trigger:"click","popper-class":"custom-popover"}},[a("div",{staticClass:"operation-buttons"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["loan_reminder:loan_reminder:add"],expression:"['loan_reminder:loan_reminder:add']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleSubmitReminder(t.row)}}},[e._v(" 提交催记 ")]),a("el-button",{staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.logView(t.row)}}},[e._v("催记日志")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_account_loan:vw_account_loan:initiate"],expression:"['vw_account_loan:vw_account_loan:initiate']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.initiate(t.row)}}},[e._v(" 发起代偿 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_account_loan:vw_account_loan:collect"],expression:"['vw_account_loan:vw_account_loan:collect']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.urgeBackSettle(t.row)}}},[e._v(" 催回结清 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_account_loan:vw_account_loan:derate"],expression:"['vw_account_loan:vw_account_loan:derate']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.derateSettle(t.row)}}},[e._v(" 减免结清 ")]),a("el-button",{staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.planBth(t.row)}}},[e._v("还款计划")]),a("el-popconfirm",{attrs:{title:"确认预估呆账吗？"},on:{confirm:function(a){return e.handleEstimateBadDebt(t.row)}}},[a("el-button",{staticClass:"operation-btn",attrs:{slot:"reference",size:"mini",type:"text"},slot:"reference"},[e._v("预估呆账")])],1)],1),a("el-button",{attrs:{slot:"reference",size:"mini",type:"text"},slot:"reference"},[e._v("更多")])],1)]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{staticClass:"dialogBox",attrs:{"close-on-click-modal":!1,title:"催记列表",visible:e.reminderShow,width:"700px","append-to-body":""},on:{"update:visible":function(t){e.reminderShow=t}}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.reminderList,border:""}},[a("el-table-column",{attrs:{prop:"identity",label:"身份",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(" "+e._s(1==t.row.identity?"业务员":2==t.row.identity?"贷后文员":3==t.row.identity?"电催员":4==t.row.identity?"上访员":5==t.row.identity?"强制上访员":6==t.row.identity?"找车员":"法诉文员")+" ")])]}}])}),a("el-table-column",{attrs:{prop:"customerName",label:"催回人",width:"120"}}),a("el-table-column",{attrs:{prop:"bmoney",label:"银行金额"}}),a("el-table-column",{attrs:{prop:"dmoney",label:"代扣金额"}}),a("el-table-column",{attrs:{label:"总金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.bmoney+t.row.dmoney))])]}}])}),a("el-table-column",{attrs:{prop:"trackingTime",label:"催回日期",width:"130"}}),a("el-table-column",{attrs:{label:"操作"}},[[a("span",{staticStyle:{color:"#46a6ff",cursor:"pointer"}},[e._v("详情")])]],2)],1)],1),a("el-dialog",{staticClass:"dialogBox",attrs:{"close-on-click-modal":!1,title:"发起代偿",visible:e.commuteopen,width:"900px","append-to-body":""},on:{"update:visible":function(t){e.commuteopen=t}}},[a("div",{staticClass:"settle_money",on:{click:e.trialSub}},[e._v("发起试算")]),a("el-form",{ref:"trialForm",attrs:{model:e.trialForm,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"贷款人",prop:"customerName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.customerName,callback:function(t){e.$set(e.trialForm,"customerName",t)},expression:"trialForm.customerName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"出单渠道",prop:"orgName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.orgName,callback:function(t){e.$set(e.trialForm,"orgName",t)},expression:"trialForm.orgName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"放款银行",prop:"bank"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.bank,callback:function(t){e.$set(e.trialForm,"bank",t)},expression:"trialForm.bank"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"放款金额",prop:"loanAmount"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.loanAmount,callback:function(t){e.$set(e.trialForm,"loanAmount",t)},expression:"trialForm.loanAmount"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"剩余本金"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialFormprincipal,callback:function(t){e.trialFormprincipal=t},expression:"trialFormprincipal"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"银行逾期金额"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialFormboverdueAmount,callback:function(t){e.trialFormboverdueAmount=t},expression:"trialFormboverdueAmount"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"银行利息"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForminterest,callback:function(t){e.trialForminterest=t},expression:"trialForminterest"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"代偿总金额"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialFormall,callback:function(t){e.trialFormall=t},expression:"trialFormall"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"风险金划比例",prop:"fxjProportion"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{type:"number"},on:{input:function(t){return e.onInputLimit("fxjProportion",t)},change:function(t){return e.calcMoney("fxjProportion","fxjMoney",t)}},model:{value:e.trialForm.fxjProportion,callback:function(t){e.$set(e.trialForm,"fxjProportion",e._n(t))},expression:"trialForm.fxjProportion"}},[a("template",{slot:"append"},[e._v("%")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"风险金划金额",prop:"fxjMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.fxjMoney,callback:function(t){e.$set(e.trialForm,"fxjMoney",t)},expression:"trialForm.fxjMoney"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"账号类型",prop:"fxjAccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:""},model:{value:e.trialForm.fxjAccount,callback:function(t){e.$set(e.trialForm,"fxjAccount",t)},expression:"trialForm.fxjAccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"渠道转入比例",prop:"qdProportion"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{type:"number"},on:{input:function(t){return e.onInputLimit("qdProportion",t)},change:function(t){return e.calcMoney("qdProportion","qdMoney",t)}},model:{value:e.trialForm.qdProportion,callback:function(t){e.$set(e.trialForm,"qdProportion",e._n(t))},expression:"trialForm.qdProportion"}},[a("template",{slot:"append"},[e._v("%")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"渠道转入金额",prop:"qdMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.qdMoney,callback:function(t){e.$set(e.trialForm,"qdMoney",t)},expression:"trialForm.qdMoney"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"账号类型",prop:"qdAccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:""},model:{value:e.trialForm.qdAccount,callback:function(t){e.$set(e.trialForm,"qdAccount",t)},expression:"trialForm.qdAccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"广明借比例",prop:"gmjProportion"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{type:"number"},on:{input:function(t){return e.onInputLimit("gmjProportion",t)},change:function(t){return e.calcMoney("gmjProportion","gmjMoney",t)}},model:{value:e.trialForm.gmjProportion,callback:function(t){e.$set(e.trialForm,"gmjProportion",e._n(t))},expression:"trialForm.gmjProportion"}},[a("template",{slot:"append"},[e._v("%")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"广明借额",prop:"gmjMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.gmjMoney,callback:function(t){e.$set(e.trialForm,"gmjMoney",t)},expression:"trialForm.gmjMoney"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"账号类型",prop:"gmjAccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:""},model:{value:e.trialForm.gmjAccount,callback:function(t){e.$set(e.trialForm,"gmjAccount",t)},expression:"trialForm.gmjAccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"科技借比例",prop:"kjczProportion"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{type:"number"},on:{input:function(t){return e.onInputLimit("kjjProportion",t)},change:function(t){return e.calcMoney("kjjProportion","kjjMoney",t)}},model:{value:e.trialForm.kjjProportion,callback:function(t){e.$set(e.trialForm,"kjjProportion",e._n(t))},expression:"trialForm.kjjProportion"}},[a("template",{slot:"append"},[e._v("%")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"科技借额",prop:"kjjMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.kjjMoney,callback:function(t){e.$set(e.trialForm,"kjjMoney",t)},expression:"trialForm.kjjMoney"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"账号类型",prop:"kjjAccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:""},model:{value:e.trialForm.kjjAccount,callback:function(t){e.$set(e.trialForm,"kjjAccount",t)},expression:"trialForm.kjjAccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"科技出资比例",prop:"kjczProportion"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{type:"number"},on:{input:function(t){return e.onInputLimit("kjczProportion",t)},change:function(t){return e.calcMoney("kjczProportion","kjczMoney",t)}},model:{value:e.trialForm.kjczProportion,callback:function(t){e.$set(e.trialForm,"kjczProportion",e._n(t))},expression:"trialForm.kjczProportion"}},[a("template",{slot:"append"},[e._v("%")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"科技出资金额",prop:"kjczMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.kjczMoney,callback:function(t){e.$set(e.trialForm,"kjczMoney",t)},expression:"trialForm.kjczMoney"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"账号类型",prop:"kjczAccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:""},model:{value:e.trialForm.kjczAccount,callback:function(t){e.$set(e.trialForm,"kjczAccount",t)},expression:"trialForm.kjczAccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"守邦出资比例",prop:"sbczProportion"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{type:"number"},on:{input:function(t){return e.onInputLimit("sbczProportion",t)},change:function(t){return e.calcMoney("sbczProportion","sbczMoney",t)}},model:{value:e.trialForm.sbczProportion,callback:function(t){e.$set(e.trialForm,"sbczProportion",e._n(t))},expression:"trialForm.sbczProportion"}},[a("template",{slot:"append"},[e._v("%")])],2)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"守邦出资金额",prop:"sbczMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialForm.sbczMoney,callback:function(t){e.$set(e.trialForm,"sbczMoney",t)},expression:"trialForm.sbczMoney"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"账号类型",prop:"sbczAccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:""},model:{value:e.trialForm.sbczAccount,callback:function(t){e.$set(e.trialForm,"sbczAccount",t)},expression:"trialForm.sbczAccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"上传凭据"}},[e.trialForm.examineStatus>=1?a("div",[e.trialForm.image&&e.trialForm.image.length>0?e._l(e.trialForm.image,(function(t,o){return a("el-image",{key:o,staticStyle:{width:"100px",height:"100px","margin-right":"10px"},attrs:{src:t,fit:"cover","preview-src-list":e.trialForm.image}})})):a("el-upload",{attrs:{"list-type":"picture-card",disabled:!0,action:e.uploadImgUrl}},[a("div",[e._v("暂无凭据")])])],2):a("el-upload",{attrs:{data:e.data,action:e.uploadImgUrl,"list-type":"picture-card",headers:e.headers,"file-list":e.trialForm.image,"on-preview":e.handlePictureCardPreview,"on-success":function(t,a,o){return e.handleUploadSuccess(t,a,o,"trialForm.image")},"on-remove":function(t,a){return e.handleRemove(t,a,"trialForm.image")},"on-error":e.handleUploadError}},[a("i",{staticClass:"el-icon-plus"})])],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"代扣剩余未还金额"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialFormdoverdueAmount,callback:function(t){e.trialFormdoverdueAmount=t},expression:"trialFormdoverdueAmount"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"违约金"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialFormliquidatedDamages,callback:function(t){e.trialFormliquidatedDamages=t},expression:"trialFormliquidatedDamages"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"登记其他欠款金额",prop:"otherDebt"}},[a("el-input",{attrs:{type:"number"},on:{input:e.handleInput3},model:{value:e.trialFormotherDebt,callback:function(t){e.trialFormotherDebt=t},expression:"trialFormotherDebt"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"总账款"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.trialFormtotal,callback:function(t){e.trialFormtotal=t},expression:"trialFormtotal"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e.trialForm.examineStatus?e._e():a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancelcommute}},[e._v("取 消")])],1)],1),a("el-dialog",{staticClass:"dialogBox",attrs:{"before-close":e.cancelurgeBack,"close-on-click-modal":!1,title:"催回结清",visible:e.urgeBackopen,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.urgeBackopen=t}}},[e.urgeform.examineStatus<1?a("div",{staticClass:"settle_money",on:{click:function(t){return e.getUrgeMoney(1)}}},[e._v("获取结清金额")]):e._e(),a("el-form",{ref:"urgeform",attrs:{model:e.urgeform,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"贷款人",prop:"customerName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeform.customerName,callback:function(t){e.$set(e.urgeform,"customerName",t)},expression:"urgeform.customerName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"出单渠道",prop:"orgName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeform.orgName,callback:function(t){e.$set(e.urgeform,"orgName",t)},expression:"urgeform.orgName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"放款银行",prop:"bank"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeform.bank,callback:function(t){e.$set(e.urgeform,"bank",t)},expression:"urgeform.bank"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"放款金额",prop:"loanAmount"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeform.loanAmount,callback:function(t){e.$set(e.urgeform,"loanAmount",t)},expression:"urgeform.loanAmount"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"银行结清金额",prop:"btotalMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeformbtotalMoney,callback:function(t){e.urgeformbtotalMoney=t},expression:"urgeformbtotalMoney"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber1"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.urgeform.accountNumber1,callback:function(t){e.$set(e.urgeform,"accountNumber1",t)},expression:"urgeform.accountNumber1"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"代扣剩余未还金额",prop:"dtotalMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeformdtotalMoney,callback:function(t){e.urgeformdtotalMoney=t},expression:"urgeformdtotalMoney"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber2"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.urgeform.accountNumber2,callback:function(t){e.$set(e.urgeform,"accountNumber2",t)},expression:"urgeform.accountNumber2"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"违约金",prop:"liquidatedDamages"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeformliquidatedDamages,callback:function(t){e.urgeformliquidatedDamages=t},expression:"urgeformliquidatedDamages"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber3"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.urgeform.accountNumber3,callback:function(t){e.$set(e.urgeform,"accountNumber3",t)},expression:"urgeform.accountNumber3"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"其他欠款",prop:"otherDebt"}},[a("el-input",{on:{input:e.handleInput1},model:{value:e.urgeform.otherDebt,callback:function(t){e.$set(e.urgeform,"otherDebt",t)},expression:"urgeform.otherDebt"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber4"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.urgeform.accountNumber4,callback:function(t){e.$set(e.urgeform,"accountNumber4",t)},expression:"urgeform.accountNumber4"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"单期代偿金",prop:"oneCommutation"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeformoneCommutation,callback:function(t){e.urgeformoneCommutation=t},expression:"urgeformoneCommutation"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber5"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.urgeform.accountNumber5,callback:function(t){e.$set(e.urgeform,"accountNumber5",t)},expression:"urgeform.accountNumber5"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"总欠款金额"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.urgeAllMoney,callback:function(t){e.urgeAllMoney=t},expression:"urgeAllMoney"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e.urgeform.examineStatus<=1?a("el-button",{attrs:{type:"primary"},on:{click:e.submitUrge}},[e._v("确 定")]):e._e(),a("el-button",{on:{click:e.cancelurgeBack}},[e._v("取 消")])],1)],1),a("el-dialog",{staticClass:"dialogBox",attrs:{"before-close":e.cancelderate,"close-on-click-modal":!1,title:"减免结清",visible:e.derateopen,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.derateopen=t}}},[e.urgeform.examineStatus<1?a("div",{staticClass:"settle_money",on:{click:function(t){return e.getUrgeMoney(2)}}},[e._v("获取结清金额")]):e._e(),a("el-form",{ref:"derateform",attrs:{model:e.derateform,rules:e.rules,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"贷款人",prop:"customerName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateform.customerName,callback:function(t){e.$set(e.derateform,"customerName",t)},expression:"derateform.customerName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"出单渠道",prop:"orgName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateform.orgName,callback:function(t){e.$set(e.derateform,"orgName",t)},expression:"derateform.orgName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"放款银行",prop:"bank"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateform.bank,callback:function(t){e.$set(e.derateform,"bank",t)},expression:"derateform.bank"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"放款金额",prop:"loanAmount"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateform.loanAmount,callback:function(t){e.$set(e.derateform,"loanAmount",t)},expression:"derateform.loanAmount"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"银行结清金额",prop:"btotalMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateformbtotalMoney,callback:function(t){e.derateformbtotalMoney=t},expression:"derateformbtotalMoney"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber1"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.derateform.accountNumber1,callback:function(t){e.$set(e.derateform,"accountNumber1",t)},expression:"derateform.accountNumber1"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"代扣剩余未还金额",prop:"dtotalMoney"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateformdtotalMoney,callback:function(t){e.derateformdtotalMoney=t},expression:"derateformdtotalMoney"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber2"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.derateform.accountNumber2,callback:function(t){e.$set(e.derateform,"accountNumber2",t)},expression:"derateform.accountNumber2"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"违约金",prop:"liquidatedDamages"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateformliquidatedDamages,callback:function(t){e.derateformliquidatedDamages=t},expression:"derateformliquidatedDamages"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber3"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.derateform.accountNumber3,callback:function(t){e.$set(e.derateform,"accountNumber3",t)},expression:"derateform.accountNumber3"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"其他欠款",prop:"otherDebt"}},[a("el-input",{on:{input:e.handleInput2},model:{value:e.derateform.otherDebt,callback:function(t){e.$set(e.derateform,"otherDebt",t)},expression:"derateform.otherDebt"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber4"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.derateform.accountNumber4,callback:function(t){e.$set(e.derateform,"accountNumber4",t)},expression:"derateform.accountNumber4"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"单期代偿金",prop:"oneCommutation"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateformoneCommutation,callback:function(t){e.derateformoneCommutation=t},expression:"derateformoneCommutation"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"accountNumber5"}},[a("el-select",{attrs:{placeholder:"账号类型"},model:{value:e.derateform.accountNumber5,callback:function(t){e.$set(e.derateform,"accountNumber5",t)},expression:"derateform.accountNumber5"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e.name}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"总欠款金额"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.derateAllMoney,callback:function(t){e.derateAllMoney=t},expression:"derateAllMoney"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e.derateform.examineStatus<=1?a("el-button",{attrs:{type:"primary"},on:{click:e.submitDerate}},[e._v("确 定")]):e._e(),a("el-button",{on:{click:e.cancelderate}},[e._v("取 消")])],1)],1),a("userInfo",{ref:"userInfo",attrs:{visible:e.lenderShow,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.lenderShow=t}}}),a("car-info",{ref:"carInfo",attrs:{visible:e.carShow,title:"车辆信息",plateNo:e.plateNo,permission:"1"},on:{"update:visible":function(t){e.carShow=t}}}),a("loan-reminder-log",{ref:"loanReminderLog",attrs:{"loan-id":e.logLoanId}}),a("loan-reminder-log-submit",{ref:"loanReminderLogSubmit",attrs:{"loan-id":e.submitLoanId,status:3}})],1)},r=[],l=a("3835"),n=a("5530"),i=(a("a15b"),a("d81d"),a("14d9"),a("a9e3"),a("b680"),a("d3b7"),a("0643"),a("a573"),a("bd52")),s=a("5f87"),u=a("2eca"),m=a("0f5f"),c=a("7954"),d=a("a5e3"),p={components:{userInfo:u["a"],carInfo:m["a"],LoanReminderLog:c["a"],LoanReminderLogSubmit:d["a"]},props:{value:[String,Object,Array],action:{type:String,default:"/common/ossupload"},data:{type:Object}},name:"Vw_account_loan",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,showMore:!1,total:0,vw_account_loanList:[],logopen:!1,currentRow:{},urgeBackopen:!1,derateopen:!1,commuteopen:!1,detailShow:!1,lenderShow:!1,customerInfo:{customerId:"",applyId:""},carShow:!1,plateNo:"",queryParams:{pageNum:1,pageSize:15,customerName:null,certId:null,plateNo:null,partnerId:null,jgName:null,slippageStatus:null,followStatus:null,followUp:null,allocationTime:null,startTime:"",endTime:"",isExtension:"",carStatus:"",isFindCar:""},bankList:[{value:"EO00000010",label:"苏银金租"},{value:"IO00000006",label:"浙商银行"},{value:"IO00000007",label:"中关村银行"},{value:"IO00000008",label:"蓝海银行"},{value:"IO00000009",label:"华瑞银行"},{value:"IO00000010",label:"皖新租赁"}],isExtensionList:[{label:"否",value:"0"},{label:"是",value:"1"}],slippageList:[{label:"提醒",value:1},{label:"电催",value:2},{label:"上访",value:3},{label:"逾期30-60",value:4},{label:"逾期60+",value:5}],followUpList:[{label:"继续联系",value:1},{label:"约定还款",value:2},{label:"无法跟进",value:3}],accountList:[],rules:{},dialogImageUrl:"",dialogVisible:!1,textarea:null,uploadImgUrl:"/prod-api"+this.action,headers:{Authorization:"Bearer "+Object(s["a"])()},reminderList:[],reminderShow:!1,urgeform:{},derateform:{},radio:0,logList:[],logDetail:{},logForm:{loanId:null,pageNum:1,pageSize:15},logtotal:0,urgeAllMoney:0,derateAllMoney:0,loanId:null,addId:null,jmForm:{},chForm:{},urgeformbtotalMoney:0,urgeformdtotalMoney:0,urgeformliquidatedDamages:0,urgeformoneCommutation:0,derateformbtotalMoney:0,derateformdtotalMoney:0,derateformliquidatedDamages:0,derateformoneCommutation:0,trialForm:{},trialFormprincipal:0,trialFormboverdueAmount:0,trialForminterest:0,trialFormall:0,trialFormdoverdueAmount:0,trialFormliquidatedDamages:0,trialFormtotal:0,trialFormotherDebt:0,dkyqMoney:0,bankyqMoney:0,lendingBank:null,OrderingChannel:null,logLoanId:null,submitLoanId:null,carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],isFindCarList:[{label:"未派单",value:"0"},{label:"已派单",value:"1"}]}},created:function(){this.getList(),this.getAccountList()},methods:{getAccountList:function(){var e=this;Object(i["i"])().then((function(t){e.accountList=t.rows}))},handleEstimateBadDebt:function(e){var t=this,a={id:e.loanId,badDebt:1};Object(i["B"])(a).then((function(e){200==e.code?t.$modal.msgSuccess("预估呆账成功"):t.$modal.msgError("预估呆账失败")}))},trialSub:function(){var e=this,t={applyId:this.trialForm.applyId,id:this.trialForm.id,loanId:this.trialForm.loanId,loanAmount:this.trialForm.loanAmount,partnerId:this.trialForm.partnerId};Object(i["f"])(t).then((function(t){e.trialFormprincipal=t.data.principal||0,e.trialForm.defaultInterest=t.data.defaultInterest||0,e.trialForminterest=t.data.interest||0,e.trialFormall=t.data.btotalMoney||0,e.trialFormdoverdueAmount=t.data.dtotalMoney||0,e.trialFormliquidatedDamages=t.data.liquidatedDamages||0,e.trialFormtotal=Number(e.trialFormall+e.trialFormdoverdueAmount+e.trialFormliquidatedDamages+e.trialFormotherDebt).toFixed(2)}))},submitDerate:function(){var e=this;if(!this.derateformbtotalMoney||this.derateform.accountNumber1)if(!this.derateformdtotalMoney||this.derateform.accountNumber2)if(!this.derateformliquidatedDamages||this.derateform.accountNumber3)if(!this.derateform.otherDebt||this.derateform.accountNumber4)if(!this.derateformoneCommutation||this.derateform.accountNumber5){var t={customerName:this.derateform.customerName,orgName:this.derateform.orgName,bank:this.derateform.bank,loanAmount:this.derateform.loanAmount,btotalMoney:this.derateform.btotalMoney,accountNumber1:this.derateform.accountNumber1,dtotalMoney:this.derateform.dtotalMoney,accountNumber2:this.derateform.accountNumber2,liquidatedDamages:this.derateform.liquidatedDamages,accountNumber3:this.derateform.accountNumber3,otherDebt:this.derateform.otherDebt,accountNumber4:this.derateform.accountNumber4,oneCommutation:this.derateform.oneCommutation,accountNumber5:this.derateform.accountNumber5,totalMoney:this.derateAllMoney,id:this.addId,status:2,examineStatus:1};Object(i["y"])(t).then((function(t){200==t.code&&(e.$modal.msgSuccess("提交成功"),e.derateopen=!1,e.derateform={},e.derateformbtotalMoney=0,e.derateformdtotalMoney=0,e.derateformliquidatedDamages=0,e.derateformoneCommutation=0)}))}else this.$modal.msgError("请选择单期代偿金账号");else this.$modal.msgError("请选择其他欠款账号");else this.$modal.msgError("请选择违约金账号");else this.$modal.msgError("请选择代扣剩余账号");else this.$modal.msgError("请选择银行结清账号")},submitUrge:function(){var e=this;if(!this.urgeformbtotalMoney||this.urgeform.accountNumber1)if(!this.urgeformdtotalMoney||this.urgeform.accountNumber2)if(!this.urgeformliquidatedDamages||this.urgeform.accountNumber3)if(!this.urgeform.otherDebt||this.urgeform.accountNumber4)if(!this.urgeformoneCommutation||this.urgeform.accountNumber5){var t={customerName:this.urgeform.customerName,orgName:this.urgeform.orgName,bank:this.urgeform.bank,loanAmount:this.urgeform.loanAmount,btotalMoney:this.urgeform.btotalMoney,accountNumber1:this.urgeform.accountNumber1,dtotalMoney:this.urgeform.dtotalMoney,accountNumber2:this.urgeform.accountNumber2,liquidatedDamages:this.urgeform.liquidatedDamages,accountNumber3:this.urgeform.accountNumber3,otherDebt:this.urgeform.otherDebt,accountNumber4:this.urgeform.accountNumber4,oneCommutation:this.urgeform.oneCommutation,accountNumber5:this.urgeform.accountNumber5,totalMoney:this.urgeAllMoney,id:this.addId,status:1,examineStatus:1};Object(i["y"])(t).then((function(t){200==t.code&&(e.$modal.msgSuccess("提交成功"),e.urgeBackopen=!1,e.urgeform={},e.urgeformbtotalMoney=0,e.urgeformdtotalMoney=0,e.urgeformliquidatedDamages=0,e.urgeformoneCommutation=0)}))}else this.$modal.msgError("请选择单期代偿金账号");else this.$modal.msgError("请选择其他欠款账号");else this.$modal.msgError("请选择违约金账号");else this.$modal.msgError("请选择代扣剩余账号");else this.$modal.msgError("请选择银行结清账号")},onInputLimit:function(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:100,r=Number(t);isNaN(r)&&(r=a),r>o&&(r=o),r<a&&(r=a),this.trialForm[e]=Number(r)},cancelDetail:function(){this.detailShow=!1},checkCar:function(e){this.plateNo=e,this.carShow=!0},checkLender:function(e){this.customerInfo=e,this.lenderShow=!0},planBth:function(e){var t={partnerId:e.partnerId,applyId:e.applyId};"IO00000008"==e.partnerId?this.$router.push({path:"/repayment/repayment_plan/lhindex",query:t}):"EO00000010"==e.partnerId?this.$router.push({path:"/repayment/repayment_plan/syindex",query:t}):"IO00000006"==e.partnerId?this.$router.push({path:"/repayment/repayment_plan/zsindex",query:t}):"IO00000007"==e.partnerId?this.$router.push({path:"/repayment/repayment_plan/zgcindex",query:t}):"IO00000009"==e.partnerId?this.$router.push({path:"/repayment/repayment_plan/hrindex",query:t}):this.$router.push({path:"/repayment/repayment_plan/wxindex",query:t})},checkReminder:function(e){var t=this,a={loanId:e,urgeStatus:2};Object(i["s"])(a).then((function(e){t.reminderList=e.rows,t.reminderShow=!0}))},cancelcommute:function(){this.commuteopen=!1},initiate:function(e){var t=this,a={loanId:e.loanId};Object(i["o"])(a).then((function(a){a.data?(t.trialForm=a.data,t.trialForm.loanAmount=e.contractAmt||0,t.trialForm.dtotalMoney=a.data.trialBalance?a.data.trialBalance.dtotalMoney:0,t.trialFormprincipal=a.data.trialBalance?a.data.trialBalance.principal:0,t.trialFormboverdueAmount=e.boverdueAmount||0,t.trialForminterest=a.data.trialBalance?a.data.trialBalance.interest:0,t.trialFormall=a.data.trialBalance?a.data.trialBalance.btotalMoney:0,t.trialFormdoverdueAmount=a.data.trialBalance?a.data.trialBalance.dtotalMoney:0,t.trialFormliquidatedDamages=a.data.trialBalance?a.data.trialBalance.liquidatedDamages:0,t.trialFormotherDebt=a.data.otherDebt?a.data.otherDebt:0,console.log(t.trialFormall,t.trialFormdoverdueAmount,t.trialFormliquidatedDamages,t.trialFormotherDebt),t.trialFormtotal=Number(t.trialFormall+t.trialFormdoverdueAmount+t.trialFormliquidatedDamages+t.trialFormotherDebt).toFixed(2),t.trialForm.image=a.data.image?a.data.image.split(","):[],t.commuteopen=!0):(t.addTrial(e),t.trialForm.image=[])}))},addTrial:function(e){var t=this;console.log(e);var a={id:e.id,applyId:e.applyId,loanId:e.loanId,customerId:e.customerId,customerName:e.customerName,salesman:e.nickName,orgName:e.jgName,partnerId:e.partnerId,bank:e.orgName,loanAmount:e.contractAmt,examineStatus:0};Object(i["b"])(a).then((function(e){t.trialForm=e.data||{},t.commuteopen=!0}))},cancelderate:function(){this.derateform={},this.jmForm={},this.derateformbtotalMoney=0,this.derateformdtotalMoney=0,this.derateformliquidatedDamages=0,this.derateformoneCommutation=0,this.derateAllMoney=0,this.derateopen=!1},handleInput3:function(e){this.trialFormotherDebt=Number(e),this.trialFormtotal=Number(this.trialFormall+this.trialFormdoverdueAmount+this.trialFormliquidatedDamages+this.trialFormotherDebt).toFixed(2)},handleInput2:function(e){this.derateform.otherDebt=Number(e),this.derateAllMoney=Number(this.derateform.btotalMoney+this.derateform.dtotalMoney+this.derateform.liquidatedDamages+this.derateform.otherDebt+this.derateform.oneCommutation).toFixed(2)},handleInput1:function(e){this.urgeform.otherDebt=Number(e),this.urgeAllMoney=Number(this.urgeform.btotalMoney+this.urgeform.dtotalMoney+this.urgeform.liquidatedDamages+this.urgeform.otherDebt+this.urgeform.oneCommutation).toFixed(2)},derateSettle:function(e){var t=this,a={loanId:e.loanId,status:2,pageSize:1,pageNum:1};this.loanId=e.loanId,Object(i["w"])(a).then((function(a){a.data?(t.derateform=a.data,a.data.customerName||(t.derateform.customerName=e.customerName),a.data.orgName||(t.derateform.orgName=e.jgName),a.data.bank||(t.derateform.bank=e.orgName),a.data.loanAmount||(t.derateform.loanAmount=e.contractAmt),t.derateform.btotalMoney=a.data.trialBalance?a.data.trialBalance.btotalMoney:0,t.derateform.dtotalMoney=a.data.trialBalance?a.data.trialBalance.dtotalMoney:0,t.derateform.oneCommutation=a.data.trialBalance?a.data.trialBalance.oneCommutation:0,t.derateform.liquidatedDamages=a.data.trialBalance?a.data.trialBalance.liquidatedDamages:0,t.derateformbtotalMoney=a.data.trialBalance?a.data.trialBalance.btotalMoney:0,t.derateformdtotalMoney=a.data.trialBalance?a.data.trialBalance.dtotalMoney:0,t.derateformoneCommutation=a.data.trialBalance?a.data.trialBalance.oneCommutation:0,t.derateformliquidatedDamages=a.data.trialBalance?a.data.trialBalance.liquidatedDamages:0,t.derateAllMoney=Number(t.derateform.btotalMoney+t.derateform.dtotalMoney+t.derateform.liquidatedDamages+t.derateform.oneCommutation).toFixed(2),t.addId=a.data.id,t.jmForm=a.data):t.postTrial(e,2)})).catch((function(e){})),this.derateopen=!0},cancelurgeBack:function(){this.urgeform={},this.chForm={},this.urgeformbtotalMoney=0,this.urgeformdtotalMoney=0,this.urgeformliquidatedDamages=0,this.urgeformoneCommutation=0,this.urgeAllMoney=0,this.urgeBackopen=!1},getUrgeMoney:function(e){var t=this,a={};a=1==e?this.chForm:this.jmForm,Object(i["z"])(a).then((function(a){1==e?(t.urgeform.btotalMoney=a.data.btotalMoney||0,t.urgeform.dtotalMoney=a.data.dtotalMoney||0,t.urgeform.liquidatedDamages=a.data.liquidatedDamages||0,t.urgeform.oneCommutation=a.data.oneCommutation||0,t.urgeformbtotalMoney=a.data.btotalMoney||0,t.urgeformdtotalMoney=a.data.dtotalMoney||0,t.urgeformliquidatedDamages=a.data.liquidatedDamages||0,t.urgeformoneCommutation=a.data.oneCommutation||0,t.urgeAllMoney=Number(t.urgeform.btotalMoney+t.urgeform.dtotalMoney+t.urgeform.liquidatedDamages+t.urgeform.otherDebt+t.urgeform.oneCommutation).toFixed(2)):(t.derateform.btotalMoney=a.data.btotalMoney||0,t.derateform.dtotalMoney=a.data.dtotalMoney||0,t.derateform.liquidatedDamages=a.data.liquidatedDamages||0,t.derateform.oneCommutation=a.data.oneCommutation||0,t.derateformbtotalMoney=a.data.btotalMoney||0,t.derateformdtotalMoney=a.data.dtotalMoney||0,t.derateformliquidatedDamages=a.data.liquidatedDamages||0,t.derateformoneCommutation=a.data.oneCommutation||0,t.derateAllMoney=Number(t.derateform.btotalMoney+t.derateform.dtotalMoney+t.derateform.liquidatedDamages+t.derateform.otherDebt+t.derateform.oneCommutation).toFixed(2))}))},urgeBackSettle:function(e){var t=this,a={loanId:e.loanId,status:1,pageSize:1,pageNum:1};this.loanId=e.loanId,Object(i["w"])(a).then((function(a){a.data?(t.urgeform=a.data,a.data.customerName||(t.urgeform.customerName=e.customerName),a.data.orgName||(t.urgeform.orgName=e.jgName),a.data.bank||(t.urgeform.bank=e.orgName),a.data.loanAmount||(t.urgeform.loanAmount=e.contractAmt),t.urgeform.btotalMoney=a.data.trialBalance?a.data.trialBalance.btotalMoney:0,t.urgeform.dtotalMoney=a.data.trialBalance?a.data.trialBalance.dtotalMoney:0,t.urgeform.liquidatedDamages=a.data.trialBalance?a.data.trialBalance.liquidatedDamages:0,t.urgeform.oneCommutation=a.data.trialBalance?a.data.trialBalance.oneCommutation:0,t.urgeformbtotalMoney=a.data.trialBalance?a.data.trialBalance.btotalMoney:0,t.urgeformdtotalMoney=a.data.trialBalance?a.data.trialBalance.dtotalMoney:0,t.urgeformliquidatedDamages=a.data.trialBalance?a.data.trialBalance.liquidatedDamages:0,t.urgeformoneCommutation=a.data.trialBalance?a.data.trialBalance.oneCommutation:0,t.urgeAllMoney=Number(t.urgeform.btotalMoney+t.urgeform.dtotalMoney+t.urgeform.liquidatedDamages+t.urgeform.oneCommutation).toFixed(2),t.addId=a.data.id,t.chForm=a.data):t.postTrial(e,1),t.urgeBackopen=!0})).catch((function(e){}))},postTrial:function(e,t){var a=this,o={applyId:e.applyId,loanId:e.loanId,customerName:e.customerName,orgName:e.jgName,loanAmount:e.contractAmt,bank:e.orgName,partnerId:e.partnerId,status:t,salesman:e.nickName,overdueDays:e.boverdueDays,examineStatus:0};Object(i["x"])(o).then((function(e){a.addId=e.id,1==t?(a.urgeform=e.data,a.chForm=e.data):(a.derateform=e.data,a.jmForm=e.data)}))},cancelLog:function(){this.logopen=!1},logView:function(e){this.logLoanId=e.loanId,this.$refs.loanReminderLog.openLogDialog()},handleSubmitReminder:function(e){var t=this;this.submitLoanId=e.loanId,this.$nextTick((function(){t.$refs.loanReminderLogSubmit.openDialog()}))},logView2:function(){var e=this;Object(i["s"])(this.logForm).then((function(t){e.logList=t.rows,e.logtotal=t.total,e.logopen=!0})).catch((function(e){}))},handleChange:function(e){this.queryParams.orgId=e},getList:function(){var e=this;console.log(this.$store.state.user),this.loading=!0,Object(i["l"])(this.queryParams).then((function(t){e.vw_account_loanList=t.rows,e.total=t.total,e.loading=!1}))},reset:function(){this.form={customerName:null,jgName:null,orgName:null,boverdueAmount:null,doverdueAmount:null},this.resetForm("form")},handleQuery:function(){this.queryParams.allocationTime&&(this.queryParams.startTime=this.queryParams.allocationTime[0],this.queryParams.endTime=this.queryParams.allocationTime[1]),this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.customerName=null,this.queryParams.certId=null,this.queryParams.plateNo=null,this.queryParams.partnerId=null,this.queryParams.jgName=null,this.queryParams.slippageStatus=null,this.queryParams.followStatus=null,this.queryParams.followUp=null,this.queryParams.allocationTime=null,this.queryParams.startTime=null,this.queryParams.endTime=null,this.queryParams.isExtension=null,this.queryParams.carStatus="",this.queryParams.isFindCar="",this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加VIEW"},calcMoney:function(e,t,a){this.trialForm[t]=(a*this.trialFormall/100).toFixed(2)},submitForm:function(){var e=this,t=["fxjProportion","qdProportion","gmjProportion","kjjProportion","kjczProportion","sbczProportion"],a=t.reduce((function(t,a){return t+Number(e.trialForm[a]||0)}),0);if(100===a){for(var o=[{money:"fxjMoney",account:"fxjAccount",label:"风险金账号类型"},{money:"qdMoney",account:"qdAccount",label:"渠道账号类型"},{money:"gmjMoney",account:"gmjAccount",label:"广明借账号类型"},{money:"kjjMoney",account:"kjjAccount",label:"科技借账号类型"},{money:"kjczMoney",account:"kjczAccount",label:"科技出资账号类型"},{money:"sbczMoney",account:"sbczAccount",label:"守邦出资账号类型"}],r=0,l=o;r<l.length;r++){var n=l[r];if(Number(this.trialForm[n.money])>0&&!this.trialForm[n.account])return void this.$message.warning("请先选择".concat(n.label))}var s={id:this.trialForm.id,fxjProportion:this.trialForm.fxjProportion,fxjMoney:this.trialForm.fxjMoney,fxjAccount:this.trialForm.fxjAccount,qdProportion:this.trialForm.qdProportion,qdMoney:this.trialForm.qdMoney,qdAccount:this.trialForm.qdAccount,gmjProportion:this.trialForm.gmjProportion,gmjMoney:this.trialForm.gmjMoney,gmjAccount:this.trialForm.gmjAccount,kjjProportion:this.trialForm.kjjProportion,kjjMoney:this.trialForm.kjjMoney,kjjAccount:this.trialForm.kjjAccount,kjczProportion:this.trialForm.kjczProportion,kjczMoney:this.trialForm.kjczMoney,kjczAccount:this.trialForm.kjczAccount,sbczProportion:this.trialForm.sbczProportion,sbczMoney:this.trialForm.sbczMoney,sbczAccount:this.trialForm.sbczAccount,otherDebt:this.trialFormotherDebt,totalMoney:this.trialFormtotal,image:this.trialForm.image.map((function(e){return e.response})).join(","),examineStatus:1,repaymentType:0};console.log(s),Object(i["v"])(s).then((function(t){200==t.code&&(e.$modal.msgSuccess("提交成功"),e.commuteopen=!1)}))}else this.$message.error("六项比例之和必须等于100！")},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除VIEW编号为"'+a+'"的数据项？').then((function(){return Object(i["g"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("vw_account_loan/vw_account_loan/export",Object(n["a"])({},this.queryParams),"vw_account_loan_".concat((new Date).getTime(),".xlsx"))},selectRow:function(){console.log("Row selected:",this.vw_account_loanList)},handleUploadSuccess:function(e,t,a,o){var r=o.split("."),n=Object(l["a"])(r,2),i=n[0],s=n[1];this[i][s]=a},handleRemove:function(e,t,a){var o=a.split("."),r=Object(l["a"])(o,2),n=r[0],i=r[1];this[n][i]=t},handleUploadError:function(){this.$modal.msgError("上传图片失败，请重试"),this.$modal.closeLoading()},handlePictureCardPreview:function(e){this.dialogImageUrl=e.url,this.dialogVisible=!0}}},f=p,b=(a("d9f0"),a("9ea0"),a("2877")),g=Object(b["a"])(f,o,r,!1,null,null,null);t["default"]=g.exports},8944:function(e,t,a){},"9ea0":function(e,t,a){"use strict";a("8944")},cb12:function(e,t,a){},d9f0:function(e,t,a){"use strict";a("cb12")}}]);