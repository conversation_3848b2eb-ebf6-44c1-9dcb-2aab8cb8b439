package com.ruoyi.vw_litigation_case_full.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.vw_litigation_case_full.mapper.VwLitigationCaseFullMapper;
import com.ruoyi.vw_litigation_case_full.domain.VwLitigationCaseFull;
import com.ruoyi.vw_litigation_case_full.domain.LitigationReminderDetail;
import com.ruoyi.vw_litigation_case_full.service.IVwLitigationCaseFullService;
import com.ruoyi.loan_reminder.mapper.LoanReminderMapper;
import com.ruoyi.loan_reminder.domain.LoanReminder;

/**
 * VIEWService业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class VwLitigationCaseFullServiceImpl implements IVwLitigationCaseFullService
{
    @Autowired
    private VwLitigationCaseFullMapper vwLitigationCaseFullMapper;

    @Autowired
    private LoanReminderMapper loanReminderMapper;

    /**
     * 查询VIEW
     *
     * @param 序号 VIEW主键
     * @return VIEW
     */
    @Override
    public VwLitigationCaseFull selectVwLitigationCaseFullBy序号(Long 序号)
    {
        VwLitigationCaseFull result = vwLitigationCaseFullMapper.selectVwLitigationCaseFullBy序号(序号);
        if (result != null) {
            // 填充催记数据
            fillReminderData(result);
        }
        return result;
    }

    /**
     * 查询VIEW列表
     *
     * @param vwLitigationCaseFull VIEW
     * @return VIEW
     */
    @Override
    public List<VwLitigationCaseFull> selectVwLitigationCaseFullList(VwLitigationCaseFull vwLitigationCaseFull)
    {
        List<VwLitigationCaseFull> list = vwLitigationCaseFullMapper.selectVwLitigationCaseFullList(vwLitigationCaseFull);

        // 批量填充催记数据
        if (list != null && !list.isEmpty()) {
            // 获取所有litigation_id（序号字段对应litigation_case表的id）
            List<Long> litigationIds = list.stream()
                .map(VwLitigationCaseFull::get序号)
                .filter(id -> id != null)
                .collect(Collectors.toList());

            // 批量查询每个法诉案件的最新催记记录
            List<LoanReminder> reminders = loanReminderMapper.selectLatestLoanRemindersByLitigationIds(litigationIds);

            // 为每个案件填充催记数据
            for (VwLitigationCaseFull item : list) {
                fillReminderDataFromList(item, reminders);
            }

            // 如果有logType筛选条件，则根据最新催记类型进行筛选
            if (vwLitigationCaseFull.getLogType() != null) {
                list = list.stream()
                    .filter(item -> {
                        Integer logType = item.get日志类型();
                        return logType != null && logType.equals(vwLitigationCaseFull.getLogType());
                    })
                    .collect(Collectors.toList());
            }
        }

        return list;
    }

    /**
     * 新增VIEW
     * 
     * @param vwLitigationCaseFull VIEW
     * @return 结果
     */
    @Override
    public int insertVwLitigationCaseFull(VwLitigationCaseFull vwLitigationCaseFull)
    {
        return vwLitigationCaseFullMapper.insertVwLitigationCaseFull(vwLitigationCaseFull);
    }

    /**
     * 修改VIEW
     * 
     * @param vwLitigationCaseFull VIEW
     * @return 结果
     */
    @Override
    public int updateVwLitigationCaseFull(VwLitigationCaseFull vwLitigationCaseFull)
    {
        return vwLitigationCaseFullMapper.updateVwLitigationCaseFull(vwLitigationCaseFull);
    }

    /**
     * 批量删除VIEW
     * 
     * @param 序号s 需要删除的VIEW主键
     * @return 结果
     */
    @Override
    public int deleteVwLitigationCaseFullBy序号s(Long[] 序号s)
    {
        return vwLitigationCaseFullMapper.deleteVwLitigationCaseFullBy序号s(序号s);
    }

    /**
     * 删除VIEW信息
     *
     * @param 序号 VIEW主键
     * @return 结果
     */
    @Override
    public int deleteVwLitigationCaseFullBy序号(Long 序号)
    {
        return vwLitigationCaseFullMapper.deleteVwLitigationCaseFullBy序号(序号);
    }

    /**
     * 填充单个案件的催记数据
     *
     * @param caseFull 法诉案件对象
     */
    private void fillReminderData(VwLitigationCaseFull caseFull) {
        if (caseFull.get序号() == null) {
            return;
        }

        // 查询该法诉案件的最新催记记录
        LoanReminder latestReminder = loanReminderMapper.selectLatestLoanReminderByLitigationId(caseFull.get序号());

        if (latestReminder != null) {
            // 填充最新催记数据
            caseFull.set日志内容(latestReminder.getUrgeDescribe());
            caseFull.set日志时间(latestReminder.getCreateTime());
            caseFull.set日志操作人(latestReminder.getCreateBy());
            caseFull.set催记类型(latestReminder.getUrgeStatus());
            caseFull.set还款状态(latestReminder.getRepaymentStatus());
            caseFull.set审批状态(latestReminder.getExamineStatus());
            caseFull.set催回金额(latestReminder.getUrgeMoney());
            caseFull.set约定时间(latestReminder.getAppointedTime());
            caseFull.set下次跟踪时间(latestReminder.getTrackingTime());

            // 填充前端显示字段
            caseFull.set日志类型(latestReminder.getUrgeStatus());
            caseFull.set日志更新日(latestReminder.getCreateTime());

            // 如果催记类型为2（约定还款），则将约定时间填入扣款时间
            if (latestReminder.getUrgeStatus() != null && latestReminder.getUrgeStatus() == 2) {
                caseFull.set扣款时间(latestReminder.getAppointedTime());
            }

            // 设置催回总金额（单条记录的催回金额）
            if (latestReminder.getUrgeMoney() != null) {
                caseFull.set催回总金额(latestReminder.getUrgeMoney());
            }
        }
    }

    /**
     * 从催记列表中填充案件的催记数据（用于批量处理）
     *
     * @param caseFull 法诉案件对象
     * @param allReminders 所有催记数据（已经是每个litigation_id的最新记录）
     */
    private void fillReminderDataFromList(VwLitigationCaseFull caseFull, List<LoanReminder> allReminders) {
        if (caseFull.get序号() == null || allReminders == null || allReminders.isEmpty()) {
            return;
        }

        // 筛选出该案件的最新催记记录（通过litigation_id匹配）
        LoanReminder latestReminder = allReminders.stream()
            .filter(r -> r.getLitigationId() != null && r.getLitigationId().equals(caseFull.get序号()))
            .findFirst()
            .orElse(null);

        if (latestReminder != null) {
            // 填充最新催记数据
            caseFull.set日志内容(latestReminder.getUrgeDescribe());
            caseFull.set日志时间(latestReminder.getCreateTime());
            caseFull.set日志操作人(latestReminder.getCreateBy());
            caseFull.set催记类型(latestReminder.getUrgeStatus());
            caseFull.set还款状态(latestReminder.getRepaymentStatus());
            caseFull.set审批状态(latestReminder.getExamineStatus());
            caseFull.set催回金额(latestReminder.getUrgeMoney());
            caseFull.set约定时间(latestReminder.getAppointedTime());
            caseFull.set下次跟踪时间(latestReminder.getTrackingTime());

            // 填充前端显示字段
            caseFull.set日志类型(latestReminder.getUrgeStatus());
            caseFull.set日志更新日(latestReminder.getCreateTime());

            // 如果催记类型为2（约定还款），则将约定时间填入扣款时间
            if (latestReminder.getUrgeStatus() != null && latestReminder.getUrgeStatus() == 2) {
                caseFull.set扣款时间(latestReminder.getAppointedTime());
            }

            // 设置催回总金额（单条记录的催回金额）
            if (latestReminder.getUrgeMoney() != null) {
                caseFull.set催回总金额(latestReminder.getUrgeMoney());
            }
        }
    }

    /**
     * 获取法诉案件的催记金额明细
     *
     * @param litigationId 法诉案件ID
     * @return 催记金额明细列表
     */
    @Override
    public List<LitigationReminderDetail> getLitigationReminderDetails(Long litigationId) {
        // 查询该法诉案件的所有催记记录（status=2表示法诉日志）
        LoanReminder queryReminder = new LoanReminder();
        queryReminder.setLitigationId(litigationId);
        queryReminder.setStatus("2"); // 法诉日志

        List<LoanReminder> reminders = loanReminderMapper.selectLoanReminderList(queryReminder);

        // 转换为明细对象
        List<LitigationReminderDetail> details = new ArrayList<>();
        if (reminders != null && !reminders.isEmpty()) {
            for (LoanReminder reminder : reminders) {
                LitigationReminderDetail detail = convertToDetail(reminder);
                details.add(detail);
            }
        }

        // 按创建时间倒序排序
        details.sort((d1, d2) -> {
            if (d1.getReminderTime() == null && d2.getReminderTime() == null) return 0;
            if (d1.getReminderTime() == null) return 1;
            if (d2.getReminderTime() == null) return -1;
            return d2.getReminderTime().compareTo(d1.getReminderTime());
        });

        return details;
    }

    /**
     * 将LoanReminder转换为LitigationReminderDetail
     */
    private LitigationReminderDetail convertToDetail(LoanReminder reminder) {
        LitigationReminderDetail detail = new LitigationReminderDetail();

        detail.setReminderId(reminder.getId());
        detail.setReminderTime(reminder.getCreateTime());
        detail.setReminderDescription(reminder.getUrgeDescribe());
        detail.setReminderType(reminder.getUrgeStatus());
        detail.setReminderTypeName(getReminderTypeName(reminder.getUrgeStatus()));

        // 设置各种金额
        detail.setBankAmount(reminder.getBMoney());
        detail.setDeductionAmount(reminder.getDMoney());
        detail.setPenaltyAmount(reminder.getPMoney());
        detail.setOtherAmount(reminder.getOMoney());
        detail.setCompensationAmount(reminder.getCMoney());
        detail.setUrgeAmount(reminder.getUrgeMoney());

        detail.setRepaymentStatus(reminder.getRepaymentStatus());
        detail.setRepaymentStatusName(getRepaymentStatusName(reminder.getRepaymentStatus()));
        detail.setApprovalStatus(reminder.getExamineStatus());
        detail.setApprovalStatusName(getApprovalStatusName(reminder.getExamineStatus()));

        detail.setAppointedTime(reminder.getAppointedTime());
        detail.setTrackingTime(reminder.getTrackingTime());
        detail.setCreateBy(reminder.getCreateBy());

        return detail;
    }

    /**
     * 获取催记类型名称
     */
    private String getReminderTypeName(Integer type) {
        if (type == null) return "";
        switch (type) {
            case 1: return "继续跟踪";
            case 2: return "约定还款";
            case 3: return "无法跟进";
            default: return "未知";
        }
    }

    /**
     * 获取还款状态名称
     */
    private String getRepaymentStatusName(Integer status) {
        if (status == null) return "";
        switch (status) {
            case 0: return "未还款";
            case 1: return "已还款";
            case 2: return "部分还款";
            default: return "未知";
        }
    }

    /**
     * 获取审批状态名称
     */
    private String getApprovalStatusName(Integer status) {
        if (status == null) return "";
        switch (status) {
            case 0: return "未审批";
            case 1: return "通过";
            case 2: return "拒绝";
            default: return "未知";
        }
    }
}
