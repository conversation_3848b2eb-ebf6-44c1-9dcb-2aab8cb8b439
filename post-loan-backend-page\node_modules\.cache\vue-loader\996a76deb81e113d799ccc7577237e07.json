{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogView.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationLogView.vue", "mtime": 1754374146667}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0TGl0aWdhdGlvbl9sb2cgfSBmcm9tICdAL2FwaS9saXRpZ2F0aW9uL2xpdGlnYXRpb24nDQppbXBvcnQgTG9hblJlbWluZGVyTG9nIGZyb20gJ0AvbGF5b3V0L2NvbXBvbmVudHMvRGlhbG9nL2xvYW5SZW1pbmRlckxvZy52dWUnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0xpdGlnYXRpb25Mb2dWaWV3JywNCiAgY29tcG9uZW50czogew0KICAgIExvYW5SZW1pbmRlckxvZywNCiAgfSwNCiAgcHJvcHM6IHsNCiAgICBkYXRhOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiAoe30pLA0KICAgIH0sDQogIH0sDQogIHdhdGNoOiB7DQogICAgZGF0YTogew0KICAgICAgaGFuZGxlcihuZXdWYWwpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ25ld1ZhbDonLCBuZXdWYWwpDQogICAgICAgIGxpc3RMaXRpZ2F0aW9uX2xvZyh7IGxpdGlnYXRpb25JZDogbmV3VmFsLuW6j+WPtyB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgdGhpcy5sb2dMaXN0ID0gcmVzLnJvd3MNCiAgICAgICAgICAvLyDojrflj5bmnIDlkI7kuIDkuKrml6Xlv5fnmoTnirbmgIHvvIzlubborr7nva7lr7nlupTnmoTov5vluqbmnaHmraXpqqQNCiAgICAgICAgICBpZiAocmVzLnJvd3MgJiYgcmVzLnJvd3MubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgY29uc3QgbGFzdExvZ1N0YXR1cyA9IHJlcy5yb3dzW3Jlcy5yb3dzLmxlbmd0aCAtIDFdLnN0YXR1cw0KICAgICAgICAgICAgdGhpcy5jdXJyZW50U3RhdHVzID0gbGFzdExvZ1N0YXR1cw0KICAgICAgICAgICAgdGhpcy5zZXRBY3RpdmVTdGVwQnlTdGF0dXMobGFzdExvZ1N0YXR1cykNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgLy8g5rKh5pyJ5pel5b+X5pWw5o2u5pe277yM5riF56m65b2T5YmN54q25oCBDQogICAgICAgICAgICB0aGlzLmN1cnJlbnRTdGF0dXMgPSAnJw0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0sDQogICAgfSwNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdmlzaWJsZTogZmFsc2UsDQogICAgICAvLyDor4norrznirbmgIHmoJHnu5PmnoQNCiAgICAgIHJlbWluZGVyTG9nTG9hbklkOiAnJywNCiAgICAgIGxpdGlnYXRpb25TdGF0dXNUcmVlOiBbDQogICAgICAgIC8vIOeLrOeri+eKtuaAge+8iOS4jemcgOimgeWIhuexu++8iQ0KICAgICAgICB7IGxhYmVsOiAn5pqC5LiN6LW36K+JJywgdmFsdWU6ICfmmoLkuI3otbfor4knIH0sDQogICAgICAgIHsgbGFiZWw6ICfmkqTmoYgnLCB2YWx1ZTogJ+aSpOahiCcgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAn56uL5qGI5YmNJywNCiAgICAgICAgICB2YWx1ZTogJ+eri+ahiOWJjScsDQogICAgICAgICAgY2hpbGRyZW46IFsNCiAgICAgICAgICAgIHsgbGFiZWw6ICflh4blpIfotYTmlpknLCB2YWx1ZTogJ+WHhuWkh+i1hOaWmScgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICflt7Lpgq7lr4QnLCB2YWx1ZTogJ+W3sumCruWvhCcgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICflvoXnq4vmoYgnLCB2YWx1ZTogJ+W+heeri+ahiCcgfSwNCiAgICAgICAgICBdLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICfnq4vmoYgt5Yik5YazJywNCiAgICAgICAgICB2YWx1ZTogJ+eri+ahiC3liKTlhrMnLA0KICAgICAgICAgIGNoaWxkcmVuOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn6I635Y+W5qGI5Lu25Y+3JywgdmFsdWU6ICfojrflj5bmoYjku7blj7cnIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5b6F5Ye65rCR5Yid5Y+3JywgdmFsdWU6ICflvoXlh7rmsJHliJ3lj7cnIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5b6F5byA5bqtJywgdmFsdWU6ICflvoXlvIDluq0nIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5b6F5Ye65rOV6Zmi5paH5LmmJywgdmFsdWU6ICflvoXlh7rms5XpmaLmlofkuaYnIH0sDQogICAgICAgICAgXSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAn5Yik5YazLeaJp+ihjCcsDQogICAgICAgICAgdmFsdWU6ICfliKTlhrMt5omn6KGMJywNCiAgICAgICAgICBjaGlsZHJlbjogWw0KICAgICAgICAgICAgeyBsYWJlbDogJ+W+heaJp+ihjCcsIHZhbHVlOiAn5b6F5omn6KGMJyB9LA0KICAgICAgICAgICAgeyBsYWJlbDogJ+W+heWHuueUs+ivt+S5picsIHZhbHVlOiAn5b6F5Ye655Sz6K+35LmmJyB9LA0KICAgICAgICAgICAgeyBsYWJlbDogJ+W3suaPkOS6pOaJp+ihjOS5picsIHZhbHVlOiAn5bey5o+Q5Lqk5omn6KGM5LmmJyB9LA0KICAgICAgICAgIF0sDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ+aJp+ihjOWQjicsDQogICAgICAgICAgdmFsdWU6ICfmiafooYzlkI4nLA0KICAgICAgICAgIGNoaWxkcmVuOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn5omn6KGM5LitJywgdmFsdWU6ICfmiafooYzkuK0nIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5omn6KGM57uI5pysJywgdmFsdWU6ICfmiafooYznu4jmnKwnIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn57un57ut5omn6KGMJywgdmFsdWU6ICfnu6fnu63miafooYwnIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5b6F6YCB6L2mJywgdmFsdWU6ICflvoXpgIHovaYnIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5b6F5rOV5ouNJywgdmFsdWU6ICflvoXms5Xmi40nIH0sDQogICAgICAgICAgXSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAn57uT5qGIJywNCiAgICAgICAgICB2YWx1ZTogJ+e7k+ahiCcsDQogICAgICAgICAgY2hpbGRyZW46IFsNCiAgICAgICAgICAgIHsgbGFiZWw6ICfms5Xor4nlh4/lhY3nu5PmuIUnLCB2YWx1ZTogJ+azleivieWHj+WFjee7k+a4hScgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICfms5Xor4nlhajpop3nu5PmuIUnLCB2YWx1ZTogJ+azleivieWFqOminee7k+a4hScgfSwNCiAgICAgICAgICBdLA0KICAgICAgICB9LA0KICAgICAgXSwNCiAgICAgIC8vIOW9k+WJjea/gOa0u+eahOatpemqpOe0ouW8lQ0KICAgICAgYWN0aXZlU3RlcDogMCwNCiAgICAgIGxvZ0xpc3Q6IFtdLA0KICAgICAgLy8g5b2T5YmN54q25oCBDQogICAgICBjdXJyZW50U3RhdHVzOiAnJywNCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLy8g5LuO54q25oCB5qCR5Lit5o+Q5Y+W54i26IqC54K555qEbGFiZWzkvZzkuLrov5vluqbmnaHmraXpqqTvvIjmjpLpmaTni6znq4vnirbmgIHvvIkNCiAgICBzdGF0dXNTdGVwcygpIHsNCiAgICAgIHJldHVybiB0aGlzLmxpdGlnYXRpb25TdGF0dXNUcmVlDQogICAgICAgIC5maWx0ZXIoaXRlbSA9PiBpdGVtLmNoaWxkcmVuICYmIGl0ZW0uY2hpbGRyZW4ubGVuZ3RoID4gMCkNCiAgICAgICAgLm1hcChpdGVtID0+IGl0ZW0ubGFiZWwpDQogICAgfSwNCiAgICAvLyDliKTmlq3mmK/lkKbkuLrmkqTmoYjnirbmgIENCiAgICBpc1dpdGhkcmF3bigpIHsNCiAgICAgIHJldHVybiB0aGlzLmN1cnJlbnRTdGF0dXMgPT09ICfmkqTmoYgnDQogICAgfSwNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOagueaNrueKtuaAgeiuvue9rua/gOa0u+eahOatpemqpA0KICAgIHNldEFjdGl2ZVN0ZXBCeVN0YXR1cyhzdGF0dXMpIHsNCiAgICAgIGlmICghc3RhdHVzKSB7DQogICAgICAgIHRoaXMuYWN0aXZlU3RlcCA9IDANCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOaYr+aSpOahiOeKtuaAge+8jOS4jeiuvue9rui/m+W6puadoQ0KICAgICAgaWYgKHN0YXR1cyA9PT0gJ+aSpOahiCcpIHsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIC8vIOafpeaJvueKtuaAgeWvueW6lOeahOeItuiKgueCuee0ouW8lQ0KICAgICAgY29uc3QgcGFyZW50SW5kZXggPSB0aGlzLmZpbmRQYXJlbnRJbmRleEJ5U3RhdHVzKHN0YXR1cykNCiAgICAgIHRoaXMuYWN0aXZlU3RlcCA9IHBhcmVudEluZGV4ID49IDAgPyBwYXJlbnRJbmRleCA6IDANCiAgICB9LA0KDQogICAgLy8g5qC55o2u54q25oCB5om+5Yiw54i26IqC54K555qE57Si5byVDQogICAgZmluZFBhcmVudEluZGV4QnlTdGF0dXMoc3RhdHVzKSB7DQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMubGl0aWdhdGlvblN0YXR1c1RyZWUubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgaXRlbSA9IHRoaXMubGl0aWdhdGlvblN0YXR1c1RyZWVbaV0NCg0KICAgICAgICAvLyDlpoLmnpzmmK/niLboioLngrnmnKzouqsNCiAgICAgICAgaWYgKGl0ZW0ubGFiZWwgPT09IHN0YXR1cyB8fCBpdGVtLnZhbHVlID09PSBzdGF0dXMpIHsNCiAgICAgICAgICByZXR1cm4gaQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5aaC5p6c5pyJ5a2Q6IqC54K577yM5Zyo5a2Q6IqC54K55Lit5p+l5om+DQogICAgICAgIGlmIChpdGVtLmNoaWxkcmVuICYmIGl0ZW0uY2hpbGRyZW4ubGVuZ3RoID4gMCkgew0KICAgICAgICAgIGNvbnN0IGNoaWxkRm91bmQgPSBpdGVtLmNoaWxkcmVuLnNvbWUoY2hpbGQgPT4gY2hpbGQubGFiZWwgPT09IHN0YXR1cyB8fCBjaGlsZC52YWx1ZSA9PT0gc3RhdHVzKQ0KICAgICAgICAgIGlmIChjaGlsZEZvdW5kKSB7DQogICAgICAgICAgICByZXR1cm4gaQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIC0xDQogICAgfSwNCg0KICAgIG9wZW5EaWFsb2coKSB7DQogICAgICB0aGlzLnZpc2libGUgPSB0cnVlDQogICAgfSwNCiAgICBoYW5kbGVVcmdlTG9nKCkgew0KICAgICAgLy8g5omT5byA5YKs6K6w5pel5b+X5a+56K+d5qGG77yM5Lyg5YWl5b2T5YmN55qEIGRhdGENCiAgICAgIHRoaXMucmVtaW5kZXJMb2dMb2FuSWQgPSBTdHJpbmcodGhpcy5kYXRhLua1geeoi+W6j+WPtykNCiAgICAgIHRoaXMuJHJlZnMubG9hblJlbWluZGVyTG9nLm9wZW5Mb2dEaWFsb2coKQ0KICAgIH0sDQogICAgaGFuZGxlQ29uZmlybSgpIHsNCiAgICAgIHJldHVybg0KICAgIH0sDQogICAgaGFuZGxlQ2FuY2VsKCkgew0KICAgICAgdGhpcy52aXNpYmxlID0gZmFsc2UNCiAgICB9LA0KICB9LA0KfQ0K"}, {"version": 3, "sources": ["litigationLogView.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "litigationLogView.vue", "sourceRoot": "src/views/litigation/litigation/modules", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :visible.sync=\"visible\" title=\"日志查看\" width=\"800px\" @close=\"handleCancel\">\r\n      <!-- 进度条或撤诉状态 -->\r\n      <div v-if=\"isWithdrawn\" style=\"text-align: center; margin-bottom: 24px; padding: 20px; background-color: #f5f5f5; border-radius: 4px;\">\r\n        <span style=\"font-size: 16px; color: #909399; font-weight: bold;\">已撤案</span>\r\n      </div>\r\n      <el-steps v-else :active=\"activeStep\" finish-status=\"success\" process-status=\"process\" align-center style=\"margin-bottom: 24px\" class=\"custom-steps\">\r\n        <el-step v-for=\"(item, idx) in statusSteps\" :key=\"idx\" :title=\"item\" />\r\n      </el-steps>\r\n\r\n      <!-- 日志表格 -->\r\n      <el-table :data=\"logList\" border style=\"width: 100%; margin-bottom: 24px\">\r\n        <el-table-column prop=\"createTime\" label=\"时间\" width=\"160\" />\r\n        <el-table-column prop=\"createBy\" label=\"跟踪人\" width=\"120\" />\r\n        <el-table-column prop=\"status\" label=\"跟踪动作\" width=\"120\" />\r\n        <el-table-column prop=\"remark\" label=\"描述\" />\r\n      </el-table>\r\n\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleUrgeLog\">催记日志</el-button>\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确认</el-button>\r\n        <el-button @click=\"handleCancel\">取消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 催记日志组件 -->\r\n    <loan-reminder-log ref=\"loanReminderLog\" :loan-id=\"reminderLogLoanId\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listLitigation_log } from '@/api/litigation/litigation'\r\nimport LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'\r\n\r\nexport default {\r\n  name: 'LitigationLogView',\r\n  components: {\r\n    LoanReminderLog,\r\n  },\r\n  props: {\r\n    data: {\r\n      type: Object,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        console.log('newVal:', newVal)\r\n        listLitigation_log({ litigationId: newVal.序号 }).then(res => {\r\n          this.logList = res.rows\r\n          // 获取最后一个日志的状态，并设置对应的进度条步骤\r\n          if (res.rows && res.rows.length > 0) {\r\n            const lastLogStatus = res.rows[res.rows.length - 1].status\r\n            this.currentStatus = lastLogStatus\r\n            this.setActiveStepByStatus(lastLogStatus)\r\n          } else {\r\n            // 没有日志数据时，清空当前状态\r\n            this.currentStatus = ''\r\n          }\r\n        })\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      // 诉讼状态树结构\r\n      reminderLogLoanId: '',\r\n      litigationStatusTree: [\r\n        // 独立状态（不需要分类）\r\n        { label: '暂不起诉', value: '暂不起诉' },\r\n        { label: '撤案', value: '撤案' },\r\n        {\r\n          label: '立案前',\r\n          value: '立案前',\r\n          children: [\r\n            { label: '准备资料', value: '准备资料' },\r\n            { label: '已邮寄', value: '已邮寄' },\r\n            { label: '待立案', value: '待立案' },\r\n          ],\r\n        },\r\n        {\r\n          label: '立案-判决',\r\n          value: '立案-判决',\r\n          children: [\r\n            { label: '获取案件号', value: '获取案件号' },\r\n            { label: '待出民初号', value: '待出民初号' },\r\n            { label: '待开庭', value: '待开庭' },\r\n            { label: '待出法院文书', value: '待出法院文书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '判决-执行',\r\n          value: '判决-执行',\r\n          children: [\r\n            { label: '待执行', value: '待执行' },\r\n            { label: '待出申请书', value: '待出申请书' },\r\n            { label: '已提交执行书', value: '已提交执行书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '执行后',\r\n          value: '执行后',\r\n          children: [\r\n            { label: '执行中', value: '执行中' },\r\n            { label: '执行终本', value: '执行终本' },\r\n            { label: '继续执行', value: '继续执行' },\r\n            { label: '待送车', value: '待送车' },\r\n            { label: '待法拍', value: '待法拍' },\r\n          ],\r\n        },\r\n        {\r\n          label: '结案',\r\n          value: '结案',\r\n          children: [\r\n            { label: '法诉减免结清', value: '法诉减免结清' },\r\n            { label: '法诉全额结清', value: '法诉全额结清' },\r\n          ],\r\n        },\r\n      ],\r\n      // 当前激活的步骤索引\r\n      activeStep: 0,\r\n      logList: [],\r\n      // 当前状态\r\n      currentStatus: '',\r\n    }\r\n  },\r\n  computed: {\r\n    // 从状态树中提取父节点的label作为进度条步骤（排除独立状态）\r\n    statusSteps() {\r\n      return this.litigationStatusTree\r\n        .filter(item => item.children && item.children.length > 0)\r\n        .map(item => item.label)\r\n    },\r\n    // 判断是否为撤案状态\r\n    isWithdrawn() {\r\n      return this.currentStatus === '撤案'\r\n    },\r\n  },\r\n  methods: {\r\n    // 根据状态设置激活的步骤\r\n    setActiveStepByStatus(status) {\r\n      if (!status) {\r\n        this.activeStep = 0\r\n        return\r\n      }\r\n\r\n      // 如果是撤案状态，不设置进度条\r\n      if (status === '撤案') {\r\n        return\r\n      }\r\n\r\n      // 查找状态对应的父节点索引\r\n      const parentIndex = this.findParentIndexByStatus(status)\r\n      this.activeStep = parentIndex >= 0 ? parentIndex : 0\r\n    },\r\n\r\n    // 根据状态找到父节点的索引\r\n    findParentIndexByStatus(status) {\r\n      for (let i = 0; i < this.litigationStatusTree.length; i++) {\r\n        const item = this.litigationStatusTree[i]\r\n\r\n        // 如果是父节点本身\r\n        if (item.label === status || item.value === status) {\r\n          return i\r\n        }\r\n\r\n        // 如果有子节点，在子节点中查找\r\n        if (item.children && item.children.length > 0) {\r\n          const childFound = item.children.some(child => child.label === status || child.value === status)\r\n          if (childFound) {\r\n            return i\r\n          }\r\n        }\r\n      }\r\n      return -1\r\n    },\r\n\r\n    openDialog() {\r\n      this.visible = true\r\n    },\r\n    handleUrgeLog() {\r\n      // 打开催记日志对话框，传入当前的 data\r\n      this.reminderLogLoanId = String(this.data.流程序号)\r\n      this.$refs.loanReminderLog.openLogDialog()\r\n    },\r\n    handleConfirm() {\r\n      return\r\n    },\r\n    handleCancel() {\r\n      this.visible = false\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 自定义步骤条样式 - 激活节点为蓝色 */\r\n::v-deep .custom-steps .el-step__head.is-process {\r\n  color: #409eff;\r\n  border-color: #409eff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__head.is-process .el-step__icon {\r\n  background-color: #409eff;\r\n  border-color: #409eff;\r\n  color: #fff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__title.is-process {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n"]}]}