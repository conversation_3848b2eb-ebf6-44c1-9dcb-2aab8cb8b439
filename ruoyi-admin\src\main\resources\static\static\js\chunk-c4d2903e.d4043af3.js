(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c4d2903e"],{"0f5f":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.carInfo.plateNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车架号"}},[e._v(e._s(e.carInfo.identityNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"发动机号"}},[e._v(e._s(e.carInfo.engineNumber||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(e._s(e.carStatusLabel))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆位置"}},[e._v(e._s(e.carInfo.carAddress||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(e._s(e.gpsStatusLabel))]):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看车牌照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"行驶证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看行驶证照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"登记证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看登记证照片")])],1):e._e()],1)],1)},r=[],s=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),a("bd52")),n={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var e=this,t=this.carStatusList.find((function(t){return t.value===e.carInfo.carStatus}));return t?t.label:"未知状态"},gpsStatusLabel:function(){var e=this,t=this.gpsStatusList.find((function(t){return t.value===e.carInfo.gpsStatus}));return t?t.label:"未知状态"}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,e&&this.plateNo&&Object(s["e"])(this.plateNo).then((function(e){t.carInfo=e.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},i=n,o=a("2877"),u=Object(o["a"])(i,l,r,!1,null,null,null);t["a"]=u.exports},1791:function(e,t,a){},"2eca":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[e._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},r=[],s=a("bd52"),n={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(s["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},i=n,o=(a("d6fd"),a("2877")),u=Object(o["a"])(i,l,r,!1,null,"8a3d4978",null);t["a"]=u.exports},"9b90":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"nickName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.nickName,callback:function(t){e.$set(e.queryParams,"nickName",t)},expression:"queryParams.nickName"}})],1),a("el-form-item",{attrs:{label:"",prop:"cardID"}},[a("el-input",{attrs:{placeholder:"贷款人身份证号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.cardID,callback:function(t){e.$set(e.queryParams,"cardID",t)},expression:"queryParams.cardID"}})],1),a("el-form-item",{attrs:{label:"",prop:"carNo"}},[a("el-input",{attrs:{placeholder:"车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.carNo,callback:function(t){e.$set(e.queryParams,"carNo",t)},expression:"queryParams.carNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"salesman"}},[a("el-input",{attrs:{placeholder:"业务员",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.urgeUser,callback:function(t){e.$set(e.queryParams,"urgeUser",t)},expression:"queryParams.urgeUser"}})],1),a("el-form-item",{attrs:{label:"",prop:"jgName"}},[a("el-cascader",{attrs:{placeholder:"录单渠道名称",options:e.jgNameList,props:{expandTrigger:"hover",label:"name",value:"id"}},on:{change:e.handleChange},model:{value:e.queryParams.jgName,callback:function(t){e.$set(e.queryParams,"jgName",t)},expression:"queryParams.jgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"loanBank"}},[a("el-select",{attrs:{placeholder:"放款银行",clearable:""},model:{value:e.queryParams.loanBank,callback:function(t){e.$set(e.queryParams,"loanBank",t)},expression:"queryParams.loanBank"}},e._l(e.bankList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"slippageStatus"}},[a("el-select",{attrs:{placeholder:"逾期状态",clearable:""},model:{value:e.queryParams.slippageStatus,callback:function(t){e.$set(e.queryParams,"slippageStatus",t)},expression:"queryParams.slippageStatus"}},e._l(e.slippageList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"followUp"}},[a("el-input",{attrs:{placeholder:"跟催员",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.followUp,callback:function(t){e.$set(e.queryParams,"followUp",t)},expression:"queryParams.followUp"}})],1),a("el-form-item",{attrs:{label:"",prop:"followStatus"}},[a("el-select",{attrs:{placeholder:"跟催类型",clearable:""},model:{value:e.queryParams.followStatus,callback:function(t){e.$set(e.queryParams,"followStatus",t)},expression:"queryParams.followStatus"}},e._l(e.followUpList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"扣款时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.queryParams.allocationTime,callback:function(t){e.$set(e.queryParams,"allocationTime",t)},expression:"queryParams.allocationTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_account_loan:vw_account_loan:remove"],expression:"['vw_account_loan:vw_account_loan:remove']"}],attrs:{type:"primary",plain:"",size:"mini",disabled:e.multiple},on:{click:e.shareOut}},[e._v(" 分配 ")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vw_account_loanList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"逾期状态",align:"center",prop:"applyId"}}),a("el-table-column",{attrs:{label:"催记类型",align:"center",prop:"putoutId"}}),a("el-table-column",{attrs:{label:"跟催员",align:"center",prop:"urgeUser"}}),a("el-table-column",{attrs:{label:"下次跟催时间",align:"center",prop:"customerId"}}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"customerName"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openUserInfo({customerId:t.row.customerId,applyId:t.row.applyId})}}},[e._v(" "+e._s(t.row.customerName)+" ")])]}}])}),a("el-table-column",{attrs:{label:"业务员",align:"center",prop:"loanStatus"}}),a("el-table-column",{attrs:{label:"车牌号码",align:"center",prop:"plateNo"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openCarInfo(t.row.plateNo)}}},[e._v(e._s(t.row.plateNo))])]}}])}),a("el-table-column",{attrs:{label:"GPS状态",align:"center",prop:"partnerId"}}),a("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"currency"}}),a("el-table-column",{attrs:{label:"逾期天数",align:"center",prop:"businessSum",width:"130"}}),a("el-table-column",{attrs:{label:"首期逾期金额",align:"center",prop:"contractAmt",width:"130"}}),a("el-table-column",{attrs:{label:"银行逾期金额",align:"center",prop:"term",width:"130"}}),a("el-table-column",{attrs:{label:"代扣逾期金额",align:"center",prop:"termUnit",width:"130"}}),a("el-table-column",{attrs:{label:"代扣结清金额",align:"center",prop:"rateTermId",width:"130"}}),a("el-table-column",{attrs:{label:"银行结清金额",align:"center",prop:"rptTermId"}}),a("el-table-column",{attrs:{label:"催记类型",align:"center",prop:"rptTermId"}}),a("el-table-column",{attrs:{label:"催记日期",align:"center",prop:"rptTermId"}}),a("el-table-column",{attrs:{label:"指派时间",align:"center",prop:"billDate",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.billDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_account_loan:vw_account_loan:edit"],expression:"['vw_account_loan:vw_account_loan:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.logView(t.row)}}},[e._v("查看催记")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:"工作指派",visible:e.assignOpen,width:"500px","append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.assignOpen=t}}},[a("el-form",{ref:"assignForm",attrs:{model:e.assignFormData,rules:e.assignRules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"贷后人员",prop:"urgeUser"}},[a("el-select",{attrs:{placeholder:"贷后人员",clearable:""},model:{value:e.assignFormData.urgeUser,callback:function(t){e.$set(e.assignFormData,"urgeUser",t)},expression:"assignFormData.urgeUser"}},e._l(e.dcNameList,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitAssignForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancelassign}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{visible:e.previewVisible,title:"图片预览"},on:{"update:visible":function(t){e.previewVisible=t}}},[a("img",{attrs:{width:"100%",src:e.previewImageUrl,alt:"还款凭证"}})]),a("userInfo",{ref:"userInfo",attrs:{visible:e.userInfoVisible,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.userInfoVisible=t}}}),a("carInfo",{ref:"carInfo",attrs:{visible:e.carInfoVisible,title:"车辆信息",plateNo:e.plateNo,permission:"2"},on:{"update:visible":function(t){e.carInfoVisible=t}}}),a("loan-reminder-log",{ref:"loanReminderLog",attrs:{"loan-id":e.currentRow.loanId}})],1)},r=[],s=a("5530"),n=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("bd52")),i=a("2eca"),o=a("0f5f"),u=a("7954"),c={name:"Vw_account_loan",components:{userInfo:i["a"],carInfo:o["a"],LoanReminderLog:u["a"]},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,vw_account_loanList:[],title:"",assignOpen:!1,previewVisible:!1,previewImageUrl:"",queryParams:{pageNum:1,pageSize:10,nickName:null,cardID:null,carNo:null,loanBank:null,jgName:null,slippageStatus:null,urgeUser:null,followStatus:null,allocationTime:null,salesman:null},bankList:[{label:"A银行",value:1},{label:"B银行",value:2}],jgNameList:[],dcNameList:[],slippageList:[{label:"提醒",value:1},{label:"电催",value:2},{label:"上访",value:3},{label:"强制上访",value:4}],followUpList:[{label:"无法跟进",value:1},{label:"约定还款",value:2},{label:"继续联系",value:3}],assignFormData:{urgeUser:null},assignRules:{urgeUser:[{required:!0,message:"请选择电催员",trigger:"change"}]},uploadFiles:[],applyId:"",userInfoVisible:!1,plateNo:"",carInfoVisible:!1,currentRow:{},customerInfo:{customerId:"",applyId:""}}},created:function(){this.getList(),this.getDcc()},methods:{cancelassign:function(){this.assignOpen=!1,this.assignFormData.urgeUser=null},shareOut:function(){this.assignOpen=!0},submitAssignForm:function(){var e=this;this.$refs.assignForm.validate((function(t){if(t){if(0===e.ids.length)return void e.$message.warning("请选择需要分配的记录");var a=Object(s["a"])(Object(s["a"])({},e.assignFormData),{},{ids:e.ids});Object(n["q"])(a).then((function(t){200===t.code?(e.assignOpen=!1,e.$message.success("工作指派成功"),e.assignFormData.urgeUser=null,e.getList()):e.$message.error(t.msg||"工作指派失败")}))}}))},handleChange:function(e){this.queryParams.jgName=e},handleRemove:function(e,t){console.log("移除文件:",e),this.uploadFiles=t},handlePictureCardPreview:function(e){var t;this.previewImageUrl=e.url||(null===(t=e.response)||void 0===t?void 0:t.url)||"",this.previewVisible=!0},getDcc:function(){var e=this;Object(n["j"])(104).then((function(t){200===t.code&&Array.isArray(t.rows)?e.dcNameList=t.rows.map((function(e){return e.userName||e.nickName})):(console.dcNameList("获取DCC列表失败:",t.msg),e.dcNameList=[])})).catch((function(t){console.error("请求发生错误:",t),e.dcNameList=[]}))},getList:function(){var e=this;this.loading=!0,Object(n["l"])(this.queryParams).then((function(t){e.vw_account_loanList=t.rows,e.total=t.total,e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.$refs.queryForm.resetFields(),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.loanId||e.id})),this.single=1!==e.length,this.multiple=!e.length,this.assignFormData.ids=this.ids},logView:function(e){this.currentRow=e,this.$refs.loanReminderLog.openLogDialog()},openUserInfo:function(e){this.customerInfo=e,this.userInfoVisible=!0},openCarInfo:function(e){this.plateNo=e,this.carInfoVisible=!0}}},p=c,m=a("2877"),d=Object(m["a"])(p,l,r,!1,null,null,null);t["default"]=d.exports},d6fd:function(e,t,a){"use strict";a("1791")}}]);