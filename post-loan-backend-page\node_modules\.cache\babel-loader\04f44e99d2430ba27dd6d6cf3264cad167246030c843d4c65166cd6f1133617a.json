{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _slicedToArray2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/slicedToArray.js\"));\nrequire(\"core-js/modules/es.array.join.js\");\nrequire(\"core-js/modules/es.array.map.js\");\nrequire(\"core-js/modules/es.json.stringify.js\");\nrequire(\"core-js/modules/es.number.constructor.js\");\nrequire(\"core-js/modules/es.number.to-fixed.js\");\nrequire(\"core-js/modules/es.object.keys.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.map.js\");\nvar _litigationStatus = _interopRequireDefault(require(\"@/layout/components/Dialog/litigationStatus.vue\"));\nvar _auth = require(\"@/utils/auth\");\nvar _litigation = require(\"@/api/litigation/litigation\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default2 = exports.default = {\n  name: 'LitigationLogForm',\n  components: {\n    litigationStatus: _litigationStatus.default\n  },\n  props: {\n    action: {\n      type: String,\n      default: '/common/ossupload'\n    },\n    data: {\n      type: Object,\n      default: function _default() {}\n    }\n  },\n  data: function data() {\n    return {\n      title: '提交法诉日志',\n      visible: false,\n      loanReminder: {},\n      litigationLog: {},\n      installmentForm: {\n        loanId: null,\n        applyAmount: 0,\n        periodCount: 1,\n        billAmount: '0.00',\n        tailAmount: 0,\n        repayDay: 1,\n        tailPayTime: null,\n        accountType: '',\n        installmentStatus: 2 // 2-法诉分期\n      },\n      uploadUrl: process.env.VUE_APP_BASE_API + this.action,\n      headers: {\n        Authorization: 'Bearer ' + (0, _auth.getToken)()\n      },\n      dialogImageUrl: '',\n      dialogVisible: false\n    };\n  },\n  watch: {\n    data: {\n      handler: function handler(newVal) {\n        if (newVal) {\n          console.log('newVal', newVal);\n          this.loanReminder = {\n            loanId: newVal.流程序号,\n            customerName: newVal.贷款人,\n            channel: newVal.出单渠道,\n            bank: newVal.放款银行,\n            identity: this.$store.state.user.roles[0],\n            repaymentStatus: '',\n            fundsRepayment: '',\n            fundsAmount: '',\n            fundsImage: [],\n            fundsAccountType: '',\n            accountNumber: '',\n            urgeStatus: '',\n            trackingTime: '',\n            urgeDescribe: '',\n            status: 2\n          };\n          this.litigationLog = {\n            loanId: newVal.流程序号,\n            litigationId: newVal.序号,\n            docName: '',\n            docNumber: '',\n            docUploadUrl: [],\n            docEffectiveDate: '',\n            openDate: '',\n            status: ''\n          };\n          // 重置分期表单\n          this.installmentForm = {\n            loanId: newVal && newVal.流程序号 ? newVal.流程序号 : null,\n            applyAmount: 0,\n            periodCount: 1,\n            billAmount: '0.00',\n            tailAmount: 0,\n            repayDay: 1,\n            tailPayTime: null,\n            accountType: '',\n            installmentStatus: 2 // 2-法诉分期\n          };\n        }\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  methods: {\n    // 分期表单计算方法\n    handleInstallmentFormChange: function handleInstallmentFormChange() {\n      var applyAmount = Number(this.installmentForm.applyAmount) || 0;\n      var periodCount = Number(this.installmentForm.periodCount) || 1;\n      var tailAmount = Number(this.installmentForm.tailAmount) || 0;\n      if (applyAmount >= 0 && periodCount >= 1) {\n        this.installmentForm.billAmount = ((applyAmount - tailAmount) / periodCount).toFixed(2);\n      } else {\n        this.installmentForm.billAmount = '0.00';\n      }\n    },\n    // 通用的上传成功处理函数\n    handleUploadSuccess: function handleUploadSuccess(res, file, fileList, formField) {\n      var _formField$split = formField.split('.'),\n        _formField$split2 = (0, _slicedToArray2.default)(_formField$split, 2),\n        obj = _formField$split2[0],\n        prop = _formField$split2[1];\n      this[obj][prop] = fileList;\n    },\n    // 通用的删除处理函数\n    handleRemove: function handleRemove(file, fileList, formField) {\n      var _formField$split3 = formField.split('.'),\n        _formField$split4 = (0, _slicedToArray2.default)(_formField$split3, 2),\n        obj = _formField$split4[0],\n        prop = _formField$split4[1];\n      this[obj][prop] = fileList;\n    },\n    // 上传失败\n    handleUploadError: function handleUploadError() {\n      this.$modal.msgError('上传失败，请重试');\n      this.$modal.closeLoading();\n    },\n    // 图片预览\n    handlePictureCardPreview: function handlePictureCardPreview(file) {\n      this.dialogImageUrl = file.url;\n      this.dialogVisible = true;\n    },\n    /** 提交表单 */submitForm: function submitForm() {\n      var _this = this;\n      var loanReminderCopy = JSON.parse(JSON.stringify(this.loanReminder));\n      var litigationLogCopy = JSON.parse(JSON.stringify(this.litigationLog));\n      loanReminderCopy.fundsImage = loanReminderCopy.fundsImage.map(function (item) {\n        return item.response;\n      }).join(',');\n      litigationLogCopy.docUploadUrl = litigationLogCopy.docUploadUrl.map(function (item) {\n        return item.response;\n      }).join(',');\n      loanReminderCopy.fundsAccountType = loanReminderCopy.fundsAccountType === '其他' ? loanReminderCopy.accountNumber : loanReminderCopy.fundsAccountType;\n      // 将日志描述从 loanReminder 复制到 litigationLog\n      litigationLogCopy.urgeDescribe = loanReminderCopy.urgeDescribe;\n\n      // 如果选择了分期还款，先提交分期申请\n      if (loanReminderCopy.repaymentStatus === '3') {\n        this.submitInstallmentApplication().then(function () {\n          // 分期申请提交成功后，再提交日志\n          _this.submitLitigationLogData(loanReminderCopy, litigationLogCopy);\n        }).catch(function () {\n          _this.$modal.msgError('分期申请提交失败');\n        });\n      } else {\n        // 直接提交日志\n        this.submitLitigationLogData(loanReminderCopy, litigationLogCopy);\n      }\n    },\n    /** 提交分期申请 */submitInstallmentApplication: function submitInstallmentApplication() {\n      var _this2 = this;\n      return new Promise(function (resolve, reject) {\n        // 表单验证\n        if (!_this2.installmentForm.loanId) {\n          _this2.$message.error('贷款ID不能为空');\n          reject();\n          return;\n        }\n        if (!_this2.installmentForm.applyAmount || _this2.installmentForm.applyAmount <= 0) {\n          _this2.$message.error('申请分期金额必须大于0');\n          reject();\n          return;\n        }\n        if (!_this2.installmentForm.periodCount || _this2.installmentForm.periodCount <= 0) {\n          _this2.$message.error('分期期数必须大于0');\n          reject();\n          return;\n        }\n        if (!_this2.installmentForm.repayDay) {\n          _this2.$message.error('请选择每期还款日');\n          reject();\n          return;\n        }\n        if (!_this2.installmentForm.accountType) {\n          _this2.$message.error('请选择账号类型');\n          reject();\n          return;\n        }\n        console.log('提交分期申请数据：', _this2.installmentForm);\n\n        // 调用法诉分期申请API\n        (0, _litigation.addLitigationInstallment)(_this2.installmentForm).then(function (response) {\n          if (response.code === 200) {\n            _this2.$modal.msgSuccess('分期申请提交成功');\n            resolve();\n          } else {\n            _this2.$modal.msgError('分期申请提交失败：' + (response.msg || '未知错误'));\n            reject();\n          }\n        }).catch(function (error) {\n          console.error('分期申请提交失败:', error);\n          _this2.$modal.msgError('分期申请提交失败，请稍后重试');\n          reject();\n        });\n      });\n    },\n    /** 提交法诉日志数据 */submitLitigationLogData: function submitLitigationLogData(loanReminderCopy, litigationLogCopy) {\n      var _this3 = this;\n      console.log('提交表单数据：', loanReminderCopy);\n      console.log('提交表单数据：', litigationLogCopy);\n      (0, _litigation.submitLitigationLog)({\n        loanReminder: loanReminderCopy,\n        litigationLog: litigationLogCopy\n      }).then(function (res) {\n        _this3.$modal.msgSuccess('提交成功');\n        _this3.visible = false;\n        _this3.resetForm();\n      });\n    },\n    /** 取消操作 */cancel: function cancel() {\n      this.visible = false;\n      this.resetForm();\n      return;\n    },\n    /** 重置表单 */resetForm: function resetForm() {\n      this.loanReminder = {\n        fundsImage: []\n      };\n      this.litigationLog = {\n        docUploadUrl: []\n      };\n      this.installmentForm = {\n        loanId: null,\n        applyAmount: 0,\n        periodCount: 1,\n        billAmount: 0,\n        tailAmount: 0,\n        repayDay: 1,\n        tailPayTime: null,\n        accountType: '',\n        installmentStatus: 2,\n        // 2-法诉分期\n        status: 1 // 1-待审核\n      };\n    },\n    /** 统一打开弹窗的方法 */openDialog: function openDialog() {\n      this.visible = true;\n      return;\n    },\n    /** 处理文件超出限制 */handleExceed: function handleExceed(files, fileList) {\n      this.$message.warning('只能上传一个文件');\n      return;\n    }\n  }\n};", "map": {"version": 3, "names": ["_litigationStatus", "_interopRequireDefault", "require", "_auth", "_litigation", "name", "components", "litigationStatus", "props", "action", "type", "String", "default", "data", "Object", "title", "visible", "loanReminder", "litigationLog", "installmentForm", "loanId", "applyAmount", "periodCount", "billAmount", "tailAmount", "repayDay", "tailPayTime", "accountType", "installmentStatus", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "headers", "Authorization", "getToken", "dialogImageUrl", "dialogVisible", "watch", "handler", "newVal", "console", "log", "流程序号", "customerName", "贷款人", "channel", "出单渠道", "bank", "放款银行", "identity", "$store", "state", "user", "roles", "repaymentStatus", "fundsRepayment", "fundsAmount", "fundsImage", "fundsAccountType", "accountNumber", "urgeStatus", "trackingTime", "urgeDescribe", "status", "litigationId", "序号", "doc<PERSON>ame", "docNumber", "docUploadUrl", "docEffectiveDate", "openDate", "immediate", "deep", "methods", "handleInstallmentFormChange", "Number", "toFixed", "handleUploadSuccess", "res", "file", "fileList", "formField", "_formField$split", "split", "_formField$split2", "_slicedToArray2", "obj", "prop", "handleRemove", "_formField$split3", "_formField$split4", "handleUploadError", "$modal", "msgError", "closeLoading", "handlePictureCardPreview", "url", "submitForm", "_this", "loanReminderCopy", "JSON", "parse", "stringify", "litigationLogCopy", "map", "item", "response", "join", "submitInstallmentApplication", "then", "submitLitigationLogData", "catch", "_this2", "Promise", "resolve", "reject", "$message", "error", "addLitigationInstallment", "code", "msgSuccess", "msg", "_this3", "submitLitigationLog", "resetForm", "cancel", "openDialog", "handleExceed", "files", "warning"], "sources": ["src/views/litigation/litigation/modules/litigationLogForm.vue"], "sourcesContent": ["<template>\r\n  <el-dialog :title=\"title\" :visible.sync=\"visible\" width=\"800px\" append-to-body @close=\"resetForm\">\r\n    <el-form ref=\"form\" :model=\"loanReminder\" label-width=\"120px\">\r\n      <!-- 第一部分：文书相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-document\"></i>\r\n        贷款信息\r\n      </el-divider>\r\n      <!-- 非填入字段 -->\r\n      <el-descriptions title=\"\" :column=\"3\" border>\r\n        <el-descriptions-item label=\"贷款人\">\r\n          {{ loanReminder.customerName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"出单渠道\">\r\n          {{ loanReminder.channel }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"放款银行\">\r\n          {{ loanReminder.bank }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <!-- 第一部分：文书相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-document\"></i>\r\n        文书信息\r\n      </el-divider>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"变更法诉状态\">\r\n            <litigation-status v-model=\"litigationLog.status\" placeholder=\"请选择法诉状态\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"文书名称\">\r\n            <el-select v-model=\"litigationLog.docName\" placeholder=\"请选择文书名称\" style=\"width: 100%\">\r\n              <el-option label=\"诉前调号\" value=\"诉前调号\" />\r\n              <el-option label=\"民初号\" value=\"民初号\" />\r\n              <el-option label=\"执行号\" value=\"执行号\" />\r\n              <el-option label=\"执保号\" value=\"执保号\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"文书号\">\r\n            <el-input v-model=\"litigationLog.docNumber\" placeholder=\"请输入文书号\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"文书生效\">\r\n            <el-date-picker v-model=\"litigationLog.docEffectiveDate\" type=\"date\" placeholder=\"选择日期\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\" v-if=\"litigationLog.status === '待出法院文书'\">\r\n          <el-form-item label=\"登记开庭时间\">\r\n            <el-date-picker v-model=\"litigationLog.openDate\" type=\"datetime\" placeholder=\"选择开庭时间\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"上传文书\">\r\n            <el-upload\r\n              :data=\"data\"\r\n              :action=\"uploadUrl\"\r\n              :headers=\"headers\"\r\n              :limit=\"1\"\r\n              :file-list=\"litigationLog.docUploadUrl\"\r\n              :on-success=\"(res, file, fileList) => handleUploadSuccess(res, file, fileList, 'litigationLog.docUploadUrl')\"\r\n              :on-remove=\"(file, fileList) => handleRemove(file, fileList, 'litigationLog.docUploadUrl')\"\r\n              :on-error=\"handleUploadError\">\r\n              <el-button size=\"small\" type=\"primary\" :disabled=\"litigationLog.docUploadUrl.length >= 1\">点击上传</el-button>\r\n            </el-upload>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第二部分：还款相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-money\"></i>\r\n        还款信息\r\n      </el-divider>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"还款类型\">\r\n            <el-select v-model=\"loanReminder.repaymentStatus\" placeholder=\"请选择还款类型\" style=\"width: 100%\">\r\n              <el-option label=\"部分还款\" value=\"2\" />\r\n              <el-option label=\"分期还款\" value=\"3\" />\r\n              <el-option label=\"协商买车\" value=\"4\" />\r\n              <el-option label=\"法诉结清\" value=\"5\" />\r\n              <el-option label=\"法诉减免结清\" value=\"6\" />\r\n              <el-option label=\"拍卖回款\" value=\"7\" />\r\n              <el-option label=\"法院划扣\" value=\"8\" />\r\n              <el-option label=\"其他分配回款\" value=\"9\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"款项明细类型\">\r\n            <el-select v-model=\"loanReminder.fundsRepayment\" placeholder=\"请选择款项明细类型\" style=\"width: 100%\">\r\n              <el-option label=\"律师费\" value=\"律师费\" />\r\n              <el-option label=\"法诉费\" value=\"法诉费\" />\r\n              <el-option label=\"保全费\" value=\"保全费\" />\r\n              <el-option label=\"布控费\" value=\"布控费\" />\r\n              <el-option label=\"公告费\" value=\"公告费\" />\r\n              <el-option label=\"评估费\" value=\"评估费\" />\r\n              <el-option label=\"执行费\" value=\"执行费\" />\r\n              <el-option label=\"违约金\" value=\"违约金\" />\r\n              <el-option label=\"担保费\" value=\"担保费\" />\r\n              <el-option label=\"居间费\" value=\"居间费\" />\r\n              <el-option label=\"代偿金\" value=\"代偿金\" />\r\n              <el-option label=\"判决金额\" value=\"判决金额\" />\r\n              <el-option label=\"利息\" value=\"利息\" />\r\n              <el-option label=\"其他欠款\" value=\"其他欠款\" />\r\n              <el-option label=\"保险费\" value=\"保险费\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"金额\">\r\n            <el-input-number v-model=\"loanReminder.fundsAmount\" :min=\"0\" :precision=\"2\" style=\"width: 100%\" placeholder=\"请输入金额\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"账号类型\">\r\n            <el-select v-model=\"loanReminder.fundsAccountType\" placeholder=\"请选择账号类型\" style=\"width: 100%\">\r\n              <el-option label=\"银行账户\" value=\"银行账户\" />\r\n              <el-option label=\"微信\" value=\"微信\" />\r\n              <el-option label=\"支付宝\" value=\"支付宝\" />\r\n              <el-option label=\"其他\" value=\"其他\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"账号\">\r\n            <el-input v-model=\"loanReminder.accountNumber\" :disabled=\"loanReminder.fundsAccountType !== '其他'\" placeholder=\"请输入账号\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"还款凭据\">\r\n            <el-upload\r\n              :data=\"data\"\r\n              :action=\"uploadUrl\"\r\n              :headers=\"headers\"\r\n              list-type=\"picture-card\"\r\n              :file-list=\"loanReminder.fundsImage\"\r\n              :on-preview=\"handlePictureCardPreview\"\r\n              :on-success=\"(res, file, fileList) => handleUploadSuccess(res, file, fileList, 'loanReminder.fundsImage')\"\r\n              :on-remove=\"(file, fileList) => handleRemove(file, fileList, 'loanReminder.fundsImage')\"\r\n              :on-error=\"handleUploadError\">\r\n              <i class=\"el-icon-plus\"></i>\r\n            </el-upload>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 分期表单 - 当选择分期还款时显示 -->\r\n      <div v-if=\"loanReminder.repaymentStatus === '3'\">\r\n        <el-divider content-position=\"left\">\r\n          <i class=\"el-icon-s-order\"></i>\r\n          分期申请信息\r\n        </el-divider>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"申请分期金额\">\r\n              <el-input-number\r\n                v-model=\"installmentForm.applyAmount\"\r\n                :min=\"0\"\r\n                :precision=\"2\"\r\n                :step=\"100\"\r\n                :controls-position=\"'right'\"\r\n                placeholder=\"请输入申请分期金额\"\r\n                @input=\"handleInstallmentFormChange\"\r\n                style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"分期期数\">\r\n              <el-input-number\r\n                v-model=\"installmentForm.periodCount\"\r\n                :min=\"1\"\r\n                :precision=\"0\"\r\n                :step=\"1\"\r\n                :controls-position=\"'right'\"\r\n                placeholder=\"请输入分期期数\"\r\n                @input=\"handleInstallmentFormChange\"\r\n                style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"每期账单金额\">\r\n              <el-input v-model=\"installmentForm.billAmount\" placeholder=\"每期账单金额\" disabled />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"每期还款日\">\r\n              <el-select v-model=\"installmentForm.repayDay\" placeholder=\"请选择每期还款日\" style=\"width: 100%\">\r\n                <el-option v-for=\"day in 28\" :key=\"day\" :label=\"`每月${day}号`\" :value=\"day\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"尾款金额\">\r\n              <el-input-number\r\n                v-model=\"installmentForm.tailAmount\"\r\n                :min=\"0\"\r\n                :precision=\"2\"\r\n                :step=\"100\"\r\n                :controls-position=\"'right'\"\r\n                placeholder=\"请输入尾款金额\"\r\n                @input=\"handleInstallmentFormChange\"\r\n                style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"尾款支付时间\">\r\n              <el-date-picker\r\n                v-model=\"installmentForm.tailPayTime\"\r\n                type=\"date\"\r\n                placeholder=\"选择尾款支付时间\"\r\n                style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\">\r\n              <el-select v-model=\"installmentForm.accountType\" placeholder=\"请选择账号类型\" style=\"width: 100%\">\r\n                <el-option label=\"银行账户\" value=\"银行账户\" />\r\n                <el-option label=\"微信\" value=\"微信\" />\r\n                <el-option label=\"支付宝\" value=\"支付宝\" />\r\n                <el-option label=\"其他\" value=\"其他\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 第三部分：日志相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-notebook-2\"></i>\r\n        日志信息\r\n      </el-divider>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"日志类型\">\r\n            <el-select v-model=\"loanReminder.urgeStatus\" placeholder=\"请选择日志类型\" style=\"width: 100%\">\r\n              <el-option label=\"继续跟踪\" value=\"1\" />\r\n              <el-option label=\"约定还款\" value=\"2\" />\r\n              <el-option label=\"无法跟进\" value=\"3\" />\r\n              <el-option label=\"暂时无需跟进\" value=\"4\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"下次跟进时间\">\r\n            <el-date-picker v-model=\"loanReminder.trackingTime\" type=\"datetime\" placeholder=\"选择跟进时间\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"日志描述\">\r\n            <el-input v-model=\"loanReminder.urgeDescribe\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入日志描述\" maxlength=\"500\" show-word-limit />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"cancel\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n    </div>\r\n    <!-- 图片预览 -->\r\n    <el-dialog :visible.sync=\"dialogVisible\">\r\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt=\"\" />\r\n    </el-dialog>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport litigationStatus from '@/layout/components/Dialog/litigationStatus.vue'\r\nimport { getToken } from '@/utils/auth'\r\nimport { submitLitigationLog, addLitigationInstallment } from '@/api/litigation/litigation'\r\n\r\nexport default {\r\n  name: 'LitigationLogForm',\r\n  components: {\r\n    litigationStatus,\r\n  },\r\n  props: {\r\n    action: {\r\n      type: String,\r\n      default: '/common/ossupload',\r\n    },\r\n    data: {\r\n      type: Object,\r\n      default: () => {},\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      title: '提交法诉日志',\r\n      visible: false,\r\n      loanReminder: {},\r\n      litigationLog: {},\r\n      installmentForm: {\r\n        loanId: null,\r\n        applyAmount: 0,\r\n        periodCount: 1,\r\n        billAmount: '0.00',\r\n        tailAmount: 0,\r\n        repayDay: 1,\r\n        tailPayTime: null,\r\n        accountType: '',\r\n        installmentStatus: 2 // 2-法诉分期\r\n      },\r\n      uploadUrl: process.env.VUE_APP_BASE_API + this.action,\r\n      headers: {\r\n        Authorization: 'Bearer ' + getToken(),\r\n      },\r\n      dialogImageUrl: '',\r\n      dialogVisible: false,\r\n    }\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          console.log('newVal', newVal)\r\n          this.loanReminder = {\r\n            loanId: newVal.流程序号,\r\n            customerName: newVal.贷款人,\r\n            channel: newVal.出单渠道,\r\n            bank: newVal.放款银行,\r\n            identity: this.$store.state.user.roles[0],\r\n            repaymentStatus: '',\r\n            fundsRepayment: '',\r\n            fundsAmount: '',\r\n            fundsImage: [],\r\n            fundsAccountType: '',\r\n            accountNumber: '',\r\n            urgeStatus: '',\r\n            trackingTime: '',\r\n            urgeDescribe: '',\r\n            status: 2,\r\n          }\r\n          this.litigationLog = {\r\n            loanId: newVal.流程序号,\r\n            litigationId: newVal.序号,\r\n            docName: '',\r\n            docNumber: '',\r\n            docUploadUrl: [],\r\n            docEffectiveDate: '',\r\n            openDate: '',\r\n            status: '',\r\n          }\r\n          // 重置分期表单\r\n          this.installmentForm = {\r\n            loanId: newVal && newVal.流程序号 ? newVal.流程序号 : null,\r\n            applyAmount: 0,\r\n            periodCount: 1,\r\n            billAmount: '0.00',\r\n            tailAmount: 0,\r\n            repayDay: 1,\r\n            tailPayTime: null,\r\n            accountType: '',\r\n            installmentStatus: 2 // 2-法诉分期\r\n          }\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true,\r\n    },\r\n  },\r\n  methods: {\r\n    // 分期表单计算方法\r\n    handleInstallmentFormChange() {\r\n      const applyAmount = Number(this.installmentForm.applyAmount) || 0\r\n      const periodCount = Number(this.installmentForm.periodCount) || 1\r\n      const tailAmount = Number(this.installmentForm.tailAmount) || 0\r\n      if (applyAmount >= 0 && periodCount >= 1) {\r\n        this.installmentForm.billAmount = ((applyAmount - tailAmount) / periodCount).toFixed(2)\r\n      } else {\r\n        this.installmentForm.billAmount = '0.00'\r\n      }\r\n    },\r\n    // 通用的上传成功处理函数\r\n    handleUploadSuccess(res, file, fileList, formField) {\r\n      const [obj, prop] = formField.split('.')\r\n      this[obj][prop] = fileList\r\n    },\r\n    // 通用的删除处理函数\r\n    handleRemove(file, fileList, formField) {\r\n      const [obj, prop] = formField.split('.')\r\n      this[obj][prop] = fileList\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError('上传失败，请重试')\r\n      this.$modal.closeLoading()\r\n    },\r\n    // 图片预览\r\n    handlePictureCardPreview(file) {\r\n      this.dialogImageUrl = file.url\r\n      this.dialogVisible = true\r\n    },\r\n    /** 提交表单 */\r\n    submitForm() {\r\n      const loanReminderCopy = JSON.parse(JSON.stringify(this.loanReminder))\r\n      const litigationLogCopy = JSON.parse(JSON.stringify(this.litigationLog))\r\n      loanReminderCopy.fundsImage = loanReminderCopy.fundsImage.map(item => item.response).join(',')\r\n      litigationLogCopy.docUploadUrl = litigationLogCopy.docUploadUrl.map(item => item.response).join(',')\r\n      loanReminderCopy.fundsAccountType =\r\n        loanReminderCopy.fundsAccountType === '其他' ? loanReminderCopy.accountNumber : loanReminderCopy.fundsAccountType\r\n      // 将日志描述从 loanReminder 复制到 litigationLog\r\n      litigationLogCopy.urgeDescribe = loanReminderCopy.urgeDescribe\r\n\r\n      // 如果选择了分期还款，先提交分期申请\r\n      if (loanReminderCopy.repaymentStatus === '3') {\r\n        this.submitInstallmentApplication().then(() => {\r\n          // 分期申请提交成功后，再提交日志\r\n          this.submitLitigationLogData(loanReminderCopy, litigationLogCopy)\r\n        }).catch(() => {\r\n          this.$modal.msgError('分期申请提交失败')\r\n        })\r\n      } else {\r\n        // 直接提交日志\r\n        this.submitLitigationLogData(loanReminderCopy, litigationLogCopy)\r\n      }\r\n    },\r\n\r\n    /** 提交分期申请 */\r\n    submitInstallmentApplication() {\r\n      return new Promise((resolve, reject) => {\r\n        // 表单验证\r\n        if (!this.installmentForm.loanId) {\r\n          this.$message.error('贷款ID不能为空')\r\n          reject()\r\n          return\r\n        }\r\n        if (!this.installmentForm.applyAmount || this.installmentForm.applyAmount <= 0) {\r\n          this.$message.error('申请分期金额必须大于0')\r\n          reject()\r\n          return\r\n        }\r\n        if (!this.installmentForm.periodCount || this.installmentForm.periodCount <= 0) {\r\n          this.$message.error('分期期数必须大于0')\r\n          reject()\r\n          return\r\n        }\r\n        if (!this.installmentForm.repayDay) {\r\n          this.$message.error('请选择每期还款日')\r\n          reject()\r\n          return\r\n        }\r\n        if (!this.installmentForm.accountType) {\r\n          this.$message.error('请选择账号类型')\r\n          reject()\r\n          return\r\n        }\r\n\r\n        console.log('提交分期申请数据：', this.installmentForm)\r\n\r\n        // 调用法诉分期申请API\r\n        addLitigationInstallment(this.installmentForm).then(response => {\r\n          if (response.code === 200) {\r\n            this.$modal.msgSuccess('分期申请提交成功')\r\n            resolve()\r\n          } else {\r\n            this.$modal.msgError('分期申请提交失败：' + (response.msg || '未知错误'))\r\n            reject()\r\n          }\r\n        }).catch(error => {\r\n          console.error('分期申请提交失败:', error)\r\n          this.$modal.msgError('分期申请提交失败，请稍后重试')\r\n          reject()\r\n        })\r\n      })\r\n    },\r\n\r\n    /** 提交法诉日志数据 */\r\n    submitLitigationLogData(loanReminderCopy, litigationLogCopy) {\r\n      console.log('提交表单数据：', loanReminderCopy)\r\n      console.log('提交表单数据：', litigationLogCopy)\r\n      submitLitigationLog({ loanReminder: loanReminderCopy, litigationLog: litigationLogCopy }).then(res => {\r\n        this.$modal.msgSuccess('提交成功')\r\n        this.visible = false\r\n        this.resetForm()\r\n      })\r\n    },\r\n    /** 取消操作 */\r\n    cancel() {\r\n      this.visible = false\r\n      this.resetForm()\r\n      return\r\n    },\r\n    /** 重置表单 */\r\n    resetForm() {\r\n      this.loanReminder = {\r\n        fundsImage: [],\r\n      }\r\n      this.litigationLog = {\r\n        docUploadUrl: [],\r\n      }\r\n      this.installmentForm = {\r\n        loanId: null,\r\n        applyAmount: 0,\r\n        periodCount: 1,\r\n        billAmount: 0,\r\n        tailAmount: 0,\r\n        repayDay: 1,\r\n        tailPayTime: null,\r\n        accountType: '',\r\n        installmentStatus: 2, // 2-法诉分期\r\n        status: 1 // 1-待审核\r\n      }\r\n    },\r\n    /** 统一打开弹窗的方法 */\r\n    openDialog() {\r\n      this.visible = true\r\n      return\r\n    },\r\n    /** 处理文件超出限制 */\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning('只能上传一个文件')\r\n      return\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.el-divider__text {\r\n  padding: 0 15px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.el-upload {\r\n  width: 100%;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AA8SA,IAAAA,iBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,gBAAA,EAAAA;EACA;EACAC,KAAA;IACAC,MAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,IAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,WAAAA,SAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAE,KAAA;MACAC,OAAA;MACAC,YAAA;MACAC,aAAA;MACAC,eAAA;QACAC,MAAA;QACAC,WAAA;QACAC,WAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,WAAA;QACAC,WAAA;QACAC,iBAAA;MACA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA,QAAAvB,MAAA;MACAwB,OAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;MACAC,cAAA;MACAC,aAAA;IACA;EACA;EACAC,KAAA;IACAzB,IAAA;MACA0B,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA;UACAC,OAAA,CAAAC,GAAA,WAAAF,MAAA;UACA,KAAAvB,YAAA;YACAG,MAAA,EAAAoB,MAAA,CAAAG,IAAA;YACAC,YAAA,EAAAJ,MAAA,CAAAK,GAAA;YACAC,OAAA,EAAAN,MAAA,CAAAO,IAAA;YACAC,IAAA,EAAAR,MAAA,CAAAS,IAAA;YACAC,QAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA;YACAC,eAAA;YACAC,cAAA;YACAC,WAAA;YACAC,UAAA;YACAC,gBAAA;YACAC,aAAA;YACAC,UAAA;YACAC,YAAA;YACAC,YAAA;YACAC,MAAA;UACA;UACA,KAAA9C,aAAA;YACAE,MAAA,EAAAoB,MAAA,CAAAG,IAAA;YACAsB,YAAA,EAAAzB,MAAA,CAAA0B,EAAA;YACAC,OAAA;YACAC,SAAA;YACAC,YAAA;YACAC,gBAAA;YACAC,QAAA;YACAP,MAAA;UACA;UACA;UACA,KAAA7C,eAAA;YACAC,MAAA,EAAAoB,MAAA,IAAAA,MAAA,CAAAG,IAAA,GAAAH,MAAA,CAAAG,IAAA;YACAtB,WAAA;YACAC,WAAA;YACAC,UAAA;YACAC,UAAA;YACAC,QAAA;YACAC,WAAA;YACAC,WAAA;YACAC,iBAAA;UACA;QACA;MACA;MACA4C,SAAA;MACAC,IAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,2BAAA,WAAAA,4BAAA;MACA,IAAAtD,WAAA,GAAAuD,MAAA,MAAAzD,eAAA,CAAAE,WAAA;MACA,IAAAC,WAAA,GAAAsD,MAAA,MAAAzD,eAAA,CAAAG,WAAA;MACA,IAAAE,UAAA,GAAAoD,MAAA,MAAAzD,eAAA,CAAAK,UAAA;MACA,IAAAH,WAAA,SAAAC,WAAA;QACA,KAAAH,eAAA,CAAAI,UAAA,KAAAF,WAAA,GAAAG,UAAA,IAAAF,WAAA,EAAAuD,OAAA;MACA;QACA,KAAA1D,eAAA,CAAAI,UAAA;MACA;IACA;IACA;IACAuD,mBAAA,WAAAA,oBAAAC,GAAA,EAAAC,IAAA,EAAAC,QAAA,EAAAC,SAAA;MACA,IAAAC,gBAAA,GAAAD,SAAA,CAAAE,KAAA;QAAAC,iBAAA,OAAAC,eAAA,CAAA1E,OAAA,EAAAuE,gBAAA;QAAAI,GAAA,GAAAF,iBAAA;QAAAG,IAAA,GAAAH,iBAAA;MACA,KAAAE,GAAA,EAAAC,IAAA,IAAAP,QAAA;IACA;IACA;IACAQ,YAAA,WAAAA,aAAAT,IAAA,EAAAC,QAAA,EAAAC,SAAA;MACA,IAAAQ,iBAAA,GAAAR,SAAA,CAAAE,KAAA;QAAAO,iBAAA,OAAAL,eAAA,CAAA1E,OAAA,EAAA8E,iBAAA;QAAAH,GAAA,GAAAI,iBAAA;QAAAH,IAAA,GAAAG,iBAAA;MACA,KAAAJ,GAAA,EAAAC,IAAA,IAAAP,QAAA;IACA;IACA;IACAW,iBAAA,WAAAA,kBAAA;MACA,KAAAC,MAAA,CAAAC,QAAA;MACA,KAAAD,MAAA,CAAAE,YAAA;IACA;IACA;IACAC,wBAAA,WAAAA,yBAAAhB,IAAA;MACA,KAAA5C,cAAA,GAAA4C,IAAA,CAAAiB,GAAA;MACA,KAAA5D,aAAA;IACA;IACA,WACA6D,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,gBAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAtF,YAAA;MACA,IAAAuF,iBAAA,GAAAH,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAArF,aAAA;MACAkF,gBAAA,CAAA1C,UAAA,GAAA0C,gBAAA,CAAA1C,UAAA,CAAA+C,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,QAAA;MAAA,GAAAC,IAAA;MACAJ,iBAAA,CAAAnC,YAAA,GAAAmC,iBAAA,CAAAnC,YAAA,CAAAoC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,QAAA;MAAA,GAAAC,IAAA;MACAR,gBAAA,CAAAzC,gBAAA,GACAyC,gBAAA,CAAAzC,gBAAA,YAAAyC,gBAAA,CAAAxC,aAAA,GAAAwC,gBAAA,CAAAzC,gBAAA;MACA;MACA6C,iBAAA,CAAAzC,YAAA,GAAAqC,gBAAA,CAAArC,YAAA;;MAEA;MACA,IAAAqC,gBAAA,CAAA7C,eAAA;QACA,KAAAsD,4BAAA,GAAAC,IAAA;UACA;UACAX,KAAA,CAAAY,uBAAA,CAAAX,gBAAA,EAAAI,iBAAA;QACA,GAAAQ,KAAA;UACAb,KAAA,CAAAN,MAAA,CAAAC,QAAA;QACA;MACA;QACA;QACA,KAAAiB,uBAAA,CAAAX,gBAAA,EAAAI,iBAAA;MACA;IACA;IAEA,aACAK,4BAAA,WAAAA,6BAAA;MAAA,IAAAI,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA;QACA,KAAAH,MAAA,CAAA9F,eAAA,CAAAC,MAAA;UACA6F,MAAA,CAAAI,QAAA,CAAAC,KAAA;UACAF,MAAA;UACA;QACA;QACA,KAAAH,MAAA,CAAA9F,eAAA,CAAAE,WAAA,IAAA4F,MAAA,CAAA9F,eAAA,CAAAE,WAAA;UACA4F,MAAA,CAAAI,QAAA,CAAAC,KAAA;UACAF,MAAA;UACA;QACA;QACA,KAAAH,MAAA,CAAA9F,eAAA,CAAAG,WAAA,IAAA2F,MAAA,CAAA9F,eAAA,CAAAG,WAAA;UACA2F,MAAA,CAAAI,QAAA,CAAAC,KAAA;UACAF,MAAA;UACA;QACA;QACA,KAAAH,MAAA,CAAA9F,eAAA,CAAAM,QAAA;UACAwF,MAAA,CAAAI,QAAA,CAAAC,KAAA;UACAF,MAAA;UACA;QACA;QACA,KAAAH,MAAA,CAAA9F,eAAA,CAAAQ,WAAA;UACAsF,MAAA,CAAAI,QAAA,CAAAC,KAAA;UACAF,MAAA;UACA;QACA;QAEA3E,OAAA,CAAAC,GAAA,cAAAuE,MAAA,CAAA9F,eAAA;;QAEA;QACA,IAAAoG,oCAAA,EAAAN,MAAA,CAAA9F,eAAA,EAAA2F,IAAA,WAAAH,QAAA;UACA,IAAAA,QAAA,CAAAa,IAAA;YACAP,MAAA,CAAApB,MAAA,CAAA4B,UAAA;YACAN,OAAA;UACA;YACAF,MAAA,CAAApB,MAAA,CAAAC,QAAA,gBAAAa,QAAA,CAAAe,GAAA;YACAN,MAAA;UACA;QACA,GAAAJ,KAAA,WAAAM,KAAA;UACA7E,OAAA,CAAA6E,KAAA,cAAAA,KAAA;UACAL,MAAA,CAAApB,MAAA,CAAAC,QAAA;UACAsB,MAAA;QACA;MACA;IACA;IAEA,eACAL,uBAAA,WAAAA,wBAAAX,gBAAA,EAAAI,iBAAA;MAAA,IAAAmB,MAAA;MACAlF,OAAA,CAAAC,GAAA,YAAA0D,gBAAA;MACA3D,OAAA,CAAAC,GAAA,YAAA8D,iBAAA;MACA,IAAAoB,+BAAA;QAAA3G,YAAA,EAAAmF,gBAAA;QAAAlF,aAAA,EAAAsF;MAAA,GAAAM,IAAA,WAAA/B,GAAA;QACA4C,MAAA,CAAA9B,MAAA,CAAA4B,UAAA;QACAE,MAAA,CAAA3G,OAAA;QACA2G,MAAA,CAAAE,SAAA;MACA;IACA;IACA,WACAC,MAAA,WAAAA,OAAA;MACA,KAAA9G,OAAA;MACA,KAAA6G,SAAA;MACA;IACA;IACA,WACAA,SAAA,WAAAA,UAAA;MACA,KAAA5G,YAAA;QACAyC,UAAA;MACA;MACA,KAAAxC,aAAA;QACAmD,YAAA;MACA;MACA,KAAAlD,eAAA;QACAC,MAAA;QACAC,WAAA;QACAC,WAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,WAAA;QACAC,WAAA;QACAC,iBAAA;QAAA;QACAoC,MAAA;MACA;IACA;IACA,gBACA+D,UAAA,WAAAA,WAAA;MACA,KAAA/G,OAAA;MACA;IACA;IACA,eACAgH,YAAA,WAAAA,aAAAC,KAAA,EAAAhD,QAAA;MACA,KAAAoC,QAAA,CAAAa,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}