(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3010cbac"],{"2a7b":function(e,t,a){"use strict";a("a8b3")},a1e7:function(e,t,a){"use strict";a.d(t,"d",(function(){return l})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return s})),a.d(t,"e",(function(){return i})),a.d(t,"b",(function(){return o}));var r=a("b775");function l(e){return Object(r["a"])({url:"/car_team/car_team/list",method:"get",params:e})}function n(e){return Object(r["a"])({url:"/car_team/car_team/"+e,method:"get"})}function s(e){return Object(r["a"])({url:"/car_team/car_team",method:"post",data:e})}function i(e){return Object(r["a"])({url:"/car_team/car_team",method:"put",data:e})}function o(e){return Object(r["a"])({url:"/car_team/car_team/"+e,method:"delete"})}},a8b3:function(e,t,a){},c953:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"teamName"}},[a("el-input",{attrs:{placeholder:"团队名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.teamName,callback:function(t){e.$set(e.queryParams,"teamName",t)},expression:"queryParams.teamName"}})],1),a("el-form-item",{attrs:{label:"",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择车队状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.statusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"启用时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.queryParams.originallyTime,callback:function(t){e.$set(e.queryParams,"originallyTime",t)},expression:"queryParams.originallyTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_team:car_team:add"],expression:"['car_team:car_team:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_team:car_team:edit"],expression:"['car_team:car_team:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_team:car_team:remove"],expression:"['car_team:car_team:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.car_teamList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"团队名称",align:"center",prop:"teamName"}}),a("el-table-column",{attrs:{label:"账号",align:"center",prop:"account"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.status?a("span",{staticClass:"dict1"},[e._v("启用")]):e._e(),2==t.row.status?a("span",{staticClass:"dict2"},[e._v("禁用")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"类型",align:"center",prop:"teamType"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.teamType?a("span",[e._v("团队")]):e._e(),2==t.row.teamType?a("span",[e._v("个人")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"联系人",align:"center",prop:"contactPerson"}}),a("el-table-column",{attrs:{label:"电话",align:"center",prop:"phone"}}),a("el-table-column",{attrs:{label:"佣金结算账号",align:"center",prop:"settlementAccount"}}),a("el-table-column",{attrs:{label:"已派单",align:"center",prop:"settlementAccount"}}),a("el-table-column",{attrs:{label:"在途订单数",align:"center",prop:"settlementAccount"}}),a("el-table-column",{attrs:{label:"已完成订单",align:"center",prop:"settlementAccount"}}),a("el-table-column",{attrs:{label:"撤销订单数",align:"center",prop:"settlementAccount"}}),a("el-table-column",{attrs:{label:"启用时间",align:"center",prop:"enabledDate",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.enabledDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_team:car_team:edit"],expression:"['car_team:car_team:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"团队名称",prop:"teamName"}},[a("el-input",{attrs:{placeholder:"请输入团队名称"},model:{value:e.form.teamName,callback:function(t){e.$set(e.form,"teamName",t)},expression:"form.teamName"}})],1),a("el-form-item",{attrs:{label:"账号",prop:"account"}},[a("el-input",{attrs:{placeholder:"请输入账号"},model:{value:e.form.account,callback:function(t){e.$set(e.form,"account",t)},expression:"form.account"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.statusList,(function(t){return a("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1),a("el-form-item",{attrs:{label:"联系人",prop:"contactPerson"}},[a("el-input",{attrs:{placeholder:"请输入联系人"},model:{value:e.form.contactPerson,callback:function(t){e.$set(e.form,"contactPerson",t)},expression:"form.contactPerson"}})],1),a("el-form-item",{attrs:{label:"电话",prop:"phone"}},[a("el-input",{attrs:{placeholder:"请输入电话"},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1),a("el-form-item",{attrs:{label:"佣金结算账号",prop:"settlementAccount"}},[a("el-input",{attrs:{placeholder:"请输入佣金结算账号"},model:{value:e.form.settlementAccount,callback:function(t){e.$set(e.form,"settlementAccount",t)},expression:"form.settlementAccount"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],n=a("5530"),s=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("a1e7")),i={name:"Car_team",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,car_teamList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:15,teamName:null,status:1,originallyTime:null,startTime:"",endTime:""},form:{},rules:{teamName:[{required:!0,message:"团队名称不能为空",trigger:"blur"}],account:[{required:!0,message:"账号不能为空",trigger:"blur"}],status:[{required:!0,message:"状态：0-禁用，1-启用不能为空",trigger:"change"}],teamType:[{required:!0,message:"类型: 0-个人，1-团队不能为空",trigger:"change"}]},statusList:[{label:"启用",value:1},{label:"禁用",value:2}],value:"停用"}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(s["d"])(this.queryParams).then((function(t){e.car_teamList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,teamName:null,account:null,status:null,teamType:null,contactPerson:null,phone:null,settlementAccount:null,enabledDate:null,createdDate:null,updatedDate:null},this.resetForm("form")},handleQuery:function(){this.queryParams.originallyTime&&(this.queryParams.startTime=this.queryParams.originallyTime[0],this.queryParams.endTime=this.queryParams.originallyTime[1]),this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.teamName=null,this.queryParams.status=null,this.queryParams.originallyTime=null,this.queryParams.startTime=null,this.queryParams.endTime=null,this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加团队管理"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;Object(s["c"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改团队管理"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?Object(s["e"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(s["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除团队管理编号为"'+a+'"的数据项？').then((function(){return Object(s["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("car_team/car_team/export",Object(n["a"])({},this.queryParams),"car_team_".concat((new Date).getTime(),".xlsx"))}}},o=i,c=(a("2a7b"),a("2877")),u=Object(c["a"])(o,r,l,!1,null,null,null);t["default"]=u.exports}}]);