{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _slicedToArray2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/slicedToArray.js\"));\nrequire(\"core-js/modules/es.error.cause.js\");\nrequire(\"core-js/modules/es.array.concat.js\");\nrequire(\"core-js/modules/es.array.join.js\");\nrequire(\"core-js/modules/es.array.map.js\");\nrequire(\"core-js/modules/es.json.stringify.js\");\nrequire(\"core-js/modules/es.number.constructor.js\");\nrequire(\"core-js/modules/es.number.to-fixed.js\");\nrequire(\"core-js/modules/es.object.keys.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.map.js\");\nvar _litigationStatus = _interopRequireDefault(require(\"@/layout/components/Dialog/litigationStatus.vue\"));\nvar _auth = require(\"@/utils/auth\");\nvar _litigation = require(\"@/api/litigation/litigation\");\nvar _installment_application_audit = require(\"@/api/installment_application_audit/installment_application_audit\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default2 = exports.default = {\n  name: 'LitigationLogForm',\n  components: {\n    litigationStatus: _litigationStatus.default\n  },\n  props: {\n    action: {\n      type: String,\n      default: '/common/ossupload'\n    },\n    data: {\n      type: Object,\n      default: function _default() {}\n    }\n  },\n  data: function data() {\n    var _this = this;\n    return {\n      title: '提交法诉日志',\n      visible: false,\n      loanReminder: {},\n      litigationLog: {},\n      installmentForm: {\n        loanId: null,\n        applyAmount: 0,\n        periodCount: 1,\n        billAmount: '0.00',\n        tailAmount: 0,\n        repayDay: 1,\n        tailPayTime: null,\n        accountType: '',\n        installmentStatus: 2 // 2-法诉分期\n      },\n      // 分期表单验证规则\n      installmentRules: {\n        applyAmount: [{\n          required: true,\n          message: '请输入申请分期金额',\n          trigger: 'blur'\n        }, {\n          type: 'number',\n          min: 0.01,\n          message: '申请分期金额必须大于0',\n          trigger: 'blur'\n        }],\n        periodCount: [{\n          required: true,\n          message: '请输入分期期数',\n          trigger: 'blur'\n        }, {\n          type: 'number',\n          min: 1,\n          max: 60,\n          message: '分期期数必须在1-60期之间',\n          trigger: 'blur'\n        }],\n        repayDay: [{\n          required: true,\n          message: '请选择每期还款日',\n          trigger: 'change'\n        }],\n        accountType: [{\n          required: true,\n          message: '请选择账号类型',\n          trigger: 'change'\n        }],\n        tailPayTime: [{\n          validator: function validator(rule, value, callback) {\n            if (_this.installmentForm.tailAmount > 0 && !value) {\n              callback(new Error('有尾款时必须选择尾款支付时间'));\n            } else {\n              callback();\n            }\n          },\n          trigger: 'change'\n        }]\n      },\n      uploadUrl: process.env.VUE_APP_BASE_API + this.action,\n      headers: {\n        Authorization: 'Bearer ' + (0, _auth.getToken)()\n      },\n      dialogImageUrl: '',\n      dialogVisible: false\n    };\n  },\n  watch: {\n    data: {\n      handler: function handler(newVal) {\n        if (newVal) {\n          console.log('newVal', newVal);\n          this.loanReminder = {\n            loanId: newVal.流程序号,\n            customerName: newVal.贷款人,\n            channel: newVal.出单渠道,\n            bank: newVal.放款银行,\n            identity: this.$store.state.user.roles[0],\n            repaymentStatus: '',\n            fundsRepayment: '',\n            fundsAmount: '',\n            fundsImage: [],\n            fundsAccountType: '',\n            accountNumber: '',\n            urgeStatus: '',\n            trackingTime: '',\n            urgeDescribe: '',\n            status: 2\n          };\n          this.litigationLog = {\n            loanId: newVal.流程序号,\n            litigationId: newVal.序号,\n            docName: '',\n            docNumber: '',\n            docUploadUrl: [],\n            docEffectiveDate: '',\n            openDate: '',\n            status: ''\n          };\n          // 重置分期表单\n          this.installmentForm = {\n            loanId: newVal && newVal.流程序号 ? newVal.流程序号 : null,\n            applyAmount: 0,\n            periodCount: 1,\n            billAmount: '0.00',\n            tailAmount: 0,\n            repayDay: 1,\n            tailPayTime: null,\n            accountType: '',\n            installmentStatus: 2 // 2-法诉分期\n          };\n        }\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  methods: {\n    // 处理还款类型变化\n    handleRepaymentTypeChange: function handleRepaymentTypeChange(value) {\n      var _this2 = this;\n      if (value === '3') {\n        // 选择分期还款时，清空其他还款信息\n        this.loanReminder.fundsRepayment = '';\n        this.loanReminder.fundsAmount = '';\n        this.loanReminder.fundsAccountType = '';\n        this.loanReminder.accountNumber = '';\n        this.loanReminder.fundsImage = [];\n      } else {\n        // 选择其他还款类型时，清空分期表单验证状态\n        this.$nextTick(function () {\n          if (_this2.$refs.installmentFormRef) {\n            _this2.$refs.installmentFormRef.clearValidate();\n          }\n        });\n      }\n    },\n    // 分期表单计算方法\n    handleInstallmentFormChange: function handleInstallmentFormChange() {\n      var applyAmount = Number(this.installmentForm.applyAmount) || 0;\n      var periodCount = Number(this.installmentForm.periodCount) || 1;\n      var tailAmount = Number(this.installmentForm.tailAmount) || 0;\n      if (applyAmount >= 0 && periodCount >= 1) {\n        this.installmentForm.billAmount = ((applyAmount - tailAmount) / periodCount).toFixed(2);\n      } else {\n        this.installmentForm.billAmount = '0.00';\n      }\n    },\n    // 通用的上传成功处理函数\n    handleUploadSuccess: function handleUploadSuccess(res, file, fileList, formField) {\n      var _formField$split = formField.split('.'),\n        _formField$split2 = (0, _slicedToArray2.default)(_formField$split, 2),\n        obj = _formField$split2[0],\n        prop = _formField$split2[1];\n      this[obj][prop] = fileList;\n    },\n    // 通用的删除处理函数\n    handleRemove: function handleRemove(file, fileList, formField) {\n      var _formField$split3 = formField.split('.'),\n        _formField$split4 = (0, _slicedToArray2.default)(_formField$split3, 2),\n        obj = _formField$split4[0],\n        prop = _formField$split4[1];\n      this[obj][prop] = fileList;\n    },\n    // 上传失败\n    handleUploadError: function handleUploadError() {\n      this.$modal.msgError('上传失败，请重试');\n      this.$modal.closeLoading();\n    },\n    // 图片预览\n    handlePictureCardPreview: function handlePictureCardPreview(file) {\n      this.dialogImageUrl = file.url;\n      this.dialogVisible = true;\n    },\n    /** 提交表单 */submitForm: function submitForm() {\n      var _this3 = this;\n      var loanReminderCopy = JSON.parse(JSON.stringify(this.loanReminder));\n      var litigationLogCopy = JSON.parse(JSON.stringify(this.litigationLog));\n      loanReminderCopy.fundsImage = loanReminderCopy.fundsImage.map(function (item) {\n        return item.response;\n      }).join(',');\n      litigationLogCopy.docUploadUrl = litigationLogCopy.docUploadUrl.map(function (item) {\n        return item.response;\n      }).join(',');\n      loanReminderCopy.fundsAccountType = loanReminderCopy.fundsAccountType === '其他' ? loanReminderCopy.accountNumber : loanReminderCopy.fundsAccountType;\n      // 将日志描述从 loanReminder 复制到 litigationLog\n      litigationLogCopy.urgeDescribe = loanReminderCopy.urgeDescribe;\n\n      // 如果选择了分期还款，先提交分期申请\n      if (loanReminderCopy.repaymentStatus === '3') {\n        // 显示确认对话框\n        var confirmMessage = \"\\u786E\\u8BA4\\u63D0\\u4EA4\\u5206\\u671F\\u7533\\u8BF7\\uFF1F\\n\\u7533\\u8BF7\\u91D1\\u989D\\uFF1A\".concat(this.installmentForm.applyAmount, \"\\u5143\\n\\u5206\\u671F\\u671F\\u6570\\uFF1A\").concat(this.installmentForm.periodCount, \"\\u671F\\n\\u6BCF\\u671F\\u91D1\\u989D\\uFF1A\").concat(this.installmentForm.billAmount, \"\\u5143\");\n        this.$confirm(confirmMessage, '确认分期申请', {\n          confirmButtonText: '确定提交',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(function () {\n          _this3.submitInstallmentApplication().then(function () {\n            // 分期申请提交成功后，再提交日志\n            _this3.submitLitigationLogData(loanReminderCopy, litigationLogCopy);\n          }).catch(function () {\n            _this3.$modal.msgError('分期申请提交失败');\n          });\n        }).catch(function () {\n          // 用户取消了操作\n        });\n      } else {\n        // 直接提交日志\n        this.submitLitigationLogData(loanReminderCopy, litigationLogCopy);\n      }\n    },\n    /** 提交分期申请 */submitInstallmentApplication: function submitInstallmentApplication() {\n      var _this4 = this;\n      return new Promise(function (resolve, reject) {\n        // 使用Element UI表单验证\n        _this4.$refs.installmentFormRef.validate(function (valid) {\n          if (!valid) {\n            _this4.$message.error('请完善分期申请信息');\n            reject();\n            return;\n          }\n\n          // 额外的业务验证\n          if (!_this4.installmentForm.loanId) {\n            _this4.$message.error('贷款ID不能为空，请重新打开表单');\n            reject();\n            return;\n          }\n\n          // 验证每期账单金额是否合理\n          var billAmount = Number(_this4.installmentForm.billAmount);\n          if (billAmount <= 0) {\n            _this4.$message.error('每期账单金额必须大于0，请检查申请金额和期数');\n            reject();\n            return;\n          }\n\n          // 验证尾款逻辑\n          if (_this4.installmentForm.tailAmount > 0 && !_this4.installmentForm.tailPayTime) {\n            _this4.$message.error('设置了尾款金额时，必须选择尾款支付时间');\n            reject();\n            return;\n          }\n          console.log('提交分期申请数据：', _this4.installmentForm);\n\n          // 调用分期申请API（与代偿分期使用相同的API）\n          (0, _installment_application_audit.addInstallment_application_audit)(_this4.installmentForm).then(function (response) {\n            if (response.code === 200) {\n              _this4.$modal.msgSuccess('分期申请提交成功');\n              resolve();\n            } else {\n              _this4.$modal.msgError('分期申请提交失败：' + (response.msg || '未知错误'));\n              reject();\n            }\n          }).catch(function (error) {\n            console.error('分期申请提交失败:', error);\n            _this4.$modal.msgError('分期申请提交失败，请稍后重试');\n            reject();\n          });\n        });\n      });\n    },\n    /** 提交法诉日志数据 */submitLitigationLogData: function submitLitigationLogData(loanReminderCopy, litigationLogCopy) {\n      var _this5 = this;\n      console.log('提交表单数据：', loanReminderCopy);\n      console.log('提交表单数据：', litigationLogCopy);\n      (0, _litigation.submitLitigationLog)({\n        loanReminder: loanReminderCopy,\n        litigationLog: litigationLogCopy\n      }).then(function (res) {\n        _this5.$modal.msgSuccess('提交成功');\n        _this5.visible = false;\n        _this5.resetForm();\n      });\n    },\n    /** 取消操作 */cancel: function cancel() {\n      this.visible = false;\n      this.resetForm();\n      return;\n    },\n    /** 重置表单 */resetForm: function resetForm() {\n      var _this6 = this;\n      this.loanReminder = {\n        fundsImage: []\n      };\n      this.litigationLog = {\n        docUploadUrl: []\n      };\n      this.installmentForm = {\n        loanId: null,\n        applyAmount: 0,\n        periodCount: 1,\n        billAmount: '0.00',\n        tailAmount: 0,\n        repayDay: 1,\n        tailPayTime: null,\n        accountType: '',\n        installmentStatus: 2 // 2-法诉分期\n      };\n      // 重置分期表单验证状态\n      this.$nextTick(function () {\n        if (_this6.$refs.installmentFormRef) {\n          _this6.$refs.installmentFormRef.clearValidate();\n        }\n      });\n    },\n    /** 统一打开弹窗的方法 */openDialog: function openDialog() {\n      this.visible = true;\n      return;\n    },\n    /** 处理文件超出限制 */handleExceed: function handleExceed(files, fileList) {\n      this.$message.warning('只能上传一个文件');\n      return;\n    }\n  }\n};", "map": {"version": 3, "names": ["_litigationStatus", "_interopRequireDefault", "require", "_auth", "_litigation", "_installment_application_audit", "name", "components", "litigationStatus", "props", "action", "type", "String", "default", "data", "Object", "_this", "title", "visible", "loanReminder", "litigationLog", "installmentForm", "loanId", "applyAmount", "periodCount", "billAmount", "tailAmount", "repayDay", "tailPayTime", "accountType", "installmentStatus", "installmentRules", "required", "message", "trigger", "min", "max", "validator", "rule", "value", "callback", "Error", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "headers", "Authorization", "getToken", "dialogImageUrl", "dialogVisible", "watch", "handler", "newVal", "console", "log", "流程序号", "customerName", "贷款人", "channel", "出单渠道", "bank", "放款银行", "identity", "$store", "state", "user", "roles", "repaymentStatus", "fundsRepayment", "fundsAmount", "fundsImage", "fundsAccountType", "accountNumber", "urgeStatus", "trackingTime", "urgeDescribe", "status", "litigationId", "序号", "doc<PERSON>ame", "docNumber", "docUploadUrl", "docEffectiveDate", "openDate", "immediate", "deep", "methods", "handleRepaymentTypeChange", "_this2", "$nextTick", "$refs", "installmentFormRef", "clearValidate", "handleInstallmentFormChange", "Number", "toFixed", "handleUploadSuccess", "res", "file", "fileList", "formField", "_formField$split", "split", "_formField$split2", "_slicedToArray2", "obj", "prop", "handleRemove", "_formField$split3", "_formField$split4", "handleUploadError", "$modal", "msgError", "closeLoading", "handlePictureCardPreview", "url", "submitForm", "_this3", "loanReminderCopy", "JSON", "parse", "stringify", "litigationLogCopy", "map", "item", "response", "join", "confirmMessage", "concat", "$confirm", "confirmButtonText", "cancelButtonText", "then", "submitInstallmentApplication", "submitLitigationLogData", "catch", "_this4", "Promise", "resolve", "reject", "validate", "valid", "$message", "error", "addInstallment_application_audit", "code", "msgSuccess", "msg", "_this5", "submitLitigationLog", "resetForm", "cancel", "_this6", "openDialog", "handleExceed", "files", "warning"], "sources": ["src/views/litigation/litigation/modules/litigationLogForm.vue"], "sourcesContent": ["<template>\r\n  <el-dialog :title=\"title\" :visible.sync=\"visible\" width=\"800px\" append-to-body @close=\"resetForm\">\r\n    <el-form ref=\"form\" :model=\"loanReminder\" label-width=\"120px\">\r\n      <!-- 第一部分：文书相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-document\"></i>\r\n        贷款信息\r\n      </el-divider>\r\n      <!-- 非填入字段 -->\r\n      <el-descriptions title=\"\" :column=\"3\" border>\r\n        <el-descriptions-item label=\"贷款人\">\r\n          {{ loanReminder.customerName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"出单渠道\">\r\n          {{ loanReminder.channel }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"放款银行\">\r\n          {{ loanReminder.bank }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <!-- 第一部分：文书相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-document\"></i>\r\n        文书信息\r\n      </el-divider>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"变更法诉状态\">\r\n            <litigation-status v-model=\"litigationLog.status\" placeholder=\"请选择法诉状态\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"文书名称\">\r\n            <el-select v-model=\"litigationLog.docName\" placeholder=\"请选择文书名称\" style=\"width: 100%\">\r\n              <el-option label=\"诉前调号\" value=\"诉前调号\" />\r\n              <el-option label=\"民初号\" value=\"民初号\" />\r\n              <el-option label=\"执行号\" value=\"执行号\" />\r\n              <el-option label=\"执保号\" value=\"执保号\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"文书号\">\r\n            <el-input v-model=\"litigationLog.docNumber\" placeholder=\"请输入文书号\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"文书生效\">\r\n            <el-date-picker v-model=\"litigationLog.docEffectiveDate\" type=\"date\" placeholder=\"选择日期\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\" v-if=\"litigationLog.status === '待出法院文书'\">\r\n          <el-form-item label=\"登记开庭时间\">\r\n            <el-date-picker v-model=\"litigationLog.openDate\" type=\"datetime\" placeholder=\"选择开庭时间\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"上传文书\">\r\n            <el-upload\r\n              :data=\"data\"\r\n              :action=\"uploadUrl\"\r\n              :headers=\"headers\"\r\n              :limit=\"1\"\r\n              :file-list=\"litigationLog.docUploadUrl\"\r\n              :on-success=\"(res, file, fileList) => handleUploadSuccess(res, file, fileList, 'litigationLog.docUploadUrl')\"\r\n              :on-remove=\"(file, fileList) => handleRemove(file, fileList, 'litigationLog.docUploadUrl')\"\r\n              :on-error=\"handleUploadError\">\r\n              <el-button size=\"small\" type=\"primary\" :disabled=\"litigationLog.docUploadUrl.length >= 1\">点击上传</el-button>\r\n            </el-upload>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第二部分：还款相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-money\"></i>\r\n        还款信息\r\n      </el-divider>\r\n\r\n      <!-- 还款类型选择 -->\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"还款类型\">\r\n            <el-select v-model=\"loanReminder.repaymentStatus\" placeholder=\"请选择还款类型\" style=\"width: 100%\" @change=\"handleRepaymentTypeChange\">\r\n              <el-option label=\"部分还款\" value=\"2\" />\r\n              <el-option label=\"分期还款\" value=\"3\" />\r\n              <el-option label=\"协商买车\" value=\"4\" />\r\n              <el-option label=\"法诉结清\" value=\"5\" />\r\n              <el-option label=\"法诉减免结清\" value=\"6\" />\r\n              <el-option label=\"拍卖回款\" value=\"7\" />\r\n              <el-option label=\"法院划扣\" value=\"8\" />\r\n              <el-option label=\"其他分配回款\" value=\"9\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 分期还款表单 -->\r\n      <div v-if=\"loanReminder.repaymentStatus === '3'\">\r\n        <el-form ref=\"installmentFormRef\" :model=\"installmentForm\" :rules=\"installmentRules\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"申请分期金额\" prop=\"applyAmount\" required>\r\n                <el-input-number\r\n                  v-model=\"installmentForm.applyAmount\"\r\n                  :min=\"0.01\"\r\n                  :max=\"999999999\"\r\n                  :precision=\"2\"\r\n                  :step=\"100\"\r\n                  :controls-position=\"'right'\"\r\n                  placeholder=\"请输入申请分期金额\"\r\n                  @input=\"handleInstallmentFormChange\"\r\n                  style=\"width: 100%\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"分期期数\" prop=\"periodCount\" required>\r\n                <el-input-number\r\n                  v-model=\"installmentForm.periodCount\"\r\n                  :min=\"1\"\r\n                  :max=\"60\"\r\n                  :precision=\"0\"\r\n                  :step=\"1\"\r\n                  :controls-position=\"'right'\"\r\n                  placeholder=\"请输入分期期数\"\r\n                  @input=\"handleInstallmentFormChange\"\r\n                  style=\"width: 100%\" />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"每期账单金额\">\r\n                <el-input v-model=\"installmentForm.billAmount\" placeholder=\"自动计算\" disabled />\r\n                <div class=\"form-tip\">\r\n                  根据申请金额和期数自动计算\r\n                  <span v-if=\"installmentForm.applyAmount > 0 && installmentForm.periodCount > 0\">\r\n                    <br>计算公式：({{ installmentForm.applyAmount }} - {{ installmentForm.tailAmount || 0 }}) ÷ {{ installmentForm.periodCount }} = {{ installmentForm.billAmount }}元/期\r\n                  </span>\r\n                </div>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"每期还款日\" prop=\"repayDay\" required>\r\n                <el-select v-model=\"installmentForm.repayDay\" placeholder=\"请选择每期还款日\" style=\"width: 100%\">\r\n                  <el-option v-for=\"day in 28\" :key=\"day\" :label=\"`每月${day}号`\" :value=\"day\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"尾款金额\">\r\n                <el-input-number\r\n                  v-model=\"installmentForm.tailAmount\"\r\n                  :min=\"0\"\r\n                  :precision=\"2\"\r\n                  :step=\"100\"\r\n                  :controls-position=\"'right'\"\r\n                  placeholder=\"请输入尾款金额（可选）\"\r\n                  @input=\"handleInstallmentFormChange\"\r\n                  style=\"width: 100%\" />\r\n                <div class=\"form-tip\">可选，如有尾款请填写</div>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"尾款支付时间\" :prop=\"installmentForm.tailAmount > 0 ? 'tailPayTime' : ''\">\r\n                <el-date-picker\r\n                  v-model=\"installmentForm.tailPayTime\"\r\n                  type=\"date\"\r\n                  placeholder=\"选择尾款支付时间\"\r\n                  :disabled=\"!installmentForm.tailAmount || installmentForm.tailAmount <= 0\"\r\n                  style=\"width: 100%\" />\r\n                <div class=\"form-tip\">有尾款时必填</div>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"账号类型\" prop=\"accountType\" required>\r\n                <el-select v-model=\"installmentForm.accountType\" placeholder=\"请选择账号类型\" style=\"width: 100%\">\r\n                  <el-option label=\"银行账户\" value=\"银行账户\" />\r\n                  <el-option label=\"微信\" value=\"微信\" />\r\n                  <el-option label=\"支付宝\" value=\"支付宝\" />\r\n                  <el-option label=\"其他\" value=\"其他\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 其他还款类型的表单 -->\r\n      <div v-else-if=\"loanReminder.repaymentStatus && loanReminder.repaymentStatus !== '3'\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"款项明细类型\">\r\n              <el-select v-model=\"loanReminder.fundsRepayment\" placeholder=\"请选择款项明细类型\" style=\"width: 100%\">\r\n                <el-option label=\"律师费\" value=\"律师费\" />\r\n                <el-option label=\"法诉费\" value=\"法诉费\" />\r\n                <el-option label=\"保全费\" value=\"保全费\" />\r\n                <el-option label=\"布控费\" value=\"布控费\" />\r\n                <el-option label=\"公告费\" value=\"公告费\" />\r\n                <el-option label=\"评估费\" value=\"评估费\" />\r\n                <el-option label=\"执行费\" value=\"执行费\" />\r\n                <el-option label=\"违约金\" value=\"违约金\" />\r\n                <el-option label=\"担保费\" value=\"担保费\" />\r\n                <el-option label=\"居间费\" value=\"居间费\" />\r\n                <el-option label=\"代偿金\" value=\"代偿金\" />\r\n                <el-option label=\"判决金额\" value=\"判决金额\" />\r\n                <el-option label=\"利息\" value=\"利息\" />\r\n                <el-option label=\"其他欠款\" value=\"其他欠款\" />\r\n                <el-option label=\"保险费\" value=\"保险费\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"金额\">\r\n              <el-input-number v-model=\"loanReminder.fundsAmount\" :min=\"0\" :precision=\"2\" style=\"width: 100%\" placeholder=\"请输入金额\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\">\r\n              <el-select v-model=\"loanReminder.fundsAccountType\" placeholder=\"请选择账号类型\" style=\"width: 100%\">\r\n                <el-option label=\"银行账户\" value=\"银行账户\" />\r\n                <el-option label=\"微信\" value=\"微信\" />\r\n                <el-option label=\"支付宝\" value=\"支付宝\" />\r\n                <el-option label=\"其他\" value=\"其他\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号\">\r\n              <el-input v-model=\"loanReminder.accountNumber\" :disabled=\"loanReminder.fundsAccountType !== '其他'\" placeholder=\"请输入账号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"还款凭据\">\r\n              <el-upload\r\n                :data=\"data\"\r\n                :action=\"uploadUrl\"\r\n                :headers=\"headers\"\r\n                list-type=\"picture-card\"\r\n                :file-list=\"loanReminder.fundsImage\"\r\n                :on-preview=\"handlePictureCardPreview\"\r\n                :on-success=\"(res, file, fileList) => handleUploadSuccess(res, file, fileList, 'loanReminder.fundsImage')\"\r\n                :on-remove=\"(file, fileList) => handleRemove(file, fileList, 'loanReminder.fundsImage')\"\r\n                :on-error=\"handleUploadError\">\r\n                <i class=\"el-icon-plus\"></i>\r\n              </el-upload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n\r\n\r\n      <!-- 第三部分：日志相关 -->\r\n      <el-divider content-position=\"left\">\r\n        <i class=\"el-icon-notebook-2\"></i>\r\n        日志信息\r\n      </el-divider>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"日志类型\">\r\n            <el-select v-model=\"loanReminder.urgeStatus\" placeholder=\"请选择日志类型\" style=\"width: 100%\">\r\n              <el-option label=\"继续跟踪\" value=\"1\" />\r\n              <el-option label=\"约定还款\" value=\"2\" />\r\n              <el-option label=\"无法跟进\" value=\"3\" />\r\n              <el-option label=\"暂时无需跟进\" value=\"4\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"下次跟进时间\">\r\n            <el-date-picker v-model=\"loanReminder.trackingTime\" type=\"datetime\" placeholder=\"选择跟进时间\" style=\"width: 100%\" />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"日志描述\">\r\n            <el-input v-model=\"loanReminder.urgeDescribe\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入日志描述\" maxlength=\"500\" show-word-limit />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"cancel\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n    </div>\r\n    <!-- 图片预览 -->\r\n    <el-dialog :visible.sync=\"dialogVisible\">\r\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt=\"\" />\r\n    </el-dialog>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport litigationStatus from '@/layout/components/Dialog/litigationStatus.vue'\r\nimport { getToken } from '@/utils/auth'\r\nimport { submitLitigationLog } from '@/api/litigation/litigation'\r\nimport { addInstallment_application_audit } from '@/api/installment_application_audit/installment_application_audit'\r\n\r\nexport default {\r\n  name: 'LitigationLogForm',\r\n  components: {\r\n    litigationStatus,\r\n  },\r\n  props: {\r\n    action: {\r\n      type: String,\r\n      default: '/common/ossupload',\r\n    },\r\n    data: {\r\n      type: Object,\r\n      default: () => {},\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      title: '提交法诉日志',\r\n      visible: false,\r\n      loanReminder: {},\r\n      litigationLog: {},\r\n      installmentForm: {\r\n        loanId: null,\r\n        applyAmount: 0,\r\n        periodCount: 1,\r\n        billAmount: '0.00',\r\n        tailAmount: 0,\r\n        repayDay: 1,\r\n        tailPayTime: null,\r\n        accountType: '',\r\n        installmentStatus: 2 // 2-法诉分期\r\n      },\r\n      // 分期表单验证规则\r\n      installmentRules: {\r\n        applyAmount: [\r\n          { required: true, message: '请输入申请分期金额', trigger: 'blur' },\r\n          { type: 'number', min: 0.01, message: '申请分期金额必须大于0', trigger: 'blur' }\r\n        ],\r\n        periodCount: [\r\n          { required: true, message: '请输入分期期数', trigger: 'blur' },\r\n          { type: 'number', min: 1, max: 60, message: '分期期数必须在1-60期之间', trigger: 'blur' }\r\n        ],\r\n        repayDay: [\r\n          { required: true, message: '请选择每期还款日', trigger: 'change' }\r\n        ],\r\n        accountType: [\r\n          { required: true, message: '请选择账号类型', trigger: 'change' }\r\n        ],\r\n        tailPayTime: [\r\n          {\r\n            validator: (rule, value, callback) => {\r\n              if (this.installmentForm.tailAmount > 0 && !value) {\r\n                callback(new Error('有尾款时必须选择尾款支付时间'))\r\n              } else {\r\n                callback()\r\n              }\r\n            },\r\n            trigger: 'change'\r\n          }\r\n        ]\r\n      },\r\n      uploadUrl: process.env.VUE_APP_BASE_API + this.action,\r\n      headers: {\r\n        Authorization: 'Bearer ' + getToken(),\r\n      },\r\n      dialogImageUrl: '',\r\n      dialogVisible: false,\r\n    }\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          console.log('newVal', newVal)\r\n          this.loanReminder = {\r\n            loanId: newVal.流程序号,\r\n            customerName: newVal.贷款人,\r\n            channel: newVal.出单渠道,\r\n            bank: newVal.放款银行,\r\n            identity: this.$store.state.user.roles[0],\r\n            repaymentStatus: '',\r\n            fundsRepayment: '',\r\n            fundsAmount: '',\r\n            fundsImage: [],\r\n            fundsAccountType: '',\r\n            accountNumber: '',\r\n            urgeStatus: '',\r\n            trackingTime: '',\r\n            urgeDescribe: '',\r\n            status: 2,\r\n          }\r\n          this.litigationLog = {\r\n            loanId: newVal.流程序号,\r\n            litigationId: newVal.序号,\r\n            docName: '',\r\n            docNumber: '',\r\n            docUploadUrl: [],\r\n            docEffectiveDate: '',\r\n            openDate: '',\r\n            status: '',\r\n          }\r\n          // 重置分期表单\r\n          this.installmentForm = {\r\n            loanId: newVal && newVal.流程序号 ? newVal.流程序号 : null,\r\n            applyAmount: 0,\r\n            periodCount: 1,\r\n            billAmount: '0.00',\r\n            tailAmount: 0,\r\n            repayDay: 1,\r\n            tailPayTime: null,\r\n            accountType: '',\r\n            installmentStatus: 2 // 2-法诉分期\r\n          }\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true,\r\n    },\r\n  },\r\n  methods: {\r\n    // 处理还款类型变化\r\n    handleRepaymentTypeChange(value) {\r\n      if (value === '3') {\r\n        // 选择分期还款时，清空其他还款信息\r\n        this.loanReminder.fundsRepayment = ''\r\n        this.loanReminder.fundsAmount = ''\r\n        this.loanReminder.fundsAccountType = ''\r\n        this.loanReminder.accountNumber = ''\r\n        this.loanReminder.fundsImage = []\r\n      } else {\r\n        // 选择其他还款类型时，清空分期表单验证状态\r\n        this.$nextTick(() => {\r\n          if (this.$refs.installmentFormRef) {\r\n            this.$refs.installmentFormRef.clearValidate()\r\n          }\r\n        })\r\n      }\r\n    },\r\n\r\n    // 分期表单计算方法\r\n    handleInstallmentFormChange() {\r\n      const applyAmount = Number(this.installmentForm.applyAmount) || 0\r\n      const periodCount = Number(this.installmentForm.periodCount) || 1\r\n      const tailAmount = Number(this.installmentForm.tailAmount) || 0\r\n      if (applyAmount >= 0 && periodCount >= 1) {\r\n        this.installmentForm.billAmount = ((applyAmount - tailAmount) / periodCount).toFixed(2)\r\n      } else {\r\n        this.installmentForm.billAmount = '0.00'\r\n      }\r\n    },\r\n    // 通用的上传成功处理函数\r\n    handleUploadSuccess(res, file, fileList, formField) {\r\n      const [obj, prop] = formField.split('.')\r\n      this[obj][prop] = fileList\r\n    },\r\n    // 通用的删除处理函数\r\n    handleRemove(file, fileList, formField) {\r\n      const [obj, prop] = formField.split('.')\r\n      this[obj][prop] = fileList\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError('上传失败，请重试')\r\n      this.$modal.closeLoading()\r\n    },\r\n    // 图片预览\r\n    handlePictureCardPreview(file) {\r\n      this.dialogImageUrl = file.url\r\n      this.dialogVisible = true\r\n    },\r\n    /** 提交表单 */\r\n    submitForm() {\r\n      const loanReminderCopy = JSON.parse(JSON.stringify(this.loanReminder))\r\n      const litigationLogCopy = JSON.parse(JSON.stringify(this.litigationLog))\r\n      loanReminderCopy.fundsImage = loanReminderCopy.fundsImage.map(item => item.response).join(',')\r\n      litigationLogCopy.docUploadUrl = litigationLogCopy.docUploadUrl.map(item => item.response).join(',')\r\n      loanReminderCopy.fundsAccountType =\r\n        loanReminderCopy.fundsAccountType === '其他' ? loanReminderCopy.accountNumber : loanReminderCopy.fundsAccountType\r\n      // 将日志描述从 loanReminder 复制到 litigationLog\r\n      litigationLogCopy.urgeDescribe = loanReminderCopy.urgeDescribe\r\n\r\n      // 如果选择了分期还款，先提交分期申请\r\n      if (loanReminderCopy.repaymentStatus === '3') {\r\n        // 显示确认对话框\r\n        const confirmMessage = `确认提交分期申请？\\n申请金额：${this.installmentForm.applyAmount}元\\n分期期数：${this.installmentForm.periodCount}期\\n每期金额：${this.installmentForm.billAmount}元`\r\n        this.$confirm(confirmMessage, '确认分期申请', {\r\n          confirmButtonText: '确定提交',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.submitInstallmentApplication().then(() => {\r\n            // 分期申请提交成功后，再提交日志\r\n            this.submitLitigationLogData(loanReminderCopy, litigationLogCopy)\r\n          }).catch(() => {\r\n            this.$modal.msgError('分期申请提交失败')\r\n          })\r\n        }).catch(() => {\r\n          // 用户取消了操作\r\n        })\r\n      } else {\r\n        // 直接提交日志\r\n        this.submitLitigationLogData(loanReminderCopy, litigationLogCopy)\r\n      }\r\n    },\r\n\r\n    /** 提交分期申请 */\r\n    submitInstallmentApplication() {\r\n      return new Promise((resolve, reject) => {\r\n        // 使用Element UI表单验证\r\n        this.$refs.installmentFormRef.validate((valid) => {\r\n          if (!valid) {\r\n            this.$message.error('请完善分期申请信息')\r\n            reject()\r\n            return\r\n          }\r\n\r\n          // 额外的业务验证\r\n          if (!this.installmentForm.loanId) {\r\n            this.$message.error('贷款ID不能为空，请重新打开表单')\r\n            reject()\r\n            return\r\n          }\r\n\r\n          // 验证每期账单金额是否合理\r\n          const billAmount = Number(this.installmentForm.billAmount)\r\n          if (billAmount <= 0) {\r\n            this.$message.error('每期账单金额必须大于0，请检查申请金额和期数')\r\n            reject()\r\n            return\r\n          }\r\n\r\n          // 验证尾款逻辑\r\n          if (this.installmentForm.tailAmount > 0 && !this.installmentForm.tailPayTime) {\r\n            this.$message.error('设置了尾款金额时，必须选择尾款支付时间')\r\n            reject()\r\n            return\r\n          }\r\n\r\n          console.log('提交分期申请数据：', this.installmentForm)\r\n\r\n          // 调用分期申请API（与代偿分期使用相同的API）\r\n          addInstallment_application_audit(this.installmentForm).then(response => {\r\n            if (response.code === 200) {\r\n              this.$modal.msgSuccess('分期申请提交成功')\r\n              resolve()\r\n            } else {\r\n              this.$modal.msgError('分期申请提交失败：' + (response.msg || '未知错误'))\r\n              reject()\r\n            }\r\n          }).catch(error => {\r\n            console.error('分期申请提交失败:', error)\r\n            this.$modal.msgError('分期申请提交失败，请稍后重试')\r\n            reject()\r\n          })\r\n        })\r\n      })\r\n    },\r\n\r\n    /** 提交法诉日志数据 */\r\n    submitLitigationLogData(loanReminderCopy, litigationLogCopy) {\r\n      console.log('提交表单数据：', loanReminderCopy)\r\n      console.log('提交表单数据：', litigationLogCopy)\r\n      submitLitigationLog({ loanReminder: loanReminderCopy, litigationLog: litigationLogCopy }).then(res => {\r\n        this.$modal.msgSuccess('提交成功')\r\n        this.visible = false\r\n        this.resetForm()\r\n      })\r\n    },\r\n    /** 取消操作 */\r\n    cancel() {\r\n      this.visible = false\r\n      this.resetForm()\r\n      return\r\n    },\r\n    /** 重置表单 */\r\n    resetForm() {\r\n      this.loanReminder = {\r\n        fundsImage: [],\r\n      }\r\n      this.litigationLog = {\r\n        docUploadUrl: [],\r\n      }\r\n      this.installmentForm = {\r\n        loanId: null,\r\n        applyAmount: 0,\r\n        periodCount: 1,\r\n        billAmount: '0.00',\r\n        tailAmount: 0,\r\n        repayDay: 1,\r\n        tailPayTime: null,\r\n        accountType: '',\r\n        installmentStatus: 2 // 2-法诉分期\r\n      }\r\n      // 重置分期表单验证状态\r\n      this.$nextTick(() => {\r\n        if (this.$refs.installmentFormRef) {\r\n          this.$refs.installmentFormRef.clearValidate()\r\n        }\r\n      })\r\n    },\r\n    /** 统一打开弹窗的方法 */\r\n    openDialog() {\r\n      this.visible = true\r\n      return\r\n    },\r\n    /** 处理文件超出限制 */\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning('只能上传一个文件')\r\n      return\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.el-divider__text {\r\n  padding: 0 15px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.upload-demo {\r\n  width: 100%;\r\n}\r\n\r\n.el-upload {\r\n  width: 100%;\r\n}\r\n\r\n/* 分期表单样式 */\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 4px;\r\n  line-height: 1.2;\r\n}\r\n\r\n/* 必填项标识 */\r\n.el-form-item.is-required .el-form-item__label::before {\r\n  content: '*';\r\n  color: #f56c6c;\r\n  margin-right: 4px;\r\n}\r\n\r\n/* 分期表单区域样式 */\r\n.el-form .el-row {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n/* 禁用状态的输入框样式 */\r\n.el-input.is-disabled .el-input__inner {\r\n  background-color: #f5f7fa;\r\n  border-color: #e4e7ed;\r\n  color: #c0c4cc;\r\n}\r\n\r\n/* 分期表单边框 */\r\n.el-form {\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  padding: 20px;\r\n  background-color: #fafafa;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AA4TA,IAAAA,iBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;AACA,IAAAG,8BAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAI,IAAA;EACAC,UAAA;IACAC,gBAAA,EAAAA;EACA;EACAC,KAAA;IACAC,MAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,IAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,WAAAA,SAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAE,KAAA;IACA;MACAC,KAAA;MACAC,OAAA;MACAC,YAAA;MACAC,aAAA;MACAC,eAAA;QACAC,MAAA;QACAC,WAAA;QACAC,WAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,WAAA;QACAC,WAAA;QACAC,iBAAA;MACA;MACA;MACAC,gBAAA;QACAR,WAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAvB,IAAA;UAAAwB,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,WAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAvB,IAAA;UAAAwB,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,WAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,WAAA,GACA;UACAS,SAAA,WAAAA,UAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACA,IAAAxB,KAAA,CAAAK,eAAA,CAAAK,UAAA,SAAAa,KAAA;cACAC,QAAA,KAAAC,KAAA;YACA;cACAD,QAAA;YACA;UACA;UACAN,OAAA;QACA;MAEA;MACAQ,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA,QAAAnC,MAAA;MACAoC,OAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;MACAC,cAAA;MACAC,aAAA;IACA;EACA;EACAC,KAAA;IACArC,IAAA;MACAsC,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA;UACAC,OAAA,CAAAC,GAAA,WAAAF,MAAA;UACA,KAAAlC,YAAA;YACAG,MAAA,EAAA+B,MAAA,CAAAG,IAAA;YACAC,YAAA,EAAAJ,MAAA,CAAAK,GAAA;YACAC,OAAA,EAAAN,MAAA,CAAAO,IAAA;YACAC,IAAA,EAAAR,MAAA,CAAAS,IAAA;YACAC,QAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,KAAA;YACAC,eAAA;YACAC,cAAA;YACAC,WAAA;YACAC,UAAA;YACAC,gBAAA;YACAC,aAAA;YACAC,UAAA;YACAC,YAAA;YACAC,YAAA;YACAC,MAAA;UACA;UACA,KAAAzD,aAAA;YACAE,MAAA,EAAA+B,MAAA,CAAAG,IAAA;YACAsB,YAAA,EAAAzB,MAAA,CAAA0B,EAAA;YACAC,OAAA;YACAC,SAAA;YACAC,YAAA;YACAC,gBAAA;YACAC,QAAA;YACAP,MAAA;UACA;UACA;UACA,KAAAxD,eAAA;YACAC,MAAA,EAAA+B,MAAA,IAAAA,MAAA,CAAAG,IAAA,GAAAH,MAAA,CAAAG,IAAA;YACAjC,WAAA;YACAC,WAAA;YACAC,UAAA;YACAC,UAAA;YACAC,QAAA;YACAC,WAAA;YACAC,WAAA;YACAC,iBAAA;UACA;QACA;MACA;MACAuD,SAAA;MACAC,IAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,yBAAA,WAAAA,0BAAAjD,KAAA;MAAA,IAAAkD,MAAA;MACA,IAAAlD,KAAA;QACA;QACA,KAAApB,YAAA,CAAAkD,cAAA;QACA,KAAAlD,YAAA,CAAAmD,WAAA;QACA,KAAAnD,YAAA,CAAAqD,gBAAA;QACA,KAAArD,YAAA,CAAAsD,aAAA;QACA,KAAAtD,YAAA,CAAAoD,UAAA;MACA;QACA;QACA,KAAAmB,SAAA;UACA,IAAAD,MAAA,CAAAE,KAAA,CAAAC,kBAAA;YACAH,MAAA,CAAAE,KAAA,CAAAC,kBAAA,CAAAC,aAAA;UACA;QACA;MACA;IACA;IAEA;IACAC,2BAAA,WAAAA,4BAAA;MACA,IAAAvE,WAAA,GAAAwE,MAAA,MAAA1E,eAAA,CAAAE,WAAA;MACA,IAAAC,WAAA,GAAAuE,MAAA,MAAA1E,eAAA,CAAAG,WAAA;MACA,IAAAE,UAAA,GAAAqE,MAAA,MAAA1E,eAAA,CAAAK,UAAA;MACA,IAAAH,WAAA,SAAAC,WAAA;QACA,KAAAH,eAAA,CAAAI,UAAA,KAAAF,WAAA,GAAAG,UAAA,IAAAF,WAAA,EAAAwE,OAAA;MACA;QACA,KAAA3E,eAAA,CAAAI,UAAA;MACA;IACA;IACA;IACAwE,mBAAA,WAAAA,oBAAAC,GAAA,EAAAC,IAAA,EAAAC,QAAA,EAAAC,SAAA;MACA,IAAAC,gBAAA,GAAAD,SAAA,CAAAE,KAAA;QAAAC,iBAAA,OAAAC,eAAA,CAAA5F,OAAA,EAAAyF,gBAAA;QAAAI,GAAA,GAAAF,iBAAA;QAAAG,IAAA,GAAAH,iBAAA;MACA,KAAAE,GAAA,EAAAC,IAAA,IAAAP,QAAA;IACA;IACA;IACAQ,YAAA,WAAAA,aAAAT,IAAA,EAAAC,QAAA,EAAAC,SAAA;MACA,IAAAQ,iBAAA,GAAAR,SAAA,CAAAE,KAAA;QAAAO,iBAAA,OAAAL,eAAA,CAAA5F,OAAA,EAAAgG,iBAAA;QAAAH,GAAA,GAAAI,iBAAA;QAAAH,IAAA,GAAAG,iBAAA;MACA,KAAAJ,GAAA,EAAAC,IAAA,IAAAP,QAAA;IACA;IACA;IACAW,iBAAA,WAAAA,kBAAA;MACA,KAAAC,MAAA,CAAAC,QAAA;MACA,KAAAD,MAAA,CAAAE,YAAA;IACA;IACA;IACAC,wBAAA,WAAAA,yBAAAhB,IAAA;MACA,KAAAlD,cAAA,GAAAkD,IAAA,CAAAiB,GAAA;MACA,KAAAlE,aAAA;IACA;IACA,WACAmE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,gBAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAvG,YAAA;MACA,IAAAwG,iBAAA,GAAAH,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAtG,aAAA;MACAmG,gBAAA,CAAAhD,UAAA,GAAAgD,gBAAA,CAAAhD,UAAA,CAAAqD,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,QAAA;MAAA,GAAAC,IAAA;MACAJ,iBAAA,CAAAzC,YAAA,GAAAyC,iBAAA,CAAAzC,YAAA,CAAA0C,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,QAAA;MAAA,GAAAC,IAAA;MACAR,gBAAA,CAAA/C,gBAAA,GACA+C,gBAAA,CAAA/C,gBAAA,YAAA+C,gBAAA,CAAA9C,aAAA,GAAA8C,gBAAA,CAAA/C,gBAAA;MACA;MACAmD,iBAAA,CAAA/C,YAAA,GAAA2C,gBAAA,CAAA3C,YAAA;;MAEA;MACA,IAAA2C,gBAAA,CAAAnD,eAAA;QACA;QACA,IAAA4D,cAAA,4FAAAC,MAAA,MAAA5G,eAAA,CAAAE,WAAA,4CAAA0G,MAAA,MAAA5G,eAAA,CAAAG,WAAA,4CAAAyG,MAAA,MAAA5G,eAAA,CAAAI,UAAA;QACA,KAAAyG,QAAA,CAAAF,cAAA;UACAG,iBAAA;UACAC,gBAAA;UACAzH,IAAA;QACA,GAAA0H,IAAA;UACAf,MAAA,CAAAgB,4BAAA,GAAAD,IAAA;YACA;YACAf,MAAA,CAAAiB,uBAAA,CAAAhB,gBAAA,EAAAI,iBAAA;UACA,GAAAa,KAAA;YACAlB,MAAA,CAAAN,MAAA,CAAAC,QAAA;UACA;QACA,GAAAuB,KAAA;UACA;QAAA,CACA;MACA;QACA;QACA,KAAAD,uBAAA,CAAAhB,gBAAA,EAAAI,iBAAA;MACA;IACA;IAEA,aACAW,4BAAA,WAAAA,6BAAA;MAAA,IAAAG,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA;QACAH,MAAA,CAAA9C,KAAA,CAAAC,kBAAA,CAAAiD,QAAA,WAAAC,KAAA;UACA,KAAAA,KAAA;YACAL,MAAA,CAAAM,QAAA,CAAAC,KAAA;YACAJ,MAAA;YACA;UACA;;UAEA;UACA,KAAAH,MAAA,CAAApH,eAAA,CAAAC,MAAA;YACAmH,MAAA,CAAAM,QAAA,CAAAC,KAAA;YACAJ,MAAA;YACA;UACA;;UAEA;UACA,IAAAnH,UAAA,GAAAsE,MAAA,CAAA0C,MAAA,CAAApH,eAAA,CAAAI,UAAA;UACA,IAAAA,UAAA;YACAgH,MAAA,CAAAM,QAAA,CAAAC,KAAA;YACAJ,MAAA;YACA;UACA;;UAEA;UACA,IAAAH,MAAA,CAAApH,eAAA,CAAAK,UAAA,SAAA+G,MAAA,CAAApH,eAAA,CAAAO,WAAA;YACA6G,MAAA,CAAAM,QAAA,CAAAC,KAAA;YACAJ,MAAA;YACA;UACA;UAEAtF,OAAA,CAAAC,GAAA,cAAAkF,MAAA,CAAApH,eAAA;;UAEA;UACA,IAAA4H,+DAAA,EAAAR,MAAA,CAAApH,eAAA,EAAAgH,IAAA,WAAAP,QAAA;YACA,IAAAA,QAAA,CAAAoB,IAAA;cACAT,MAAA,CAAAzB,MAAA,CAAAmC,UAAA;cACAR,OAAA;YACA;cACAF,MAAA,CAAAzB,MAAA,CAAAC,QAAA,gBAAAa,QAAA,CAAAsB,GAAA;cACAR,MAAA;YACA;UACA,GAAAJ,KAAA,WAAAQ,KAAA;YACA1F,OAAA,CAAA0F,KAAA,cAAAA,KAAA;YACAP,MAAA,CAAAzB,MAAA,CAAAC,QAAA;YACA2B,MAAA;UACA;QACA;MACA;IACA;IAEA,eACAL,uBAAA,WAAAA,wBAAAhB,gBAAA,EAAAI,iBAAA;MAAA,IAAA0B,MAAA;MACA/F,OAAA,CAAAC,GAAA,YAAAgE,gBAAA;MACAjE,OAAA,CAAAC,GAAA,YAAAoE,iBAAA;MACA,IAAA2B,+BAAA;QAAAnI,YAAA,EAAAoG,gBAAA;QAAAnG,aAAA,EAAAuG;MAAA,GAAAU,IAAA,WAAAnC,GAAA;QACAmD,MAAA,CAAArC,MAAA,CAAAmC,UAAA;QACAE,MAAA,CAAAnI,OAAA;QACAmI,MAAA,CAAAE,SAAA;MACA;IACA;IACA,WACAC,MAAA,WAAAA,OAAA;MACA,KAAAtI,OAAA;MACA,KAAAqI,SAAA;MACA;IACA;IACA,WACAA,SAAA,WAAAA,UAAA;MAAA,IAAAE,MAAA;MACA,KAAAtI,YAAA;QACAoD,UAAA;MACA;MACA,KAAAnD,aAAA;QACA8D,YAAA;MACA;MACA,KAAA7D,eAAA;QACAC,MAAA;QACAC,WAAA;QACAC,WAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,WAAA;QACAC,WAAA;QACAC,iBAAA;MACA;MACA;MACA,KAAA4D,SAAA;QACA,IAAA+D,MAAA,CAAA9D,KAAA,CAAAC,kBAAA;UACA6D,MAAA,CAAA9D,KAAA,CAAAC,kBAAA,CAAAC,aAAA;QACA;MACA;IACA;IACA,gBACA6D,UAAA,WAAAA,WAAA;MACA,KAAAxI,OAAA;MACA;IACA;IACA,eACAyI,YAAA,WAAAA,aAAAC,KAAA,EAAAxD,QAAA;MACA,KAAA2C,QAAA,CAAAc,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}