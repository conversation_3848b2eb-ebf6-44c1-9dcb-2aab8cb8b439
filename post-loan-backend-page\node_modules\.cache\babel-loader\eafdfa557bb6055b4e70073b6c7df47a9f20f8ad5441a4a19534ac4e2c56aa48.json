{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _regenerator2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/regenerator.js\"));\nvar _toConsumableArray2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/toConsumableArray.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nrequire(\"core-js/modules/es.array.concat.js\");\nrequire(\"core-js/modules/es.array.filter.js\");\nrequire(\"core-js/modules/es.array.find.js\");\nrequire(\"core-js/modules/es.array.includes.js\");\nrequire(\"core-js/modules/es.array.join.js\");\nrequire(\"core-js/modules/es.array.map.js\");\nrequire(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/es.function.name.js\");\nrequire(\"core-js/modules/es.json.stringify.js\");\nrequire(\"core-js/modules/es.number.constructor.js\");\nrequire(\"core-js/modules/es.object.keys.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/es.set.js\");\nrequire(\"core-js/modules/es.set.difference.v2.js\");\nrequire(\"core-js/modules/es.set.intersection.v2.js\");\nrequire(\"core-js/modules/es.set.is-disjoint-from.v2.js\");\nrequire(\"core-js/modules/es.set.is-subset-of.v2.js\");\nrequire(\"core-js/modules/es.set.is-superset-of.v2.js\");\nrequire(\"core-js/modules/es.set.symmetric-difference.v2.js\");\nrequire(\"core-js/modules/es.set.union.v2.js\");\nrequire(\"core-js/modules/es.string.includes.js\");\nrequire(\"core-js/modules/es.string.iterator.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.filter.js\");\nrequire(\"core-js/modules/esnext.iterator.find.js\");\nrequire(\"core-js/modules/esnext.iterator.for-each.js\");\nrequire(\"core-js/modules/esnext.iterator.map.js\");\nrequire(\"core-js/modules/web.dom-collections.for-each.js\");\nrequire(\"core-js/modules/web.dom-collections.iterator.js\");\nvar _litigation = require(\"@/api/litigation/litigation\");\nvar _partner_info = require(\"@/api/partner_info/partner_info\");\nvar _litigationStatus = _interopRequireDefault(require(\"@/layout/components/Dialog/litigationStatus.vue\"));\nvar _litigationForm = _interopRequireDefault(require(\"./modules/litigationForm\"));\nvar _litigationFeeForm = _interopRequireDefault(require(\"./modules/litigationFeeForm\"));\nvar _litigationLogForm = _interopRequireDefault(require(\"./modules/litigationLogForm\"));\nvar _litigationLogView = _interopRequireDefault(require(\"./modules/litigationLogView\"));\nvar _userInfo = _interopRequireDefault(require(\"@/layout/components/Dialog/userInfo.vue\"));\nvar _carInfo = _interopRequireDefault(require(\"@/layout/components/Dialog/carInfo.vue\"));\nvar _dispatchVehicleForm = _interopRequireDefault(require(\"@/layout/components/Dialog/dispatchVehicleForm.vue\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: 'Litigation',\n  components: {\n    litigationStatus: _litigationStatus.default,\n    litigationForm: _litigationForm.default,\n    litigationFeeForm: _litigationFeeForm.default,\n    litigationLogForm: _litigationLogForm.default,\n    litigationLogView: _litigationLogView.default,\n    userInfo: _userInfo.default,\n    carInfo: _carInfo.default,\n    dispatchVehicleForm: _dispatchVehicleForm.default\n  },\n  data: function data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 法诉案件表格数据\n      litigation_caseList: [],\n      // 法诉费用汇总数据 - 按案件ID存储\n      litigationCostSummary: {},\n      // 弹出层标题\n      title: '',\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        贷款人: '',\n        身份证: '',\n        车辆牌号: '',\n        车辆状态: '',\n        出单渠道: '',\n        lendingBank: '',\n        放款银行: '',\n        litigationTime: '',\n        litigationStartDate: '',\n        litigationEndDate: '',\n        法诉文员: '',\n        跟催员: '',\n        案件负责人: '',\n        法诉子状态: '',\n        诉讼法院: ''\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {},\n      // 当前行数据\n      currentRow: {},\n      showMore: false,\n      // 贷款人信息\n      userInfoVisible: false,\n      customerInfo: {\n        customerId: '',\n        applyId: ''\n      },\n      // 车辆信息\n      carShow: false,\n      plateNo: '',\n      // 欠款详情\n      debtDetailVisible: false,\n      currentDebtRow: null,\n      carStatusList: [{\n        label: '省内正常行驶',\n        value: '1'\n      }, {\n        label: '省外正常行驶',\n        value: '2'\n      }, {\n        label: '抵押',\n        value: '3'\n      }, {\n        label: '疑似抵押',\n        value: '4'\n      }, {\n        label: '疑似黑车',\n        value: '5'\n      }, {\n        label: '已入库',\n        value: '6'\n      }, {\n        label: '车在法院',\n        value: '7'\n      }, {\n        label: '已法拍',\n        value: '8'\n      }, {\n        label: '协商卖车',\n        value: '9'\n      }],\n      lendingBankList: [],\n      litigationStatusList: [{\n        label: '待立案',\n        value: '1'\n      }, {\n        label: '已立案',\n        value: '2'\n      }, {\n        label: '开庭',\n        value: '3'\n      }, {\n        label: '判决',\n        value: '4'\n      }, {\n        label: '结案',\n        value: '5'\n      }],\n      urgeTypeList: [{\n        label: '继续跟踪',\n        value: '1'\n      }, {\n        label: '约定还款',\n        value: '2'\n      }, {\n        label: '无法跟进',\n        value: '3'\n      }],\n      lawsuitCourtList: [{\n        label: '法院A',\n        value: 'A'\n      }, {\n        label: '法院B',\n        value: 'B'\n      }, {\n        label: '法院C',\n        value: 'C'\n      }],\n      lawsuitTypeList: [{\n        label: '债转',\n        value: '1'\n      }, {\n        label: '债加',\n        value: '2'\n      }, {\n        label: '担保物权',\n        value: '3'\n      }, {\n        label: '仲裁',\n        value: '4'\n      }, {\n        label: '赋强公证',\n        value: '5'\n      }, {\n        label: '拍状元',\n        value: '6'\n      }, {\n        label: '拍司令',\n        value: '7'\n      }, {\n        label: '属地诉讼',\n        value: '8'\n      }, {\n        label: '余值起诉',\n        value: '9'\n      }, {\n        label: '债权出售',\n        value: '10'\n      }, {\n        label: '签约地诉讼',\n        value: '11'\n      }, {\n        label: '特殊诉讼通道',\n        value: '12'\n      }],\n      lawsuitContentList: [{\n        label: '银行代偿金额',\n        value: '1'\n      }, {\n        label: '代扣金额',\n        value: '2'\n      }, {\n        label: '违约金',\n        value: '3'\n      }, {\n        label: '其他欠款',\n        value: '4'\n      }],\n      dispatchLoanId: ''\n    };\n  },\n  created: function created() {\n    this.getList();\n    this.getLendingBankList();\n  },\n  methods: {\n    /** 查询法诉案件列表 */getList: function getList() {\n      var _this = this;\n      this.loading = true;\n      (0, _litigation.listLitigation)(this.queryParams).then(function (response) {\n        _this.litigation_caseList = response.rows;\n        _this.total = response.total;\n        _this.loading = false;\n        // 获取法诉费用汇总数据\n        _this.loadLitigationCostSummary();\n      });\n    },\n    /** 加载法诉费用汇总数据 */loadLitigationCostSummary: function loadLitigationCostSummary() {\n      var _this2 = this;\n      // 获取所有案件ID，确保转换为数字类型\n      var caseIds = this.litigation_caseList.map(function (item) {\n        var id = item.序号;\n        // 确保ID是数字类型\n        return typeof id === 'string' ? parseInt(id) : Number(id);\n      }).filter(function (id) {\n        return id && !isNaN(id);\n      });\n      if (caseIds.length === 0) return;\n      console.log('发送的案件ID列表:', caseIds);\n\n      // 调用API获取费用汇总\n      (0, _litigation.getLitigationCostSummary)(caseIds).then(function (response) {\n        if (response.code === 200) {\n          _this2.litigationCostSummary = response.data || {};\n          console.log('获取到的费用汇总数据:', _this2.litigationCostSummary);\n        } else {\n          console.error('获取法诉费用汇总失败:', response.msg);\n          _this2.litigationCostSummary = {};\n        }\n      }).catch(function (error) {\n        console.error('获取法诉费用汇总失败:', error);\n        _this2.litigationCostSummary = {};\n      });\n    },\n    /** 获取放款银行列表 */getLendingBankList: function getLendingBankList() {\n      var _this3 = this;\n      (0, _partner_info.listPartner_info_simple)().then(function (response) {\n        // 将资金方数据转换为下拉框需要的格式\n        _this3.lendingBankList = (response.data || []).map(function (item) {\n          return {\n            label: item.name || item.orgName,\n            value: item.id\n          };\n        });\n      }).catch(function (error) {\n        console.error('获取放款银行列表失败:', error);\n        _this3.lendingBankList = [];\n      });\n    },\n    /** 获取法诉费用金额 */getLitigationFeeAmount: function getLitigationFeeAmount(caseId, feeType) {\n      // 确保caseId是正确的类型\n      var normalizedCaseId = typeof caseId === 'string' ? parseInt(caseId) : Number(caseId);\n      var summary = this.litigationCostSummary[normalizedCaseId];\n      if (!summary) {\n        return 0;\n      }\n      switch (feeType) {\n        case 'judgmentAmount':\n          return Number(summary.judgmentAmount || 0);\n        case 'interest':\n          return Number(summary.interest || 0);\n        case 'litigation':\n          // 诉讼费包含多种费用类型的总和\n          return Number(summary.lawyerFee || 0) + Number(summary.litigationFee || 0) + Number(summary.preservationFee || 0) + Number(summary.surveillanceFee || 0) + Number(summary.announcementFee || 0) + Number(summary.appraisalFee || 0) + Number(summary.executionFee || 0) + Number(summary.penalty || 0) + Number(summary.guaranteeFee || 0) + Number(summary.intermediaryFee || 0) + Number(summary.compensity || 0) + Number(summary.otherAmountsOwed || 0) + Number(summary.insurance || 0);\n        default:\n          return 0;\n      }\n    },\n    /** 查看诉讼费详情 */viewLitigationFeeDetails: function viewLitigationFeeDetails(caseId, feeType) {\n      // 确保caseId是正确的类型\n      var normalizedCaseId = typeof caseId === 'string' ? parseInt(caseId) : Number(caseId);\n\n      // 只处理诉讼费详情\n      if (feeType === 'litigation') {\n        var title = '诉讼费详情';\n        var content = this.formatLitigationFeeDetail(normalizedCaseId, ['lawyerFee', 'litigationFee', 'preservationFee', 'surveillanceFee', 'announcementFee', 'appraisalFee', 'executionFee', 'penalty', 'guaranteeFee', 'intermediaryFee', 'compensity', 'otherAmountsOwed', 'insurance']);\n        this.$alert(content, title, {\n          dangerouslyUseHTMLString: true,\n          confirmButtonText: '确定'\n        });\n      }\n    },\n    /** 格式化法诉费用详情 */formatLitigationFeeDetail: function formatLitigationFeeDetail(caseId, feeTypes) {\n      var _this4 = this;\n      var summary = this.litigationCostSummary[caseId];\n      if (!summary) return '<p>暂无费用数据</p>';\n      var feeLabels = {\n        judgmentAmount: '判决金额',\n        interest: '利息',\n        lawyerFee: '律师费',\n        litigationFee: '法诉费',\n        preservationFee: '保全费',\n        surveillanceFee: '布控费',\n        announcementFee: '公告费',\n        appraisalFee: '评估费',\n        executionFee: '执行费',\n        penalty: '违约金',\n        guaranteeFee: '担保费',\n        intermediaryFee: '居间费',\n        compensity: '代偿金',\n        otherAmountsOwed: '其他欠款',\n        insurance: '保险费'\n      };\n      var html = '<div style=\"text-align: left;\">';\n      var total = 0;\n      feeTypes.forEach(function (feeType) {\n        var amount = Number(summary[feeType] || 0);\n        if (amount > 0) {\n          html += \"<p>\".concat(feeLabels[feeType], \": \\uFFE5\").concat(_this4.formatMoney(amount), \"</p>\");\n          total += amount;\n        }\n      });\n      if (feeTypes.length > 1 && total > 0) {\n        html += \"<hr><p><strong>\\u5408\\u8BA1: \\uFFE5\".concat(this.formatMoney(total), \"</strong></p>\");\n      }\n      html += '</div>';\n      return html || '<p>暂无费用数据</p>';\n    },\n    handleQuery: function handleQuery() {\n      var _this5 = this;\n      // 同步放款银行筛选条件到后端期望的字段名\n      if (this.queryParams.lendingBank) {\n        // 根据选中的银行ID查找对应的银行名称\n        var selectedBank = this.lendingBankList.find(function (bank) {\n          return bank.value === _this5.queryParams.lendingBank;\n        });\n        this.queryParams.放款银行 = selectedBank ? selectedBank.label : '';\n      } else {\n        this.queryParams.放款银行 = '';\n      }\n\n      // 处理法诉时间区间筛选\n      if (this.queryParams.litigationTime && this.queryParams.litigationTime.length === 2) {\n        this.queryParams.litigationStartDate = this.queryParams.litigationTime[0];\n        this.queryParams.litigationEndDate = this.queryParams.litigationTime[1];\n      } else {\n        this.queryParams.litigationStartDate = '';\n        this.queryParams.litigationEndDate = '';\n      }\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery: function resetQuery() {\n      this.queryParams = {};\n      this.getList();\n    },\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.ids = selection.map(function (item) {\n        return item.id;\n      });\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    // 启动法诉弹窗\n    openLitigationForm: function openLitigationForm(row) {\n      this.currentRow = row || {};\n      this.$refs.litigationForm.open && this.$refs.litigationForm.open();\n    },\n    onLitigationFormConfirm: function onLitigationFormConfirm() {\n      // 处理确认逻辑\n    },\n    // 法诉费用弹窗\n    openLitigationFeeForm: function openLitigationFeeForm(row) {\n      this.currentRow = row || {};\n      this.$refs.litigationFeeForm.open && this.$refs.litigationFeeForm.open();\n    },\n    onLitigationFeeFormConfirm: function onLitigationFeeFormConfirm() {\n      // 处理确认逻辑\n    },\n    // 提交日志弹窗\n    openLitigationLogForm: function openLitigationLogForm(row) {\n      this.currentRow = JSON.parse(JSON.stringify(row));\n      this.$refs.litigationLogForm.openDialog && this.$refs.litigationLogForm.openDialog();\n    },\n    onLitigationLogFormConfirm: function onLitigationLogFormConfirm() {\n      // 处理确认逻辑\n    },\n    // 日志查看弹窗\n    openLitigationLogView: function openLitigationLogView(row) {\n      this.currentRow = row || {};\n      this.$refs.litigationLogView.openDialog && this.$refs.litigationLogView.openDialog();\n    },\n    // 找车按钮弹窗\n    openDispatchVehicleForm: function openDispatchVehicleForm(row) {\n      this.dispatchLoanId = row.流程序号;\n      this.$refs.dispatchVehicleForm.openDialog();\n    },\n    /** 计算待追偿欠款总额 */calculateTotalDebt: function calculateTotalDebt(row) {\n      var caseId = row.序号;\n\n      // 获取各项费用\n      var judgmentAmount = this.getLitigationFeeAmount(caseId, 'judgmentAmount'); // 判决金额\n      var interest = this.getLitigationFeeAmount(caseId, 'interest'); // 利息\n      var litigationCosts = this.getLitigationFeeAmount(caseId, 'litigation'); // 法诉费用\n      var unsuedAmount = this.calculateUnsuedAmount(row); // 未起诉金额\n\n      // 计算总额：判决金额 + 利息 + 法诉费用 + 未起诉金额\n      var total = Number(judgmentAmount) + Number(interest) + Number(litigationCosts) + Number(unsuedAmount);\n      return total;\n    },\n    /** 查看待追偿欠款详情 */viewTotalDebtDetails: function viewTotalDebtDetails(row) {\n      var caseId = row.序号;\n\n      // 获取各项费用\n      var judgmentAmount = this.getLitigationFeeAmount(caseId, 'judgmentAmount');\n      var interest = this.getLitigationFeeAmount(caseId, 'interest');\n      var litigationCosts = this.getLitigationFeeAmount(caseId, 'litigation');\n      var unsuedAmount = this.calculateUnsuedAmount(row);\n      var total = this.calculateTotalDebt(row);\n      var html = '<div style=\"text-align: left;\">';\n      html += \"<p>\\u5224\\u51B3\\u91D1\\u989D: \\uFFE5\".concat(this.formatMoney(judgmentAmount), \"</p>\");\n      html += \"<p>\\u5229\\u606F: \\uFFE5\".concat(this.formatMoney(interest), \"</p>\");\n      html += \"<p>\\u6CD5\\u8BC9\\u8D39\\u7528: \\uFFE5\".concat(this.formatMoney(litigationCosts), \"</p>\");\n      html += \"<p>\\u672A\\u8D77\\u8BC9\\u91D1\\u989D: \\uFFE5\".concat(this.formatMoney(unsuedAmount), \"</p>\");\n      html += \"<hr><p><strong>\\u5F85\\u8FFD\\u507F\\u6B20\\u6B3E\\u603B\\u8BA1: \\uFFE5\".concat(this.formatMoney(total), \"</strong></p>\");\n      html += '</div>';\n      this.$alert(html, '待追偿欠款详情', {\n        dangerouslyUseHTMLString: true,\n        confirmButtonText: '确定'\n      });\n    },\n    // 打开日常费用申请弹窗\n    openDailyExpenseDialog: function openDailyExpenseDialog(row) {\n      // 通过ref调用法诉费用表单组件的方法，传入案件ID\n      this.$refs.litigationFeeForm.openDailyExpenseDialog(row.序号);\n    },\n    // 查看贷款人信息\n    openUserInfo: function openUserInfo(customerInfo) {\n      this.customerInfo = customerInfo;\n      this.userInfoVisible = true;\n    },\n    // 查看车辆信息\n    checkCar: function checkCar(plateNo) {\n      this.plateNo = plateNo;\n      this.carShow = true;\n    },\n    // 获取起诉类型文字\n    getLawsuitTypeText: function getLawsuitTypeText(value) {\n      var item = this.lawsuitTypeList.find(function (item) {\n        return item.value === value;\n      });\n      return item ? item.label : value;\n    },\n    // 获取起诉内容文字\n    getLawsuitContentText: function getLawsuitContentText(value) {\n      var _this6 = this;\n      if (!value) return '-';\n      try {\n        // 尝试解析JSON数组（多选格式）\n        var contentArray = JSON.parse(value);\n        if (Array.isArray(contentArray)) {\n          return contentArray.map(function (val) {\n            var item = _this6.lawsuitContentList.find(function (item) {\n              return item.value === val;\n            });\n            return item ? item.label : val;\n          }).join('、');\n        }\n      } catch (_unused) {\n        // 如果不是JSON格式，按单选处理\n        var item = this.lawsuitContentList.find(function (item) {\n          return item.value === value;\n        });\n        return item ? item.label : value;\n      }\n\n      // 默认返回原值\n      return value;\n    },\n    // 获取催记类型文本\n    getUrgeStatusText: function getUrgeStatusText(value) {\n      var urgeStatusMap = {\n        1: '继续跟踪',\n        2: '约定还款',\n        3: '无法跟进'\n      };\n      return urgeStatusMap[value] || value;\n    },\n    // 获取车辆状态文本\n    getCarStatusText: function getCarStatusText(value) {\n      // 使用已有的 carStatusList 数据\n      var statusItem = this.carStatusList.find(function (item) {\n        return item.value == value;\n      });\n      return statusItem ? statusItem.label : value;\n    },\n    // 显示欠款详情弹窗\n    showDebtDetail: function showDebtDetail(row) {\n      this.currentDebtRow = row;\n      this.debtDetailVisible = true;\n    },\n    // 格式化金额显示\n    formatMoney: function formatMoney(amount) {\n      if (amount === null || amount === undefined || amount === '') {\n        return '0.00';\n      }\n      return Number(amount).toLocaleString('zh-CN', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      });\n    },\n    // 计算未起诉金额\n    calculateUnsuedAmount: function calculateUnsuedAmount(row) {\n      var remainingAmount = Number(row.剩余金额) || 0;\n      var suedAmount = Number(row.起诉金额) || 0;\n      var unsuedAmount = remainingAmount - suedAmount;\n      return unsuedAmount > 0 ? unsuedAmount : 0;\n    },\n    // 显示未起诉内容详情\n    showUnsuedContentDetail: function showUnsuedContentDetail(row) {\n      var _this7 = this;\n      return (0, _asyncToGenerator2.default)(/*#__PURE__*/(0, _regenerator2.default)().m(function _callee() {\n        var response, usedProsecutionContents, uniqueUsedContents, allProsecutionContents, unusedContents, html, totalUnsuedAmount, _t;\n        return (0, _regenerator2.default)().w(function (_context) {\n          while (1) switch (_context.p = _context.n) {\n            case 0:\n              _context.p = 0;\n              _context.n = 1;\n              return (0, _litigation.getLitigationByLoanId)(row.流程序号);\n            case 1:\n              response = _context.v;\n              if (response.code === 200 && response.data) {\n                // 获取所有已启动的法诉案件的起诉内容\n                usedProsecutionContents = [];\n                response.data.forEach(function (caseItem) {\n                  // 判断法诉是否已启动\n                  if (_this7.isLitigationStarted(caseItem)) {\n                    var content = caseItem.prosecutionContent;\n                    if (content) {\n                      try {\n                        // 尝试解析为数组（多选）\n                        var contentArray = JSON.parse(content);\n                        if (Array.isArray(contentArray)) {\n                          usedProsecutionContents.push.apply(usedProsecutionContents, (0, _toConsumableArray2.default)(contentArray));\n                        } else {\n                          usedProsecutionContents.push(content);\n                        }\n                      } catch (_unused2) {\n                        // 如果不是JSON格式，按单选处理\n                        usedProsecutionContents.push(content);\n                      }\n                    }\n                  }\n                });\n\n                // 去重已使用的起诉内容\n                uniqueUsedContents = (0, _toConsumableArray2.default)(new Set(usedProsecutionContents)); // 找出未使用的起诉内容\n                allProsecutionContents = _this7.lawsuitContentList;\n                unusedContents = allProsecutionContents.filter(function (item) {\n                  return !uniqueUsedContents.includes(item.value);\n                }); // 计算未起诉内容对应的金额\n                html = '<div style=\"text-align: left;\">';\n                totalUnsuedAmount = 0;\n                if (unusedContents.length > 0) {\n                  html += '<h4 style=\"margin-bottom: 15px; color: #303133;\">未起诉内容：</h4>';\n                  unusedContents.forEach(function (content) {\n                    var amount = 0;\n                    // 根据起诉内容类型计算对应金额\n                    switch (content.value) {\n                      case '1':\n                        // 银行代偿金额\n                        amount = Number(row.银行剩余未还代偿金) || 0;\n                        break;\n                      case '2':\n                        // 代扣金额\n                        amount = Number(row.代扣剩余未还代偿金) || 0;\n                        break;\n                      case '3':\n                        // 违约金\n                        amount = Number(row.剩余未还违约金金额) || 0;\n                        break;\n                      case '4':\n                        // 其他欠款\n                        amount = Number(row.剩余未还其他欠款) || 0;\n                        break;\n                    }\n\n                    // 显示所有未起诉内容，不管金额是否为0\n                    html += \"<p>\".concat(content.label, \": \\uFFE5\").concat(_this7.formatMoney(amount), \"</p>\");\n                    totalUnsuedAmount += amount;\n                  });\n                  html += \"<hr><p><strong>\\u672A\\u8D77\\u8BC9\\u91D1\\u989D\\u5408\\u8BA1: \\uFFE5\".concat(_this7.formatMoney(totalUnsuedAmount), \"</strong></p>\");\n                } else {\n                  html += '<p>所有起诉内容都已被选择</p>';\n                }\n                html += '</div>';\n                _this7.$alert(html, '未起诉内容详情', {\n                  dangerouslyUseHTMLString: true,\n                  confirmButtonText: '确定'\n                });\n              } else {\n                _this7.$message.error('获取法诉案件数据失败');\n              }\n              _context.n = 3;\n              break;\n            case 2:\n              _context.p = 2;\n              _t = _context.v;\n              _this7.$message.error('获取未起诉内容详情失败');\n            case 3:\n              return _context.a(2);\n          }\n        }, _callee, null, [[0, 2]]);\n      }))();\n    },\n    // 判断法诉是否已启动\n    isLitigationStarted: function isLitigationStarted(caseData) {\n      // 检查关键字段是否都有值\n      var hasLitigationType = caseData.litigationType != null && caseData.litigationType !== '';\n      var hasProsecutionType = caseData.prosecutionType != null && caseData.prosecutionType !== '';\n      var hasProsecutionContent = caseData.prosecutionContent != null && caseData.prosecutionContent !== '';\n      var hasLitigationStartDay = caseData.litigationStartDay != null && caseData.litigationStartDay !== '';\n      return hasLitigationType && hasProsecutionType && hasProsecutionContent && hasLitigationStartDay;\n    }\n  }\n};", "map": {"version": 3, "names": ["_litigation", "require", "_partner_info", "_litigationStatus", "_interopRequireDefault", "_litigationForm", "_litigationFeeForm", "_litigationLogForm", "_litigationLogView", "_userInfo", "_carInfo", "_dispatchVehicleForm", "name", "components", "litigationStatus", "litigationForm", "litigationFeeForm", "litigationLogForm", "litigationLogView", "userInfo", "carInfo", "dispatchVehicleForm", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "litigation_caseList", "litigationCostSummary", "title", "open", "queryParams", "pageNum", "pageSize", "贷款人", "身份证", "车辆牌号", "车辆状态", "出单渠道", "lendingBank", "放款银行", "litigationTime", "litigationStartDate", "litigationEndDate", "法诉文员", "跟催员", "案件负责人", "法诉子状态", "诉讼法院", "form", "rules", "currentRow", "showMore", "userInfoVisible", "customerInfo", "customerId", "applyId", "carShow", "plateNo", "debtDetailVisible", "currentDebtRow", "carStatusList", "label", "value", "lendingBankList", "litigationStatusList", "urgeTypeList", "lawsuitCourtList", "lawsuitTypeList", "lawsuitContentList", "dispatchLoanId", "created", "getList", "getLendingBankList", "methods", "_this", "listLitigation", "then", "response", "rows", "loadLitigationCostSummary", "_this2", "caseIds", "map", "item", "id", "序号", "parseInt", "Number", "filter", "isNaN", "length", "console", "log", "getLitigationCostSummary", "code", "error", "msg", "catch", "_this3", "listPartner_info_simple", "orgName", "getLitigationFeeAmount", "caseId", "feeType", "normalizedCaseId", "summary", "judgmentAmount", "interest", "<PERSON><PERSON><PERSON>", "litigationFee", "preservationFee", "surveillanceFee", "announcementFee", "appraisalFee", "executionFee", "penalty", "guaranteeFee", "intermediaryFee", "compensity", "otherAmountsOwed", "insurance", "viewLitigationFeeDetails", "content", "formatLitigationFeeDetail", "$alert", "dangerouslyUseHTMLString", "confirmButtonText", "feeTypes", "_this4", "feeLabels", "html", "for<PERSON>ach", "amount", "concat", "formatMoney", "handleQuery", "_this5", "selectedBank", "find", "bank", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "openLitigationForm", "row", "$refs", "onLitigationFormConfirm", "openLitigationFeeForm", "onLitigationFeeFormConfirm", "openLitigationLogForm", "JSON", "parse", "stringify", "openDialog", "onLitigationLogFormConfirm", "openLitigationLogView", "openDispatchVehicleForm", "流程序号", "calculateTotalDebt", "litigationCosts", "unsuedAmount", "calculateUnsuedAmount", "viewTotalDebtDetails", "openDailyExpenseDialog", "openUserInfo", "checkCar", "getLawsuitTypeText", "getLawsuitContentText", "_this6", "contentArray", "Array", "isArray", "val", "join", "_unused", "getUrgeStatusText", "urgeStatusMap", "getCarStatusText", "statusItem", "showDebtDetail", "undefined", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "remainingAmount", "剩余金额", "suedAmount", "起诉金额", "showUnsuedContentDetail", "_this7", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "usedProsecutionContents", "uniqueUsedContents", "allProsecutionContents", "unusedContents", "totalUnsuedAmount", "_t", "w", "_context", "p", "n", "getLitigationByLoanId", "v", "caseItem", "isLitigationStarted", "prosecutionContent", "push", "apply", "_toConsumableArray2", "_unused2", "Set", "includes", "银行剩余未还代偿金", "代扣剩余未还代偿金", "剩余未还违约金金额", "剩余未还其他欠款", "$message", "a", "caseData", "hasLitigationType", "litigationType", "hasProsecutionType", "prosecutionType", "has<PERSON>rose<PERSON>ion<PERSON><PERSON>nt", "hasLitigationStartDay", "litigationStartDay"], "sources": ["src/views/litigation/litigation/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 启动法诉弹窗 -->\r\n    <litigation-form ref=\"litigationForm\" :data=\"currentRow\" />\r\n\r\n    <!-- 法诉费用弹窗 -->\r\n    <litigation-fee-form ref=\"litigationFeeForm\" :data=\"currentRow\" />\r\n\r\n    <!-- 提交日志弹窗 -->\r\n    <litigation-log-form ref=\"litigationLogForm\" :data=\"currentRow\" />\r\n\r\n    <!-- 日志查看弹窗 -->\r\n    <litigation-log-view ref=\"litigationLogView\" :data=\"currentRow\" />\r\n\r\n    <!-- 派单找车组件 -->\r\n    <dispatch-vehicle-form ref=\"dispatchVehicleForm\" :loanId=\"dispatchLoanId\" />\r\n\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"\" prop=\"贷款人\">\r\n        <el-input v-model=\"queryParams.贷款人\" placeholder=\"贷款人账户、姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"身份证\">\r\n        <el-input v-model=\"queryParams.身份证\" placeholder=\"贷款人身份证号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"车辆牌号\">\r\n        <el-input v-model=\"queryParams.车辆牌号\" placeholder=\"车牌号码\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"车辆状态\">\r\n        <el-select v-model=\"queryParams.车辆状态\" placeholder=\"车辆状态\" clearable>\r\n          <el-option v-for=\"dict in carStatusList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"出单渠道\">\r\n        <el-input v-model=\"queryParams.出单渠道\" placeholder=\"录单渠道名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"lendingBank\">\r\n        <el-select v-model=\"queryParams.lendingBank\" placeholder=\"放款银行\" clearable>\r\n          <el-option v-for=\"dict in lendingBankList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"法诉时间\">\r\n        <el-date-picker\r\n          v-model=\"queryParams.litigationTime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <template v-if=\"showMore\">\r\n        <el-form-item label=\"\" prop=\"法诉文员\">\r\n          <el-input v-model=\"queryParams.法诉文员\" placeholder=\"法诉文员\" clearable @keyup.enter.native=\"handleQuery\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"跟催员\">\r\n          <el-input v-model=\"queryParams.跟催员\" placeholder=\"跟催员\" clearable @keyup.enter.native=\"handleQuery\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"案件负责人\">\r\n          <el-input v-model=\"queryParams.案件负责人\" placeholder=\"案件负责人\" clearable @keyup.enter.native=\"handleQuery\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"法诉子状态\">\r\n          <litigation-status v-model=\"queryParams.法诉子状态\" placeholder=\"法诉状态\" />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"\" prop=\"诉讼法院\">\r\n          <el-select v-model=\"queryParams.诉讼法院\" placeholder=\"诉讼法院\" clearable>\r\n            <el-option v-for=\"dict in lawsuitCourtList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </template>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        <el-button type=\"text\" size=\"mini\" @click=\"showMore = !showMore\">\r\n          {{ showMore ? '收起' : '更多' }}\r\n          <i :class=\"showMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\r\n        </el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"litigation_caseList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"55\" fixed=\"left\" />\r\n      <el-table-column label=\"法诉文员\" align=\"center\" prop=\"法诉文员\" width=\"100\" />\r\n      <el-table-column label=\"发起法诉日\" align=\"center\" prop=\"发起法诉日\" width=\"110\" />\r\n      <el-table-column label=\"案件启动日\" align=\"center\" prop=\"案件启动日\" width=\"110\" />\r\n      <el-table-column label=\"日志类型\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getUrgeStatusText(scope.row.日志类型) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"日志更新日\" align=\"center\" prop=\"日志更新日\" width=\"110\" />\r\n      <el-table-column label=\"法诉状态\" align=\"center\" prop=\"法诉子状态\" width=\"100\" />\r\n      <el-table-column label=\"贷款人\" align=\"center\" prop=\"贷款人\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"openUserInfo({ customerId: scope.row.客户ID, applyId: scope.row.申请编号 })\">\r\n            {{ scope.row.贷款人 }}\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"身份证\" align=\"center\" prop=\"身份证\" width=\"150\" />\r\n      <el-table-column label=\"车辆牌号\" align=\"center\" prop=\"车辆牌号\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"checkCar(scope.row.车辆牌号)\">{{ scope.row.车辆牌号 }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"车辆状态\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getCarStatusText(scope.row.车辆状态) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"找车团队\" align=\"center\" width=\"100\">\r\n        <template #default=\"{ row }\">\r\n          {{ row.找车团队 || '未派单' }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"地区\" align=\"center\" prop=\"地区\" width=\"100\" />\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"出单渠道\" width=\"120\" />\r\n      <el-table-column label=\"放款银行\" align=\"center\" prop=\"放款银行\" width=\"120\" />\r\n      <el-table-column label=\"托管类型\" align=\"center\" prop=\"托管类型\" width=\"100\" />\r\n      <el-table-column label=\"欠款余额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(scope.row.剩余金额) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"showDebtDetail(scope.row)\"\r\n              style=\"color: #409EFF; font-size: 12px;\"\r\n            >\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"未起诉金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(calculateUnsuedAmount(scope.row)) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"showUnsuedContentDetail(scope.row)\"\r\n              style=\"color: #409EFF; font-size: 12px;\"\r\n            >\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"起诉金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(scope.row.起诉金额) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"起诉类型\" align=\"center\" prop=\"起诉类型\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getLawsuitTypeText(scope.row.起诉类型) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"起诉内容\" align=\"center\" prop=\"起诉内容\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getLawsuitContentText(scope.row.起诉内容) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"判决金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(getLitigationFeeAmount(scope.row.序号, 'judgmentAmount')) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"利息\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(getLitigationFeeAmount(scope.row.序号, 'interest')) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"诉讼费\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(getLitigationFeeAmount(scope.row.序号, 'litigation')) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"viewLitigationFeeDetails(scope.row.序号, 'litigation')\"\r\n              style=\"font-size: 12px; padding: 0;\">\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"待追偿欠款\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(calculateTotalDebt(scope.row)) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"viewTotalDebtDetails(scope.row)\"\r\n              style=\"font-size: 12px; padding: 0;\">\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"代偿证明发出日\" align=\"center\" prop=\"代偿证明发出日\" width=\"140\" />\r\n      <el-table-column label=\"法院地\" align=\"center\" prop=\"法院地\" width=\"100\" />\r\n      <el-table-column label=\"诉讼法院\" align=\"center\" prop=\"诉讼法院\" width=\"120\" />\r\n      <el-table-column label=\"案件负责人\" align=\"center\" prop=\"案件负责人\" width=\"100\" />\r\n      <el-table-column label=\"诉前调号出具时间\" align=\"center\" prop=\"诉前调号出具时间\" width=\"150\" />\r\n      <el-table-column label=\"诉前调号\" align=\"center\" prop=\"诉前调号\" width=\"120\" />\r\n      <el-table-column label=\"民初号出具时间\" align=\"center\" prop=\"民初号出具时间\" width=\"140\" />\r\n      <el-table-column label=\"民初号\" align=\"center\" prop=\"民初号\" width=\"120\" />\r\n      <el-table-column label=\"开庭时间\" align=\"center\" prop=\"开庭时间\" width=\"110\" />\r\n      <el-table-column label=\"申请执行时间\" align=\"center\" prop=\"申请执行时间\" width=\"130\" />\r\n      <el-table-column label=\"执行号/执保号\" align=\"center\" prop=\"执行号/执保号\" width=\"140\" />\r\n      <el-table-column label=\"车辆出库时间\" align=\"center\" prop=\"车辆出库时间\" width=\"130\" />\r\n      <el-table-column label=\"法拍时间\" align=\"center\" prop=\"法拍时间\" width=\"110\" />\r\n      <el-table-column label=\"车辆评估价\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(scope.row.车辆评估价) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"拍卖金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(scope.row.拍卖金额) }}\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"操作\" align=\"center\" fixed=\"right\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-popover placement=\"left\" trigger=\"click\" popper-class=\"custom-popover\">\r\n            <div class=\"operation-buttons\">\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openLitigationLogView(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                查看日志\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:remove']\"\r\n                @click=\"openLitigationForm(scope.row)\">\r\n                启动法诉\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openLitigationLogForm(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                提交日志\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openLitigationFeeForm(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                法诉费用\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openDispatchVehicleForm(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                派单找车\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openDailyExpenseDialog(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                日常费用\r\n              </el-button>\r\n            </div>\r\n            <el-button slot=\"reference\" size=\"mini\" type=\"text\">更多</el-button>\r\n          </el-popover>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n\r\n    <!-- 贷款人信息对话框 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n    <!-- 车辆信息组件 -->\r\n    <car-info ref=\"carInfo\" :visible.sync=\"carShow\" title=\"车辆信息\" :plateNo=\"plateNo\" permission=\"2\" />\r\n\r\n    <!-- 欠款详情弹窗 -->\r\n    <el-dialog title=\"欠款详情\" :visible.sync=\"debtDetailVisible\" width=\"800px\" append-to-body>\r\n      <div v-if=\"currentDebtRow\" style=\"padding: 10px;\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">银行代偿</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">银行代偿金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.银行代偿金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">银行催回金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.银行催回金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">银行剩余未还:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.银行剩余未还代偿金) }}</span>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">代扣金额</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">代扣金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.代扣金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">代扣催回金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.代扣催回金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">代扣剩余未还:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.代扣剩余未还代偿金) }}</span>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">违约金</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">违约金:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.违约金) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">催回违约金:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.催回违约金金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">剩余未还违约金:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.剩余未还违约金金额) }}</span>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">其他欠款</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">其他欠款:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.其他欠款) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">催回其他欠款:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.催回其他欠款) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">剩余未还其他欠款:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.剩余未还其他欠款) }}</span>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <div style=\"margin-top: 20px; padding-top: 15px; border-top: 2px solid #409EFF; background-color: #f5f7fa; padding: 15px; border-radius: 4px;\">\r\n          <h4 style=\"margin-bottom: 15px; color: #409EFF;\">汇总信息</h4>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <div class=\"debt-item total-debt\">\r\n                <span class=\"debt-label\">总欠款金额:</span>\r\n                <span class=\"debt-value\">{{ formatMoney(currentDebtRow.总欠款金额) }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"debt-item total-debt\">\r\n                <span class=\"debt-label\">已还金额:</span>\r\n                <span class=\"debt-value\">{{ formatMoney(currentDebtRow.已还金额) }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"debt-item total-debt\">\r\n                <span class=\"debt-label\">剩余金额:</span>\r\n                <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.剩余金额) }}</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listLitigation, getLitigationCostSummary, getLitigationByLoanId } from '@/api/litigation/litigation'\r\nimport { listPartner_info_simple } from '@/api/partner_info/partner_info'\r\nimport litigationStatus from '@/layout/components/Dialog/litigationStatus.vue'\r\nimport litigationForm from './modules/litigationForm'\r\nimport litigationFeeForm from './modules/litigationFeeForm'\r\nimport litigationLogForm from './modules/litigationLogForm'\r\nimport litigationLogView from './modules/litigationLogView'\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport carInfo from '@/layout/components/Dialog/carInfo.vue'\r\nimport dispatchVehicleForm from '@/layout/components/Dialog/dispatchVehicleForm.vue'\r\n\r\nexport default {\r\n  name: 'Litigation',\r\n  components: {\r\n    litigationStatus,\r\n    litigationForm,\r\n    litigationFeeForm,\r\n    litigationLogForm,\r\n    litigationLogView,\r\n    userInfo,\r\n    carInfo,\r\n    dispatchVehicleForm,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 法诉案件表格数据\r\n      litigation_caseList: [],\r\n      // 法诉费用汇总数据 - 按案件ID存储\r\n      litigationCostSummary: {},\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        贷款人: '',\r\n        身份证: '',\r\n        车辆牌号: '',\r\n        车辆状态: '',\r\n        出单渠道: '',\r\n        lendingBank: '',\r\n        放款银行: '',\r\n        litigationTime: '',\r\n        litigationStartDate: '',\r\n        litigationEndDate: '',\r\n        法诉文员: '',\r\n        跟催员: '',\r\n        案件负责人: '',\r\n        法诉子状态: '',\r\n        诉讼法院: '',\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {},\r\n      // 当前行数据\r\n      currentRow: {},\r\n      showMore: false,\r\n      // 贷款人信息\r\n      userInfoVisible: false,\r\n      customerInfo: { customerId: '', applyId: '' },\r\n      // 车辆信息\r\n      carShow: false,\r\n      plateNo: '',\r\n      // 欠款详情\r\n      debtDetailVisible: false,\r\n      currentDebtRow: null,\r\n      carStatusList: [\r\n        { label: '省内正常行驶', value: '1' },\r\n        { label: '省外正常行驶', value: '2' },\r\n        { label: '抵押', value: '3' },\r\n        { label: '疑似抵押', value: '4' },\r\n        { label: '疑似黑车', value: '5' },\r\n        { label: '已入库', value: '6' },\r\n        { label: '车在法院', value: '7' },\r\n        { label: '已法拍', value: '8' },\r\n        { label: '协商卖车', value: '9' },\r\n      ],\r\n      lendingBankList: [],\r\n      litigationStatusList: [\r\n        { label: '待立案', value: '1' },\r\n        { label: '已立案', value: '2' },\r\n        { label: '开庭', value: '3' },\r\n        { label: '判决', value: '4' },\r\n        { label: '结案', value: '5' },\r\n      ],\r\n      urgeTypeList: [\r\n        { label: '继续跟踪', value: '1' },\r\n        { label: '约定还款', value: '2' },\r\n        { label: '无法跟进', value: '3' },\r\n      ],\r\n      lawsuitCourtList: [\r\n        { label: '法院A', value: 'A' },\r\n        { label: '法院B', value: 'B' },\r\n        { label: '法院C', value: 'C' },\r\n      ],\r\n      lawsuitTypeList: [\r\n        { label: '债转', value: '1' },\r\n        { label: '债加', value: '2' },\r\n        { label: '担保物权', value: '3' },\r\n        { label: '仲裁', value: '4' },\r\n        { label: '赋强公证', value: '5' },\r\n        { label: '拍状元', value: '6' },\r\n        { label: '拍司令', value: '7' },\r\n        { label: '属地诉讼', value: '8' },\r\n        { label: '余值起诉', value: '9' },\r\n        { label: '债权出售', value: '10' },\r\n        { label: '签约地诉讼', value: '11' },\r\n        { label: '特殊诉讼通道', value: '12' },\r\n      ],\r\n      lawsuitContentList: [\r\n        { label: '银行代偿金额', value: '1' },\r\n        { label: '代扣金额', value: '2' },\r\n        { label: '违约金', value: '3' },\r\n        { label: '其他欠款', value: '4' },\r\n      ],\r\n      dispatchLoanId: '',\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getLendingBankList()\r\n  },\r\n  methods: {\r\n    /** 查询法诉案件列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listLitigation(this.queryParams).then(response => {\r\n        this.litigation_caseList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n        // 获取法诉费用汇总数据\r\n        this.loadLitigationCostSummary()\r\n      })\r\n    },\r\n\r\n    /** 加载法诉费用汇总数据 */\r\n    loadLitigationCostSummary() {\r\n      // 获取所有案件ID，确保转换为数字类型\r\n      const caseIds = this.litigation_caseList\r\n        .map(item => {\r\n          const id = item.序号\r\n          // 确保ID是数字类型\r\n          return typeof id === 'string' ? parseInt(id) : Number(id)\r\n        })\r\n        .filter(id => id && !isNaN(id))\r\n\r\n      if (caseIds.length === 0) return\r\n\r\n      console.log('发送的案件ID列表:', caseIds)\r\n\r\n      // 调用API获取费用汇总\r\n      getLitigationCostSummary(caseIds).then(response => {\r\n        if (response.code === 200) {\r\n          this.litigationCostSummary = response.data || {}\r\n          console.log('获取到的费用汇总数据:', this.litigationCostSummary)\r\n        } else {\r\n          console.error('获取法诉费用汇总失败:', response.msg)\r\n          this.litigationCostSummary = {}\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取法诉费用汇总失败:', error)\r\n        this.litigationCostSummary = {}\r\n      })\r\n    },\r\n\r\n    /** 获取放款银行列表 */\r\n    getLendingBankList() {\r\n      listPartner_info_simple().then(response => {\r\n        // 将资金方数据转换为下拉框需要的格式\r\n        this.lendingBankList = (response.data || []).map(item => ({\r\n          label: item.name || item.orgName,\r\n          value: item.id\r\n        }))\r\n      }).catch(error => {\r\n        console.error('获取放款银行列表失败:', error)\r\n        this.lendingBankList = []\r\n      })\r\n    },\r\n\r\n    /** 获取法诉费用金额 */\r\n    getLitigationFeeAmount(caseId, feeType) {\r\n      // 确保caseId是正确的类型\r\n      const normalizedCaseId = typeof caseId === 'string' ? parseInt(caseId) : Number(caseId)\r\n      const summary = this.litigationCostSummary[normalizedCaseId]\r\n\r\n      if (!summary) {\r\n        return 0\r\n      }\r\n\r\n      switch (feeType) {\r\n        case 'judgmentAmount':\r\n          return Number(summary.judgmentAmount || 0)\r\n        case 'interest':\r\n          return Number(summary.interest || 0)\r\n        case 'litigation':\r\n          // 诉讼费包含多种费用类型的总和\r\n          return Number(summary.lawyerFee || 0) +\r\n                 Number(summary.litigationFee || 0) +\r\n                 Number(summary.preservationFee || 0) +\r\n                 Number(summary.surveillanceFee || 0) +\r\n                 Number(summary.announcementFee || 0) +\r\n                 Number(summary.appraisalFee || 0) +\r\n                 Number(summary.executionFee || 0) +\r\n                 Number(summary.penalty || 0) +\r\n                 Number(summary.guaranteeFee || 0) +\r\n                 Number(summary.intermediaryFee || 0) +\r\n                 Number(summary.compensity || 0) +\r\n                 Number(summary.otherAmountsOwed || 0) +\r\n                 Number(summary.insurance || 0)\r\n        default:\r\n          return 0\r\n      }\r\n    },\r\n\r\n    /** 查看诉讼费详情 */\r\n    viewLitigationFeeDetails(caseId, feeType) {\r\n      // 确保caseId是正确的类型\r\n      const normalizedCaseId = typeof caseId === 'string' ? parseInt(caseId) : Number(caseId)\r\n\r\n      // 只处理诉讼费详情\r\n      if (feeType === 'litigation') {\r\n        const title = '诉讼费详情'\r\n        const content = this.formatLitigationFeeDetail(normalizedCaseId, [\r\n          'lawyerFee', 'litigationFee', 'preservationFee', 'surveillanceFee',\r\n          'announcementFee', 'appraisalFee', 'executionFee', 'penalty',\r\n          'guaranteeFee', 'intermediaryFee', 'compensity', 'otherAmountsOwed', 'insurance'\r\n        ])\r\n\r\n        this.$alert(content, title, {\r\n          dangerouslyUseHTMLString: true,\r\n          confirmButtonText: '确定'\r\n        })\r\n      }\r\n    },\r\n\r\n    /** 格式化法诉费用详情 */\r\n    formatLitigationFeeDetail(caseId, feeTypes) {\r\n      const summary = this.litigationCostSummary[caseId]\r\n      if (!summary) return '<p>暂无费用数据</p>'\r\n\r\n      const feeLabels = {\r\n        judgmentAmount: '判决金额',\r\n        interest: '利息',\r\n        lawyerFee: '律师费',\r\n        litigationFee: '法诉费',\r\n        preservationFee: '保全费',\r\n        surveillanceFee: '布控费',\r\n        announcementFee: '公告费',\r\n        appraisalFee: '评估费',\r\n        executionFee: '执行费',\r\n        penalty: '违约金',\r\n        guaranteeFee: '担保费',\r\n        intermediaryFee: '居间费',\r\n        compensity: '代偿金',\r\n        otherAmountsOwed: '其他欠款',\r\n        insurance: '保险费'\r\n      }\r\n\r\n      let html = '<div style=\"text-align: left;\">'\r\n      let total = 0\r\n\r\n      feeTypes.forEach(feeType => {\r\n        const amount = Number(summary[feeType] || 0)\r\n        if (amount > 0) {\r\n          html += `<p>${feeLabels[feeType]}: ￥${this.formatMoney(amount)}</p>`\r\n          total += amount\r\n        }\r\n      })\r\n\r\n      if (feeTypes.length > 1 && total > 0) {\r\n        html += `<hr><p><strong>合计: ￥${this.formatMoney(total)}</strong></p>`\r\n      }\r\n\r\n      html += '</div>'\r\n      return html || '<p>暂无费用数据</p>'\r\n    },\r\n    handleQuery() {\r\n      // 同步放款银行筛选条件到后端期望的字段名\r\n      if (this.queryParams.lendingBank) {\r\n        // 根据选中的银行ID查找对应的银行名称\r\n        const selectedBank = this.lendingBankList.find(bank => bank.value === this.queryParams.lendingBank)\r\n        this.queryParams.放款银行 = selectedBank ? selectedBank.label : ''\r\n      } else {\r\n        this.queryParams.放款银行 = ''\r\n      }\r\n\r\n      // 处理法诉时间区间筛选\r\n      if (this.queryParams.litigationTime && this.queryParams.litigationTime.length === 2) {\r\n        this.queryParams.litigationStartDate = this.queryParams.litigationTime[0]\r\n        this.queryParams.litigationEndDate = this.queryParams.litigationTime[1]\r\n      } else {\r\n        this.queryParams.litigationStartDate = ''\r\n        this.queryParams.litigationEndDate = ''\r\n      }\r\n\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    resetQuery() {\r\n      this.queryParams = {}\r\n      this.getList()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    // 启动法诉弹窗\r\n    openLitigationForm(row) {\r\n      this.currentRow = row || {}\r\n      this.$refs.litigationForm.open && this.$refs.litigationForm.open()\r\n    },\r\n    onLitigationFormConfirm() {\r\n      // 处理确认逻辑\r\n    },\r\n    // 法诉费用弹窗\r\n    openLitigationFeeForm(row) {\r\n      this.currentRow = row || {}\r\n      this.$refs.litigationFeeForm.open && this.$refs.litigationFeeForm.open()\r\n    },\r\n    onLitigationFeeFormConfirm() {\r\n      // 处理确认逻辑\r\n    },\r\n    // 提交日志弹窗\r\n    openLitigationLogForm(row) {\r\n      this.currentRow = JSON.parse(JSON.stringify(row))\r\n      this.$refs.litigationLogForm.openDialog && this.$refs.litigationLogForm.openDialog()\r\n    },\r\n    onLitigationLogFormConfirm() {\r\n      // 处理确认逻辑\r\n    },\r\n    // 日志查看弹窗\r\n    openLitigationLogView(row) {\r\n      this.currentRow = row || {}\r\n      this.$refs.litigationLogView.openDialog && this.$refs.litigationLogView.openDialog()\r\n    },\r\n    // 找车按钮弹窗\r\n    openDispatchVehicleForm(row) {\r\n      this.dispatchLoanId = row.流程序号\r\n      this.$refs.dispatchVehicleForm.openDialog()\r\n    },\r\n    /** 计算待追偿欠款总额 */\r\n    calculateTotalDebt(row) {\r\n      const caseId = row.序号\r\n\r\n      // 获取各项费用\r\n      const judgmentAmount = this.getLitigationFeeAmount(caseId, 'judgmentAmount') // 判决金额\r\n      const interest = this.getLitigationFeeAmount(caseId, 'interest') // 利息\r\n      const litigationCosts = this.getLitigationFeeAmount(caseId, 'litigation') // 法诉费用\r\n      const unsuedAmount = this.calculateUnsuedAmount(row) // 未起诉金额\r\n\r\n      // 计算总额：判决金额 + 利息 + 法诉费用 + 未起诉金额\r\n      const total = Number(judgmentAmount) + Number(interest) + Number(litigationCosts) + Number(unsuedAmount)\r\n\r\n      return total\r\n    },\r\n\r\n    /** 查看待追偿欠款详情 */\r\n    viewTotalDebtDetails(row) {\r\n      const caseId = row.序号\r\n\r\n      // 获取各项费用\r\n      const judgmentAmount = this.getLitigationFeeAmount(caseId, 'judgmentAmount')\r\n      const interest = this.getLitigationFeeAmount(caseId, 'interest')\r\n      const litigationCosts = this.getLitigationFeeAmount(caseId, 'litigation')\r\n      const unsuedAmount = this.calculateUnsuedAmount(row)\r\n      const total = this.calculateTotalDebt(row)\r\n\r\n      let html = '<div style=\"text-align: left;\">'\r\n      html += `<p>判决金额: ￥${this.formatMoney(judgmentAmount)}</p>`\r\n      html += `<p>利息: ￥${this.formatMoney(interest)}</p>`\r\n      html += `<p>法诉费用: ￥${this.formatMoney(litigationCosts)}</p>`\r\n      html += `<p>未起诉金额: ￥${this.formatMoney(unsuedAmount)}</p>`\r\n      html += `<hr><p><strong>待追偿欠款总计: ￥${this.formatMoney(total)}</strong></p>`\r\n      html += '</div>'\r\n\r\n      this.$alert(html, '待追偿欠款详情', {\r\n        dangerouslyUseHTMLString: true,\r\n        confirmButtonText: '确定'\r\n      })\r\n    },\r\n\r\n    // 打开日常费用申请弹窗\r\n    openDailyExpenseDialog(row) {\r\n      // 通过ref调用法诉费用表单组件的方法，传入案件ID\r\n      this.$refs.litigationFeeForm.openDailyExpenseDialog(row.序号)\r\n    },\r\n    // 查看贷款人信息\r\n    openUserInfo(customerInfo) {\r\n      this.customerInfo = customerInfo\r\n      this.userInfoVisible = true\r\n    },\r\n    // 查看车辆信息\r\n    checkCar(plateNo) {\r\n      this.plateNo = plateNo\r\n      this.carShow = true\r\n    },\r\n    // 获取起诉类型文字\r\n    getLawsuitTypeText(value) {\r\n      const item = this.lawsuitTypeList.find(item => item.value === value)\r\n      return item ? item.label : value\r\n    },\r\n    // 获取起诉内容文字\r\n    getLawsuitContentText(value) {\r\n      if (!value) return '-'\r\n\r\n      try {\r\n        // 尝试解析JSON数组（多选格式）\r\n        const contentArray = JSON.parse(value)\r\n        if (Array.isArray(contentArray)) {\r\n          return contentArray.map(val => {\r\n            const item = this.lawsuitContentList.find(item => item.value === val)\r\n            return item ? item.label : val\r\n          }).join('、')\r\n        }\r\n      } catch {\r\n        // 如果不是JSON格式，按单选处理\r\n        const item = this.lawsuitContentList.find(item => item.value === value)\r\n        return item ? item.label : value\r\n      }\r\n\r\n      // 默认返回原值\r\n      return value\r\n    },\r\n    // 获取催记类型文本\r\n    getUrgeStatusText(value) {\r\n      const urgeStatusMap = {\r\n        1: '继续跟踪',\r\n        2: '约定还款',\r\n        3: '无法跟进'\r\n      }\r\n      return urgeStatusMap[value] || value\r\n    },\r\n    // 获取车辆状态文本\r\n    getCarStatusText(value) {\r\n      // 使用已有的 carStatusList 数据\r\n      const statusItem = this.carStatusList.find(item => item.value == value)\r\n      return statusItem ? statusItem.label : value\r\n    },\r\n    // 显示欠款详情弹窗\r\n    showDebtDetail(row) {\r\n      this.currentDebtRow = row\r\n      this.debtDetailVisible = true\r\n    },\r\n    // 格式化金额显示\r\n    formatMoney(amount) {\r\n      if (amount === null || amount === undefined || amount === '') {\r\n        return '0.00'\r\n      }\r\n      return Number(amount).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      })\r\n    },\r\n    // 计算未起诉金额\r\n    calculateUnsuedAmount(row) {\r\n      const remainingAmount = Number(row.剩余金额) || 0\r\n      const suedAmount = Number(row.起诉金额) || 0\r\n      const unsuedAmount = remainingAmount - suedAmount\r\n      return unsuedAmount > 0 ? unsuedAmount : 0\r\n    },\r\n\r\n    // 显示未起诉内容详情\r\n    async showUnsuedContentDetail(row) {\r\n      try {\r\n        // 获取该贷款的所有法诉案件\r\n        const response = await getLitigationByLoanId(row.流程序号)\r\n\r\n        if (response.code === 200 && response.data) {\r\n          // 获取所有已启动的法诉案件的起诉内容\r\n          const usedProsecutionContents = []\r\n\r\n          response.data.forEach(caseItem => {\r\n            // 判断法诉是否已启动\r\n            if (this.isLitigationStarted(caseItem)) {\r\n              const content = caseItem.prosecutionContent\r\n              if (content) {\r\n                try {\r\n                  // 尝试解析为数组（多选）\r\n                  const contentArray = JSON.parse(content)\r\n                  if (Array.isArray(contentArray)) {\r\n                    usedProsecutionContents.push(...contentArray)\r\n                  } else {\r\n                    usedProsecutionContents.push(content)\r\n                  }\r\n                } catch {\r\n                  // 如果不是JSON格式，按单选处理\r\n                  usedProsecutionContents.push(content)\r\n                }\r\n              }\r\n            }\r\n          })\r\n\r\n          // 去重已使用的起诉内容\r\n          const uniqueUsedContents = [...new Set(usedProsecutionContents)]\r\n\r\n          // 找出未使用的起诉内容\r\n          const allProsecutionContents = this.lawsuitContentList\r\n          const unusedContents = allProsecutionContents.filter(item =>\r\n            !uniqueUsedContents.includes(item.value)\r\n          )\r\n\r\n          // 计算未起诉内容对应的金额\r\n          let html = '<div style=\"text-align: left;\">'\r\n          let totalUnsuedAmount = 0\r\n\r\n          if (unusedContents.length > 0) {\r\n            html += '<h4 style=\"margin-bottom: 15px; color: #303133;\">未起诉内容：</h4>'\r\n\r\n            unusedContents.forEach(content => {\r\n              let amount = 0\r\n              // 根据起诉内容类型计算对应金额\r\n              switch (content.value) {\r\n                case '1': // 银行代偿金额\r\n                  amount = Number(row.银行剩余未还代偿金) || 0\r\n                  break\r\n                case '2': // 代扣金额\r\n                  amount = Number(row.代扣剩余未还代偿金) || 0\r\n                  break\r\n                case '3': // 违约金\r\n                  amount = Number(row.剩余未还违约金金额) || 0\r\n                  break\r\n                case '4': // 其他欠款\r\n                  amount = Number(row.剩余未还其他欠款) || 0\r\n                  break\r\n              }\r\n\r\n              // 显示所有未起诉内容，不管金额是否为0\r\n              html += `<p>${content.label}: ￥${this.formatMoney(amount)}</p>`\r\n              totalUnsuedAmount += amount\r\n            })\r\n\r\n            html += `<hr><p><strong>未起诉金额合计: ￥${this.formatMoney(totalUnsuedAmount)}</strong></p>`\r\n          } else {\r\n            html += '<p>所有起诉内容都已被选择</p>'\r\n          }\r\n\r\n          html += '</div>'\r\n\r\n          this.$alert(html, '未起诉内容详情', {\r\n            dangerouslyUseHTMLString: true,\r\n            confirmButtonText: '确定'\r\n          })\r\n        } else {\r\n          this.$message.error('获取法诉案件数据失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('获取未起诉内容详情失败')\r\n      }\r\n    },\r\n\r\n    // 判断法诉是否已启动\r\n    isLitigationStarted(caseData) {\r\n      // 检查关键字段是否都有值\r\n      const hasLitigationType = caseData.litigationType != null && caseData.litigationType !== ''\r\n      const hasProsecutionType = caseData.prosecutionType != null && caseData.prosecutionType !== ''\r\n      const hasProsecutionContent = caseData.prosecutionContent != null && caseData.prosecutionContent !== ''\r\n      const hasLitigationStartDay = caseData.litigationStartDay != null && caseData.litigationStartDay !== ''\r\n\r\n      return hasLitigationType && hasProsecutionType && hasProsecutionContent && hasLitigationStartDay\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 操作按钮容器 */\r\n.operation-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8px;\r\n  width: 90px;\r\n  padding: 4px 8px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.operation-btn {\r\n  width: 74px !important;\r\n  height: 26px !important;\r\n  margin: 0 !important;\r\n  padding: 0 6px !important;\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  text-align: center;\r\n  line-height: 26px;\r\n}\r\n\r\n/* 按钮悬停效果 */\r\n.operation-btn:hover {\r\n  background-color: #f5f7fa;\r\n  color: #409eff;\r\n}\r\n\r\n/* 欠款详情样式 */\r\n.debt-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  padding: 5px 0;\r\n}\r\n\r\n.debt-label {\r\n  font-size: 13px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.debt-value {\r\n  font-size: 13px;\r\n  color: #303133;\r\n  font-weight: 600;\r\n}\r\n\r\n.debt-remaining {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.total-debt {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n.total-debt .debt-label {\r\n  color: #303133;\r\n  font-weight: 600;\r\n}\r\n\r\n.total-debt .debt-value {\r\n  font-size: 15px;\r\n  color: #409EFF;\r\n}\r\n</style>\r\n<style>\r\n.custom-popover {\r\n  width: 116px !important;\r\n  min-width: 116px !important;\r\n  max-width: 116px !important;\r\n  box-sizing: border-box !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsYA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,iBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,eAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,kBAAA,GAAAF,sBAAA,CAAAH,OAAA;AACA,IAAAM,kBAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,kBAAA,GAAAJ,sBAAA,CAAAH,OAAA;AACA,IAAAQ,SAAA,GAAAL,sBAAA,CAAAH,OAAA;AACA,IAAAS,QAAA,GAAAN,sBAAA,CAAAH,OAAA;AACA,IAAAU,oBAAA,GAAAP,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAW,IAAA;EACAC,UAAA;IACAC,gBAAA,EAAAA,yBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,OAAA,EAAAA,gBAAA;IACAC,mBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,mBAAA;MACA;MACAC,qBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,GAAA;QACAC,GAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,WAAA;QACAC,IAAA;QACAC,cAAA;QACAC,mBAAA;QACAC,iBAAA;QACAC,IAAA;QACAC,GAAA;QACAC,KAAA;QACAC,KAAA;QACAC,IAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACAC,QAAA;MACA;MACAC,eAAA;MACAC,YAAA;QAAAC,UAAA;QAAAC,OAAA;MAAA;MACA;MACAC,OAAA;MACAC,OAAA;MACA;MACAC,iBAAA;MACAC,cAAA;MACAC,aAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,eAAA;MACAC,oBAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAG,YAAA,GACA;QAAAJ,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAI,gBAAA,GACA;QAAAL,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAK,eAAA,GACA;QAAAN,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAM,kBAAA,GACA;QAAAP,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAO,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,kBAAA;EACA;EACAC,OAAA;IACA,eACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,KAAAtD,OAAA;MACA,IAAAuD,0BAAA,OAAA7C,WAAA,EAAA8C,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAhD,mBAAA,GAAAmD,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAjD,KAAA,GAAAoD,QAAA,CAAApD,KAAA;QACAiD,KAAA,CAAAtD,OAAA;QACA;QACAsD,KAAA,CAAAK,yBAAA;MACA;IACA;IAEA,iBACAA,yBAAA,WAAAA,0BAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,OAAA,QAAAvD,mBAAA,CACAwD,GAAA,WAAAC,IAAA;QACA,IAAAC,EAAA,GAAAD,IAAA,CAAAE,EAAA;QACA;QACA,cAAAD,EAAA,gBAAAE,QAAA,CAAAF,EAAA,IAAAG,MAAA,CAAAH,EAAA;MACA,GACAI,MAAA,WAAAJ,EAAA;QAAA,OAAAA,EAAA,KAAAK,KAAA,CAAAL,EAAA;MAAA;MAEA,IAAAH,OAAA,CAAAS,MAAA;MAEAC,OAAA,CAAAC,GAAA,eAAAX,OAAA;;MAEA;MACA,IAAAY,oCAAA,EAAAZ,OAAA,EAAAL,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAiB,IAAA;UACAd,MAAA,CAAArD,qBAAA,GAAAkD,QAAA,CAAA1D,IAAA;UACAwE,OAAA,CAAAC,GAAA,gBAAAZ,MAAA,CAAArD,qBAAA;QACA;UACAgE,OAAA,CAAAI,KAAA,gBAAAlB,QAAA,CAAAmB,GAAA;UACAhB,MAAA,CAAArD,qBAAA;QACA;MACA,GAAAsE,KAAA,WAAAF,KAAA;QACAJ,OAAA,CAAAI,KAAA,gBAAAA,KAAA;QACAf,MAAA,CAAArD,qBAAA;MACA;IACA;IAEA,eACA6C,kBAAA,WAAAA,mBAAA;MAAA,IAAA0B,MAAA;MACA,IAAAC,qCAAA,IAAAvB,IAAA,WAAAC,QAAA;QACA;QACAqB,MAAA,CAAAnC,eAAA,IAAAc,QAAA,CAAA1D,IAAA,QAAA+D,GAAA,WAAAC,IAAA;UAAA;YACAtB,KAAA,EAAAsB,IAAA,CAAA1E,IAAA,IAAA0E,IAAA,CAAAiB,OAAA;YACAtC,KAAA,EAAAqB,IAAA,CAAAC;UACA;QAAA;MACA,GAAAa,KAAA,WAAAF,KAAA;QACAJ,OAAA,CAAAI,KAAA,gBAAAA,KAAA;QACAG,MAAA,CAAAnC,eAAA;MACA;IACA;IAEA,eACAsC,sBAAA,WAAAA,uBAAAC,MAAA,EAAAC,OAAA;MACA;MACA,IAAAC,gBAAA,UAAAF,MAAA,gBAAAhB,QAAA,CAAAgB,MAAA,IAAAf,MAAA,CAAAe,MAAA;MACA,IAAAG,OAAA,QAAA9E,qBAAA,CAAA6E,gBAAA;MAEA,KAAAC,OAAA;QACA;MACA;MAEA,QAAAF,OAAA;QACA;UACA,OAAAhB,MAAA,CAAAkB,OAAA,CAAAC,cAAA;QACA;UACA,OAAAnB,MAAA,CAAAkB,OAAA,CAAAE,QAAA;QACA;UACA;UACA,OAAApB,MAAA,CAAAkB,OAAA,CAAAG,SAAA,SACArB,MAAA,CAAAkB,OAAA,CAAAI,aAAA,SACAtB,MAAA,CAAAkB,OAAA,CAAAK,eAAA,SACAvB,MAAA,CAAAkB,OAAA,CAAAM,eAAA,SACAxB,MAAA,CAAAkB,OAAA,CAAAO,eAAA,SACAzB,MAAA,CAAAkB,OAAA,CAAAQ,YAAA,SACA1B,MAAA,CAAAkB,OAAA,CAAAS,YAAA,SACA3B,MAAA,CAAAkB,OAAA,CAAAU,OAAA,SACA5B,MAAA,CAAAkB,OAAA,CAAAW,YAAA,SACA7B,MAAA,CAAAkB,OAAA,CAAAY,eAAA,SACA9B,MAAA,CAAAkB,OAAA,CAAAa,UAAA,SACA/B,MAAA,CAAAkB,OAAA,CAAAc,gBAAA,SACAhC,MAAA,CAAAkB,OAAA,CAAAe,SAAA;QACA;UACA;MACA;IACA;IAEA,cACAC,wBAAA,WAAAA,yBAAAnB,MAAA,EAAAC,OAAA;MACA;MACA,IAAAC,gBAAA,UAAAF,MAAA,gBAAAhB,QAAA,CAAAgB,MAAA,IAAAf,MAAA,CAAAe,MAAA;;MAEA;MACA,IAAAC,OAAA;QACA,IAAA3E,KAAA;QACA,IAAA8F,OAAA,QAAAC,yBAAA,CAAAnB,gBAAA,GACA,oEACA,8DACA,iFACA;QAEA,KAAAoB,MAAA,CAAAF,OAAA,EAAA9F,KAAA;UACAiG,wBAAA;UACAC,iBAAA;QACA;MACA;IACA;IAEA,gBACAH,yBAAA,WAAAA,0BAAArB,MAAA,EAAAyB,QAAA;MAAA,IAAAC,MAAA;MACA,IAAAvB,OAAA,QAAA9E,qBAAA,CAAA2E,MAAA;MACA,KAAAG,OAAA;MAEA,IAAAwB,SAAA;QACAvB,cAAA;QACAC,QAAA;QACAC,SAAA;QACAC,aAAA;QACAC,eAAA;QACAC,eAAA;QACAC,eAAA;QACAC,YAAA;QACAC,YAAA;QACAC,OAAA;QACAC,YAAA;QACAC,eAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,SAAA;MACA;MAEA,IAAAU,IAAA;MACA,IAAAzG,KAAA;MAEAsG,QAAA,CAAAI,OAAA,WAAA5B,OAAA;QACA,IAAA6B,MAAA,GAAA7C,MAAA,CAAAkB,OAAA,CAAAF,OAAA;QACA,IAAA6B,MAAA;UACAF,IAAA,UAAAG,MAAA,CAAAJ,SAAA,CAAA1B,OAAA,eAAA8B,MAAA,CAAAL,MAAA,CAAAM,WAAA,CAAAF,MAAA;UACA3G,KAAA,IAAA2G,MAAA;QACA;MACA;MAEA,IAAAL,QAAA,CAAArC,MAAA,QAAAjE,KAAA;QACAyG,IAAA,0CAAAG,MAAA,MAAAC,WAAA,CAAA7G,KAAA;MACA;MAEAyG,IAAA;MACA,OAAAA,IAAA;IACA;IACAK,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAA1G,WAAA,CAAAQ,WAAA;QACA;QACA,IAAAmG,YAAA,QAAA1E,eAAA,CAAA2E,IAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAA7E,KAAA,KAAA0E,MAAA,CAAA1G,WAAA,CAAAQ,WAAA;QAAA;QACA,KAAAR,WAAA,CAAAS,IAAA,GAAAkG,YAAA,GAAAA,YAAA,CAAA5E,KAAA;MACA;QACA,KAAA/B,WAAA,CAAAS,IAAA;MACA;;MAEA;MACA,SAAAT,WAAA,CAAAU,cAAA,SAAAV,WAAA,CAAAU,cAAA,CAAAkD,MAAA;QACA,KAAA5D,WAAA,CAAAW,mBAAA,QAAAX,WAAA,CAAAU,cAAA;QACA,KAAAV,WAAA,CAAAY,iBAAA,QAAAZ,WAAA,CAAAU,cAAA;MACA;QACA,KAAAV,WAAA,CAAAW,mBAAA;QACA,KAAAX,WAAA,CAAAY,iBAAA;MACA;MAEA,KAAAZ,WAAA,CAAAC,OAAA;MACA,KAAAwC,OAAA;IACA;IACAqE,UAAA,WAAAA,WAAA;MACA,KAAA9G,WAAA;MACA,KAAAyC,OAAA;IACA;IACAsE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAzH,GAAA,GAAAyH,SAAA,CAAA5D,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,EAAA;MAAA;MACA,KAAA9D,MAAA,GAAAwH,SAAA,CAAApD,MAAA;MACA,KAAAnE,QAAA,IAAAuH,SAAA,CAAApD,MAAA;IACA;IACA;IACAqD,kBAAA,WAAAA,mBAAAC,GAAA;MACA,KAAA9F,UAAA,GAAA8F,GAAA;MACA,KAAAC,KAAA,CAAArI,cAAA,CAAAiB,IAAA,SAAAoH,KAAA,CAAArI,cAAA,CAAAiB,IAAA;IACA;IACAqH,uBAAA,WAAAA,wBAAA;MACA;IAAA,CACA;IACA;IACAC,qBAAA,WAAAA,sBAAAH,GAAA;MACA,KAAA9F,UAAA,GAAA8F,GAAA;MACA,KAAAC,KAAA,CAAApI,iBAAA,CAAAgB,IAAA,SAAAoH,KAAA,CAAApI,iBAAA,CAAAgB,IAAA;IACA;IACAuH,0BAAA,WAAAA,2BAAA;MACA;IAAA,CACA;IACA;IACAC,qBAAA,WAAAA,sBAAAL,GAAA;MACA,KAAA9F,UAAA,GAAAoG,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAR,GAAA;MACA,KAAAC,KAAA,CAAAnI,iBAAA,CAAA2I,UAAA,SAAAR,KAAA,CAAAnI,iBAAA,CAAA2I,UAAA;IACA;IACAC,0BAAA,WAAAA,2BAAA;MACA;IAAA,CACA;IACA;IACAC,qBAAA,WAAAA,sBAAAX,GAAA;MACA,KAAA9F,UAAA,GAAA8F,GAAA;MACA,KAAAC,KAAA,CAAAlI,iBAAA,CAAA0I,UAAA,SAAAR,KAAA,CAAAlI,iBAAA,CAAA0I,UAAA;IACA;IACA;IACAG,uBAAA,WAAAA,wBAAAZ,GAAA;MACA,KAAA3E,cAAA,GAAA2E,GAAA,CAAAa,IAAA;MACA,KAAAZ,KAAA,CAAA/H,mBAAA,CAAAuI,UAAA;IACA;IACA,gBACAK,kBAAA,WAAAA,mBAAAd,GAAA;MACA,IAAA1C,MAAA,GAAA0C,GAAA,CAAA3D,EAAA;;MAEA;MACA,IAAAqB,cAAA,QAAAL,sBAAA,CAAAC,MAAA;MACA,IAAAK,QAAA,QAAAN,sBAAA,CAAAC,MAAA;MACA,IAAAyD,eAAA,QAAA1D,sBAAA,CAAAC,MAAA;MACA,IAAA0D,YAAA,QAAAC,qBAAA,CAAAjB,GAAA;;MAEA;MACA,IAAAvH,KAAA,GAAA8D,MAAA,CAAAmB,cAAA,IAAAnB,MAAA,CAAAoB,QAAA,IAAApB,MAAA,CAAAwE,eAAA,IAAAxE,MAAA,CAAAyE,YAAA;MAEA,OAAAvI,KAAA;IACA;IAEA,gBACAyI,oBAAA,WAAAA,qBAAAlB,GAAA;MACA,IAAA1C,MAAA,GAAA0C,GAAA,CAAA3D,EAAA;;MAEA;MACA,IAAAqB,cAAA,QAAAL,sBAAA,CAAAC,MAAA;MACA,IAAAK,QAAA,QAAAN,sBAAA,CAAAC,MAAA;MACA,IAAAyD,eAAA,QAAA1D,sBAAA,CAAAC,MAAA;MACA,IAAA0D,YAAA,QAAAC,qBAAA,CAAAjB,GAAA;MACA,IAAAvH,KAAA,QAAAqI,kBAAA,CAAAd,GAAA;MAEA,IAAAd,IAAA;MACAA,IAAA,0CAAAG,MAAA,MAAAC,WAAA,CAAA5B,cAAA;MACAwB,IAAA,8BAAAG,MAAA,MAAAC,WAAA,CAAA3B,QAAA;MACAuB,IAAA,0CAAAG,MAAA,MAAAC,WAAA,CAAAyB,eAAA;MACA7B,IAAA,gDAAAG,MAAA,MAAAC,WAAA,CAAA0B,YAAA;MACA9B,IAAA,wEAAAG,MAAA,MAAAC,WAAA,CAAA7G,KAAA;MACAyG,IAAA;MAEA,KAAAN,MAAA,CAAAM,IAAA;QACAL,wBAAA;QACAC,iBAAA;MACA;IACA;IAEA;IACAqC,sBAAA,WAAAA,uBAAAnB,GAAA;MACA;MACA,KAAAC,KAAA,CAAApI,iBAAA,CAAAsJ,sBAAA,CAAAnB,GAAA,CAAA3D,EAAA;IACA;IACA;IACA+E,YAAA,WAAAA,aAAA/G,YAAA;MACA,KAAAA,YAAA,GAAAA,YAAA;MACA,KAAAD,eAAA;IACA;IACA;IACAiH,QAAA,WAAAA,SAAA5G,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAD,OAAA;IACA;IACA;IACA8G,kBAAA,WAAAA,mBAAAxG,KAAA;MACA,IAAAqB,IAAA,QAAAhB,eAAA,CAAAuE,IAAA,WAAAvD,IAAA;QAAA,OAAAA,IAAA,CAAArB,KAAA,KAAAA,KAAA;MAAA;MACA,OAAAqB,IAAA,GAAAA,IAAA,CAAAtB,KAAA,GAAAC,KAAA;IACA;IACA;IACAyG,qBAAA,WAAAA,sBAAAzG,KAAA;MAAA,IAAA0G,MAAA;MACA,KAAA1G,KAAA;MAEA;QACA;QACA,IAAA2G,YAAA,GAAAnB,IAAA,CAAAC,KAAA,CAAAzF,KAAA;QACA,IAAA4G,KAAA,CAAAC,OAAA,CAAAF,YAAA;UACA,OAAAA,YAAA,CAAAvF,GAAA,WAAA0F,GAAA;YACA,IAAAzF,IAAA,GAAAqF,MAAA,CAAApG,kBAAA,CAAAsE,IAAA,WAAAvD,IAAA;cAAA,OAAAA,IAAA,CAAArB,KAAA,KAAA8G,GAAA;YAAA;YACA,OAAAzF,IAAA,GAAAA,IAAA,CAAAtB,KAAA,GAAA+G,GAAA;UACA,GAAAC,IAAA;QACA;MACA,SAAAC,OAAA;QACA;QACA,IAAA3F,IAAA,QAAAf,kBAAA,CAAAsE,IAAA,WAAAvD,IAAA;UAAA,OAAAA,IAAA,CAAArB,KAAA,KAAAA,KAAA;QAAA;QACA,OAAAqB,IAAA,GAAAA,IAAA,CAAAtB,KAAA,GAAAC,KAAA;MACA;;MAEA;MACA,OAAAA,KAAA;IACA;IACA;IACAiH,iBAAA,WAAAA,kBAAAjH,KAAA;MACA,IAAAkH,aAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,aAAA,CAAAlH,KAAA,KAAAA,KAAA;IACA;IACA;IACAmH,gBAAA,WAAAA,iBAAAnH,KAAA;MACA;MACA,IAAAoH,UAAA,QAAAtH,aAAA,CAAA8E,IAAA,WAAAvD,IAAA;QAAA,OAAAA,IAAA,CAAArB,KAAA,IAAAA,KAAA;MAAA;MACA,OAAAoH,UAAA,GAAAA,UAAA,CAAArH,KAAA,GAAAC,KAAA;IACA;IACA;IACAqH,cAAA,WAAAA,eAAAnC,GAAA;MACA,KAAArF,cAAA,GAAAqF,GAAA;MACA,KAAAtF,iBAAA;IACA;IACA;IACA4E,WAAA,WAAAA,YAAAF,MAAA;MACA,IAAAA,MAAA,aAAAA,MAAA,KAAAgD,SAAA,IAAAhD,MAAA;QACA;MACA;MACA,OAAA7C,MAAA,CAAA6C,MAAA,EAAAiD,cAAA;QACAC,qBAAA;QACAC,qBAAA;MACA;IACA;IACA;IACAtB,qBAAA,WAAAA,sBAAAjB,GAAA;MACA,IAAAwC,eAAA,GAAAjG,MAAA,CAAAyD,GAAA,CAAAyC,IAAA;MACA,IAAAC,UAAA,GAAAnG,MAAA,CAAAyD,GAAA,CAAA2C,IAAA;MACA,IAAA3B,YAAA,GAAAwB,eAAA,GAAAE,UAAA;MACA,OAAA1B,YAAA,OAAAA,YAAA;IACA;IAEA;IACA4B,uBAAA,WAAAA,wBAAA5C,GAAA;MAAA,IAAA6C,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAArH,QAAA,EAAAsH,uBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,cAAA,EAAApE,IAAA,EAAAqE,iBAAA,EAAAC,EAAA;QAAA,WAAAR,aAAA,CAAAD,OAAA,IAAAU,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAD,QAAA,CAAAE,CAAA;cAAA,OAGA,IAAAC,iCAAA,EAAA7D,GAAA,CAAAa,IAAA;YAAA;cAAAhF,QAAA,GAAA6H,QAAA,CAAAI,CAAA;cAEA,IAAAjI,QAAA,CAAAiB,IAAA,YAAAjB,QAAA,CAAA1D,IAAA;gBACA;gBACAgL,uBAAA;gBAEAtH,QAAA,CAAA1D,IAAA,CAAAgH,OAAA,WAAA4E,QAAA;kBACA;kBACA,IAAAlB,MAAA,CAAAmB,mBAAA,CAAAD,QAAA;oBACA,IAAArF,OAAA,GAAAqF,QAAA,CAAAE,kBAAA;oBACA,IAAAvF,OAAA;sBACA;wBACA;wBACA,IAAA+C,YAAA,GAAAnB,IAAA,CAAAC,KAAA,CAAA7B,OAAA;wBACA,IAAAgD,KAAA,CAAAC,OAAA,CAAAF,YAAA;0BACA0B,uBAAA,CAAAe,IAAA,CAAAC,KAAA,CAAAhB,uBAAA,MAAAiB,mBAAA,CAAArB,OAAA,EAAAtB,YAAA;wBACA;0BACA0B,uBAAA,CAAAe,IAAA,CAAAxF,OAAA;wBACA;sBACA,SAAA2F,QAAA;wBACA;wBACAlB,uBAAA,CAAAe,IAAA,CAAAxF,OAAA;sBACA;oBACA;kBACA;gBACA;;gBAEA;gBACA0E,kBAAA,OAAAgB,mBAAA,CAAArB,OAAA,MAAAuB,GAAA,CAAAnB,uBAAA,IAEA;gBACAE,sBAAA,GAAAR,MAAA,CAAAzH,kBAAA;gBACAkI,cAAA,GAAAD,sBAAA,CAAA7G,MAAA,WAAAL,IAAA;kBAAA,OACA,CAAAiH,kBAAA,CAAAmB,QAAA,CAAApI,IAAA,CAAArB,KAAA;gBAAA,CACA,GAEA;gBACAoE,IAAA;gBACAqE,iBAAA;gBAEA,IAAAD,cAAA,CAAA5G,MAAA;kBACAwC,IAAA;kBAEAoE,cAAA,CAAAnE,OAAA,WAAAT,OAAA;oBACA,IAAAU,MAAA;oBACA;oBACA,QAAAV,OAAA,CAAA5D,KAAA;sBACA;wBAAA;wBACAsE,MAAA,GAAA7C,MAAA,CAAAyD,GAAA,CAAAwE,SAAA;wBACA;sBACA;wBAAA;wBACApF,MAAA,GAAA7C,MAAA,CAAAyD,GAAA,CAAAyE,SAAA;wBACA;sBACA;wBAAA;wBACArF,MAAA,GAAA7C,MAAA,CAAAyD,GAAA,CAAA0E,SAAA;wBACA;sBACA;wBAAA;wBACAtF,MAAA,GAAA7C,MAAA,CAAAyD,GAAA,CAAA2E,QAAA;wBACA;oBACA;;oBAEA;oBACAzF,IAAA,UAAAG,MAAA,CAAAX,OAAA,CAAA7D,KAAA,cAAAwE,MAAA,CAAAwD,MAAA,CAAAvD,WAAA,CAAAF,MAAA;oBACAmE,iBAAA,IAAAnE,MAAA;kBACA;kBAEAF,IAAA,wEAAAG,MAAA,CAAAwD,MAAA,CAAAvD,WAAA,CAAAiE,iBAAA;gBACA;kBACArE,IAAA;gBACA;gBAEAA,IAAA;gBAEA2D,MAAA,CAAAjE,MAAA,CAAAM,IAAA;kBACAL,wBAAA;kBACAC,iBAAA;gBACA;cACA;gBACA+D,MAAA,CAAA+B,QAAA,CAAA7H,KAAA;cACA;cAAA2G,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEAjB,MAAA,CAAA+B,QAAA,CAAA7H,KAAA;YAAA;cAAA,OAAA2G,QAAA,CAAAmB,CAAA;UAAA;QAAA,GAAA3B,OAAA;MAAA;IAEA;IAEA;IACAc,mBAAA,WAAAA,oBAAAc,QAAA;MACA;MACA,IAAAC,iBAAA,GAAAD,QAAA,CAAAE,cAAA,YAAAF,QAAA,CAAAE,cAAA;MACA,IAAAC,kBAAA,GAAAH,QAAA,CAAAI,eAAA,YAAAJ,QAAA,CAAAI,eAAA;MACA,IAAAC,qBAAA,GAAAL,QAAA,CAAAb,kBAAA,YAAAa,QAAA,CAAAb,kBAAA;MACA,IAAAmB,qBAAA,GAAAN,QAAA,CAAAO,kBAAA,YAAAP,QAAA,CAAAO,kBAAA;MAEA,OAAAN,iBAAA,IAAAE,kBAAA,IAAAE,qBAAA,IAAAC,qBAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}