(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0ccba6"],{"4eba":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_order_examine:car_order_examine:add"],expression:"['car_order_examine:car_order_examine:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_order_examine:car_order_examine:edit"],expression:"['car_order_examine:car_order_examine:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_order_examine:car_order_examine:remove"],expression:"['car_order_examine:car_order_examine:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_order_examine:car_order_examine:export"],expression:"['car_order_examine:car_order_examine:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.car_order_examineList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:"${comment}",align:"center",prop:"id"}}),r("el-table-column",{attrs:{label:"佣金",align:"center",prop:"transportationFee"}}),r("el-table-column",{attrs:{label:"拖车费",align:"center",prop:"towingFee"}}),r("el-table-column",{attrs:{label:"贴机费",align:"center",prop:"trackerInstallationFee"}}),r("el-table-column",{attrs:{label:"其他报销",align:"center",prop:"otherReimbursement"}}),r("el-table-column",{attrs:{label:"合计费用",align:"center",prop:"totalCost"}}),r("el-table-column",{attrs:{label:"审批状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-tag",{attrs:{type:e.getStatusTagType(t.row.status)}},[e._v(" "+e._s(e.getStatusText(t.row.status))+" ")])]}}])}),r("el-table-column",{attrs:{label:"审批时间",align:"center",prop:"examineTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.examineTime,"{y}-{m}-{d}")))])]}}])}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.isFinalStatus(t.row.status)?e._e():r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_order_examine:car_order_examine:edit"],expression:"['car_order_examine:car_order_examine:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改")]),e.isFinalStatus(t.row.status)?e._e():r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_order_examine:car_order_examine:remove"],expression:"['car_order_examine:car_order_examine:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_order_examine:car_order_examine:approve"],expression:"['car_order_examine:car_order_examine:approve']"}],attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(r){return e.handleViewDetails(t.row)}}},[e._v("审批")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}}),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),r("el-dialog",{attrs:{title:"审批拒绝",visible:e.rejectDialogVisible,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.rejectDialogVisible=t}}},[r("el-form",{ref:"rejectForm",attrs:{model:e.rejectForm,rules:e.rejectRules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"拒绝原因",prop:"rejectReason"}},[r("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入拒绝原因"},model:{value:e.rejectForm.rejectReason,callback:function(t){e.$set(e.rejectForm,"rejectReason",t)},expression:"rejectForm.rejectReason"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitReject}},[e._v("确 定")]),r("el-button",{on:{click:e.cancelReject}},[e._v("取 消")])],1)],1),r("el-dialog",{attrs:{title:"找车费用审批",visible:e.detailsDialogVisible,width:"1000px","append-to-body":""},on:{"update:visible":function(t){e.detailsDialogVisible=t}}},[e.currentOrderInfo?r("el-descriptions",{staticStyle:{"margin-bottom":"20px"},attrs:{column:3,border:""}},[r("el-descriptions-item",{attrs:{label:"订单号"}},[e._v(e._s(e.currentOrderInfo.orderId))]),r("el-descriptions-item",{attrs:{label:"客户姓名"}},[e._v(e._s(e.currentOrderInfo.customerName))]),r("el-descriptions-item",{attrs:{label:"手机号"}},[e._v(e._s(e.currentOrderInfo.mobilePhone))]),r("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.currentOrderInfo.plateNo))]),r("el-descriptions-item",{attrs:{label:"找车团队"}},[e._v(e._s(e.currentOrderInfo.teamName))]),r("el-descriptions-item",{attrs:{label:"机构名称"}},[e._v(e._s(e.currentOrderInfo.jgName))])],1):e._e(),r("div",{staticStyle:{"margin-bottom":"20px"}},[r("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center","margin-bottom":"10px"}},[r("h4",[e._v("费用记录")]),r("div",[r("el-button",{attrs:{type:"success",size:"small",disabled:0===e.selectedRecords.length},on:{click:function(t){return e.handleBatchApprove("approve")}}},[e._v("批量通过")]),r("el-button",{attrs:{type:"danger",size:"small",disabled:0===e.selectedRecords.length},on:{click:function(t){return e.handleBatchApprove("reject")}}},[e._v("批量拒绝")])],1)]),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.recordsLoading,expression:"recordsLoading"}],attrs:{data:e.feeRecords,border:""},on:{"selection-change":e.handleRecordSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:"提交时间",align:"center",prop:"createTime",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}"))+" ")]}}])}),r("el-table-column",{attrs:{label:"提交人",align:"center",prop:"createBy",width:"100"}}),r("el-table-column",{attrs:{label:"佣金",align:"center",prop:"transportationFee",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ￥"+e._s(t.row.transportationFee||0)+" ")]}}])}),r("el-table-column",{attrs:{label:"拖车费",align:"center",prop:"towingFee",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ￥"+e._s(t.row.towingFee||0)+" ")]}}])}),r("el-table-column",{attrs:{label:"贴机费",align:"center",prop:"trackerInstallationFee",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ￥"+e._s(t.row.trackerInstallationFee||0)+" ")]}}])}),r("el-table-column",{attrs:{label:"其他报销",align:"center",prop:"otherReimbursement",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ￥"+e._s(t.row.otherReimbursement||0)+" ")]}}])}),r("el-table-column",{attrs:{label:"合计费用",align:"center",prop:"totalMoney",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ￥"+e._s(t.row.totalMoney||0)+" ")]}}])}),r("el-table-column",{attrs:{label:"审批状态",align:"center",prop:"status",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-tag",{attrs:{type:e.getStatusTagType(t.row.status)}},[e._v(" "+e._s(e.getStatusText(t.row.status))+" ")])]}}])}),r("el-table-column",{attrs:{label:"审批时间",align:"center",prop:"approveTime",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.approveTime?e.parseTime(t.row.approveTime,"{y}-{m}-{d} {h}:{i}"):"-")+" ")]}}])}),r("el-table-column",{attrs:{label:"审批人",align:"center",prop:"approveBy",width:"100"}}),r("el-table-column",{attrs:{label:"拒绝原因",align:"center",prop:"reasons",width:"150","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{label:"操作",align:"center",width:"150",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[0===t.row.status?r("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(r){return e.handleSingleApprove(t.row,"approve")}}},[e._v("通过")]):e._e(),0===t.row.status?r("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(r){return e.handleSingleApprove(t.row,"reject")}}},[e._v("拒绝")]):e._e()]}}])})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.detailsDialogVisible=!1}}},[e._v("关 闭")])],1)],1),r("el-dialog",{attrs:{title:"审批确认",visible:e.singleApprovalDialogVisible,width:"400px","append-to-body":""},on:{"update:visible":function(t){e.singleApprovalDialogVisible=t}}},[r("el-form",{ref:"singleApprovalForm",attrs:{model:e.singleApprovalForm,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"审批结果"}},[r("el-tag",{attrs:{type:"approve"===e.singleApprovalForm.action?"success":"danger"}},[e._v(" "+e._s("approve"===e.singleApprovalForm.action?"通过":"拒绝")+" ")])],1),"reject"===e.singleApprovalForm.action?r("el-form-item",{attrs:{label:"拒绝原因",prop:"rejectReason",rules:[{required:!0,message:"请输入拒绝原因",trigger:"blur"}]}},[r("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入拒绝原因"},model:{value:e.singleApprovalForm.rejectReason,callback:function(t){e.$set(e.singleApprovalForm,"rejectReason",t)},expression:"singleApprovalForm.rejectReason"}})],1):e._e()],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.confirmSingleApproval}},[e._v("确 定")]),r("el-button",{on:{click:function(t){e.singleApprovalDialogVisible=!1}}},[e._v("取 消")])],1)],1),r("el-dialog",{attrs:{title:"批量审批确认",visible:e.batchApprovalDialogVisible,width:"400px","append-to-body":""},on:{"update:visible":function(t){e.batchApprovalDialogVisible=t}}},[r("el-form",{ref:"batchApprovalForm",attrs:{model:e.batchApprovalForm,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"审批结果"}},[r("el-tag",{attrs:{type:"approve"===e.batchApprovalForm.action?"success":"danger"}},[e._v(" "+e._s("approve"===e.batchApprovalForm.action?"批量通过":"批量拒绝")+" ")])],1),r("el-form-item",{attrs:{label:"选中记录"}},[r("span",[e._v(e._s(e.selectedRecords.length)+" 条记录")])]),"reject"===e.batchApprovalForm.action?r("el-form-item",{attrs:{label:"拒绝原因",prop:"rejectReason",rules:[{required:!0,message:"请输入拒绝原因",trigger:"blur"}]}},[r("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入拒绝原因"},model:{value:e.batchApprovalForm.rejectReason,callback:function(t){e.$set(e.batchApprovalForm,"rejectReason",t)},expression:"batchApprovalForm.rejectReason"}})],1):e._e()],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.confirmBatchApproval}},[e._v("确 定")]),r("el-button",{on:{click:function(t){e.batchApprovalDialogVisible=!1}}},[e._v("取 消")])],1)],1)],1)},o=[],n=r("5530"),i=(r("d81d"),r("d3b7"),r("0643"),r("a573"),r("b775"));function l(e){return Object(i["a"])({url:"/car_order_examine/car_order_examine/list",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/car_order_examine/car_order_examine/"+e,method:"get"})}function c(e){return Object(i["a"])({url:"/car_order_examine/car_order_examine",method:"post",data:e})}function u(e){return Object(i["a"])({url:"/car_order_examine/car_order_examine",method:"put",data:e})}function p(e){return Object(i["a"])({url:"/car_order_examine/car_order_examine/"+e,method:"delete"})}function d(e){return Object(i["a"])({url:"/car_order_examine/car_order_examine/records/"+e,method:"get"})}function m(e){return Object(i["a"])({url:"/car_order_examine/car_order_examine/batchApprove",method:"post",data:e})}var h=r("d4ec"),v=r("bee2"),f=r("ade3"),g=(r("d9e2"),r("caad"),{PENDING:0,APPROVED:1,REJECTED:2,LEGAL_SUPERVISOR:3,DIRECTOR:4,FINANCE_SUPERVISOR:5,GENERAL_MANAGER:6}),b=Object(f["a"])(Object(f["a"])(Object(f["a"])(Object(f["a"])(Object(f["a"])(Object(f["a"])(Object(f["a"])({},g.PENDING,"未审批"),g.APPROVED,"全部同意"),g.REJECTED,"已拒绝"),g.LEGAL_SUPERVISOR,"贷后主管审批"),g.DIRECTOR,"总监审批"),g.FINANCE_SUPERVISOR,"财务主管/总监抄送"),g.GENERAL_MANAGER,"总经理/董事长审批"),_=[g.LEGAL_SUPERVISOR,g.DIRECTOR,g.FINANCE_SUPERVISOR,g.GENERAL_MANAGER],x=function(){function e(){Object(h["a"])(this,e)}return Object(v["a"])(e,null,[{key:"getNextStatus",value:function(e){var t=_.indexOf(e);return-1===t||t===_.length-1?null:_[t+1]}},{key:"isLastApprovalNode",value:function(e){return e===g.GENERAL_MANAGER}},{key:"isFinalStatus",value:function(e){return e===g.APPROVED||e===g.REJECTED}},{key:"canApprove",value:function(e){return _.includes(e)}},{key:"handleApprove",value:function(e){if(!this.canApprove(e))throw new Error("当前状态不允许审批操作");if(this.isLastApprovalNode(e))return g.APPROVED;var t=this.getNextStatus(e);if(null===t)throw new Error("无法获取下一个审批状态");return t}},{key:"handleReject",value:function(e){if(!this.canApprove(e))throw new Error("当前状态不允许审批操作");return g.REJECTED}},{key:"startApprovalFlow",value:function(){return _[0]}},{key:"getStatusText",value:function(e){return b[e]||"未知状态"}},{key:"getStatusTagType",value:function(e){switch(e){case g.PENDING:return"info";case g.APPROVED:return"success";case g.REJECTED:return"danger";case g.LEGAL_SUPERVISOR:case g.DIRECTOR:case g.FINANCE_SUPERVISOR:case g.GENERAL_MANAGER:return"warning";default:return"info"}}},{key:"isValidStatusTransition",value:function(e,t){return!this.isFinalStatus(e)&&(t===g.REJECTED?this.canApprove(e):this.isLastApprovalNode(e)?t===g.APPROVED:t===this.getNextStatus(e))}}])}(),w=x,A={name:"Car_order_examine",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,car_order_examineList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,status:null},form:{},rules:{},rejectDialogVisible:!1,rejectForm:{id:null,rejectReason:""},rejectRules:{rejectReason:[{required:!0,message:"拒绝原因不能为空",trigger:"blur"}]},detailsDialogVisible:!1,currentOrderInfo:null,feeRecords:[],recordsLoading:!1,selectedRecords:[],singleApprovalDialogVisible:!1,singleApprovalForm:{id:"",action:"",rejectReason:""},batchApprovalDialogVisible:!1,batchApprovalForm:{action:"",rejectReason:""}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,l(this.queryParams).then((function(t){e.car_order_examineList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,createBy:null,createDate:null,updateBy:null,updateDate:null,transportationFee:null,towingFee:null,trackerInstallationFee:null,otherReimbursement:null,totalCost:null,status:null,examineTime:null,rejectReason:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加找车费用审批"},handleUpdate:function(e){var t=this;this.reset();var r=e.id||this.ids;s(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改找车费用审批"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?u(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):c(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.id||this.ids;this.$modal.confirm('是否确认删除找车费用审批编号为"'+r+'"的数据项？').then((function(){return p(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("car_order_examine/car_order_examine/export",Object(n["a"])({},this.queryParams),"car_order_examine_".concat((new Date).getTime(),".xlsx"))},getStatusText:function(e){return w.getStatusText(e)},getStatusTagType:function(e){return w.getStatusTagType(e)},canApprove:function(e){return w.canApprove(e)},isFinalStatus:function(e){return w.isFinalStatus(e)},handleViewDetails:function(e){this.currentOrderInfo=e,this.detailsDialogVisible=!0,this.loadFeeRecords(e.orderId)},loadFeeRecords:function(e){var t=this;this.recordsLoading=!0,d(e).then((function(e){t.feeRecords=e.data||[],t.recordsLoading=!1})).catch((function(){t.recordsLoading=!1,t.$modal.msgError("加载费用记录失败")}))},handleRecordSelectionChange:function(e){this.selectedRecords=e},handleSingleApprove:function(e,t){this.singleApprovalForm.id=e.id,this.singleApprovalForm.action=t,this.singleApprovalForm.rejectReason="",this.singleApprovalDialogVisible=!0},confirmSingleApproval:function(){var e=this;"reject"===this.singleApprovalForm.action?this.$refs["singleApprovalForm"].validate((function(t){t&&e.executeSingleApproval()})):this.executeSingleApproval()},executeSingleApproval:function(){this.singleApprovalForm.id,this.singleApprovalForm.action,this.singleApprovalForm.rejectReason;this.$modal.msgSuccess("".concat("approve"===this.singleApprovalForm.action?"通过":"拒绝","审批成功")),this.singleApprovalDialogVisible=!1,this.loadFeeRecords(this.currentOrderInfo.orderId),this.getList()},handleBatchApprove:function(e){0!==this.selectedRecords.length?(this.batchApprovalForm.action=e,this.batchApprovalForm.rejectReason="",this.batchApprovalDialogVisible=!0):this.$modal.msgError("请选择要审批的记录")},confirmBatchApproval:function(){var e=this;"reject"===this.batchApprovalForm.action?this.$refs["batchApprovalForm"].validate((function(t){t&&e.executeBatchApproval()})):this.executeBatchApproval()},executeBatchApproval:function(){var e=this,t={ids:this.selectedRecords.map((function(e){return e.id})),action:this.batchApprovalForm.action,rejectReason:this.batchApprovalForm.rejectReason};m(t).then((function(){e.$modal.msgSuccess("批量".concat("approve"===e.batchApprovalForm.action?"通过":"拒绝","审批成功")),e.batchApprovalDialogVisible=!1,e.loadFeeRecords(e.currentOrderInfo.orderId),e.getList()})).catch((function(){e.$modal.msgError("批量审批失败")}))}}},y=A,R=r("2877"),S=Object(R["a"])(y,a,o,!1,null,null,null);t["default"]=S.exports}}]);