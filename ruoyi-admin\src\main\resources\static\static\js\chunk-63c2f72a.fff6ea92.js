(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-63c2f72a"],{1791:function(e,t,a){},"2eca":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[e._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},r=[],o=a("bd52"),n={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(o["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},i=n,s=(a("d6fd"),a("2877")),c=Object(s["a"])(i,l,r,!1,null,"8a3d4978",null);t["a"]=c.exports},"325c":function(e,t,a){"use strict";a("bff0")},51940:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"certId"}},[a("el-input",{attrs:{placeholder:"贷款人身份证号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.certId,callback:function(t){e.$set(e.queryParams,"certId",t)},expression:"queryParams.certId"}})],1),a("el-form-item",{attrs:{label:"",prop:"jgName"}},[a("el-input",{attrs:{placeholder:"出单渠道",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jgName,callback:function(t){e.$set(e.queryParams,"jgName",t)},expression:"queryParams.jgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"lendingBank"}},[a("el-input",{attrs:{placeholder:"放款银行",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.lendingBank,callback:function(t){e.$set(e.queryParams,"lendingBank",t)},expression:"queryParams.lendingBank"}})],1),a("el-form-item",{attrs:{label:"",prop:"litigationStatus"}},[a("el-cascader",{attrs:{options:e.litigationStatusOptions,props:{expandTrigger:"hover",value:"value",label:"label",children:"children"},placeholder:"法诉状态",clearable:""},on:{change:e.handleQuery},model:{value:e.queryParams.litigationStatus,callback:function(t){e.$set(e.queryParams,"litigationStatus",t)},expression:"queryParams.litigationStatus"}})],1),a("el-form-item",{attrs:{label:"",prop:"applicationBy"}},[a("el-input",{attrs:{placeholder:"申请人",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.applicationBy,callback:function(t){e.$set(e.queryParams,"applicationBy",t)},expression:"queryParams.applicationBy"}})],1),a("el-form-item",{attrs:{label:"",prop:"costCategory"}},[a("el-select",{attrs:{placeholder:"费用类型",clearable:""},on:{change:e.handleQuery},model:{value:e.queryParams.costCategory,callback:function(t){e.$set(e.queryParams,"costCategory",t)},expression:"queryParams.costCategory"}},[a("el-option",{attrs:{label:"律师费",value:"律师费"}}),a("el-option",{attrs:{label:"诉讼费",value:"诉讼费"}}),a("el-option",{attrs:{label:"保全费",value:"保全费"}}),a("el-option",{attrs:{label:"执行费",value:"执行费"}}),a("el-option",{attrs:{label:"其他费用",value:"其他费用"}})],1)],1),a("el-form-item",{attrs:{label:"",prop:"approvalStatus"}},[a("el-select",{attrs:{placeholder:"审批状态",clearable:""},on:{change:e.handleQuery},model:{value:e.queryParams.approvalStatus,callback:function(t){e.$set(e.queryParams,"approvalStatus",t)},expression:"queryParams.approvalStatus"}},[a("el-option",{attrs:{label:"未审批",value:"0"}}),a("el-option",{attrs:{label:"全部同意",value:"1"}}),a("el-option",{attrs:{label:"已拒绝",value:"2"}}),a("el-option",{attrs:{label:"法诉主管审批",value:"3"}}),a("el-option",{attrs:{label:"总监审批",value:"4"}}),a("el-option",{attrs:{label:"财务主管/总监抄送",value:"5"}}),a("el-option",{attrs:{label:"总经理/董事长审批",value:"6"}})],1)],1),a("el-form-item",{attrs:{label:"",prop:"dateRange"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"申请开始日期","end-placeholder":"申请结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},on:{change:e.handleDateRangeChange},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),a("el-form-item",{attrs:{label:"",prop:"approvalDateRange"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"审批开始日期","end-placeholder":"审批结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},on:{change:e.handleApprovalDateRangeChange},model:{value:e.approvalDateRange,callback:function(t){e.approvalDateRange=t},expression:"approvalDateRange"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["litigation_cost_approval:litigation_cost_approval:add"],expression:"['litigation_cost_approval:litigation_cost_approval:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["litigation_cost_approval:litigation_cost_approval:edit"],expression:"['litigation_cost_approval:litigation_cost_approval:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleBatchEdit}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["litigation_cost_approval:litigation_cost_approval:remove"],expression:"['litigation_cost_approval:litigation_cost_approval:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["litigation_cost_approval:litigation_cost_approval:export"],expression:"['litigation_cost_approval:litigation_cost_approval:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.litigationCostApprovalList,"row-key":"id",flex:"right"},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"60",fixed:""}}),a("el-table-column",{attrs:{label:"申请人",align:"center",prop:"applicationBy",width:"100"}}),a("el-table-column",{attrs:{label:"最新申请时间",align:"center",prop:"applicationTime",width:"150"}}),a("el-table-column",{attrs:{label:"案件负责人",align:"center",prop:"curator",width:"100"}}),a("el-table-column",{attrs:{label:"提交次数",align:"center",prop:"submissionCount",width:"100"}}),a("el-table-column",{attrs:{label:"法诉状态",align:"center",prop:"litigationStatus",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s("1"==t.row.litigationStatus?"待立案":"已邮寄"))])]}}])}),a("el-table-column",{attrs:{label:"贷款人",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{color:"#409EFF"},attrs:{type:"text"},on:{click:function(a){return e.openUserInfo(t.row)}}},[e._v(" "+e._s(t.row.customerName)+" ")])]}}])}),a("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"jgName"}}),a("el-table-column",{attrs:{label:"地区",align:"center",prop:"area",width:"80"}}),a("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"lendingBank"}}),a("el-table-column",{attrs:{label:"法院地",align:"center",prop:"courtLocation"}}),a("el-table-column",{attrs:{label:"诉讼法院",align:"center",prop:"commonPleas"}}),a("el-table-column",{attrs:{label:"律师费",align:"center",prop:"lawyerFee",width:"80"}}),a("el-table-column",{attrs:{label:"诉讼费",align:"center",prop:"litigationFee",width:"80"}}),a("el-table-column",{attrs:{label:"保险费",align:"center",prop:"insurance",width:"80"}}),a("el-table-column",{attrs:{label:"保全费",align:"center",prop:"preservationFee",width:"80"}}),a("el-table-column",{attrs:{label:"布控费",align:"center",prop:"surveillanceFee",width:"80"}}),a("el-table-column",{attrs:{label:"公告费",align:"center",prop:"announcementFee",width:"80"}}),a("el-table-column",{attrs:{label:"评估费",align:"center",prop:"appraisalFee",width:"80"}}),a("el-table-column",{attrs:{label:"执行费",align:"center",prop:"executionFee",width:"80"}}),a("el-table-column",{attrs:{label:"特殊通道费",align:"center",prop:"specialChannelFees",width:"100"}}),a("el-table-column",{attrs:{label:"日常报销",align:"center",prop:"otherAmountsOwed",width:"80"}}),a("el-table-column",{attrs:{label:"总费用",align:"center",prop:"totalMoney",width:"100"}}),a("el-table-column",{attrs:{label:"整体审批状态",align:"center",prop:"overallApprovalStatus",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return["NOT_STARTED"==t.row.overallApprovalStatus?a("el-tag",{attrs:{type:"info"}},[e._v("未开始审批")]):"PARTIAL"==t.row.overallApprovalStatus?a("el-tag",{attrs:{type:"warning"}},[e._v("部分审批")]):"COMPLETED"==t.row.overallApprovalStatus?a("el-tag",{attrs:{type:"success"}},[e._v("全部完成")]):a("el-tag",{attrs:{type:"info"}},[e._v(e._s(t.row.overallApprovalStatus||"未知状态"))])]}}])}),a("el-table-column",{attrs:{label:"审批时间",align:"center",prop:"approveTime",width:"150"}}),a("el-table-column",{attrs:{label:"审批人角色",align:"center",prop:"approveRole"}}),a("el-table-column",{attrs:{label:"审批人",align:"center",prop:"approveBy"}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"100","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["litigation_cost_approval:litigation_cost_approval:approve"],expression:"['litigation_cost_approval:litigation_cost_approval:approve']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("审批")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:"法诉费用审批详情",visible:e.open,width:"1200px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("div",{staticClass:"approval-header"},[a("el-row",[a("el-col",{attrs:{span:8}},[a("strong",[e._v("贷款人：")]),e._v(e._s(e.currentRecord.customerName)+" ")]),a("el-col",{attrs:{span:8}},[a("strong",[e._v("案件负责人：")]),e._v(e._s(e.currentRecord.curator)+" ")]),a("el-col",{attrs:{span:8}},[a("strong",[e._v("法院地：")]),e._v(e._s(e.currentRecord.courtLocation)+" ")])],1)],1),a("div",{staticClass:"batch-approval-section",staticStyle:{margin:"20px 0"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["litigation_cost_approval:litigation_cost_approval:approve"],expression:"['litigation_cost_approval:litigation_cost_approval:approve']"}],attrs:{type:"success",size:"small",disabled:0===e.selectedRecords.length},on:{click:function(t){return e.handleBatchApprove("approve")}}},[e._v(" 批量通过 ("+e._s(e.selectedRecords.length)+") ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["litigation_cost_approval:litigation_cost_approval:approve"],expression:"['litigation_cost_approval:litigation_cost_approval:approve']"}],attrs:{type:"danger",size:"small",disabled:0===e.selectedRecords.length},on:{click:function(t){return e.handleBatchApprove("reject")}}},[e._v(" 批量拒绝 ("+e._s(e.selectedRecords.length)+") ")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.recordsLoading,expression:"recordsLoading"}],attrs:{data:e.submissionRecords},on:{"selection-change":e.handleRecordSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center",fixed:"left"}}),a("el-table-column",{attrs:{label:"提交时间",align:"center",prop:"applicationTime",width:"150"}}),a("el-table-column",{attrs:{label:"提交人",align:"center",prop:"applicationBy",width:"100"}}),a("el-table-column",{attrs:{label:"律师费",align:"center",prop:"lawyerFee",width:"80"}}),a("el-table-column",{attrs:{label:"诉讼费",align:"center",prop:"litigationFee",width:"80"}}),a("el-table-column",{attrs:{label:"保全费",align:"center",prop:"preservationFee",width:"80"}}),a("el-table-column",{attrs:{label:"布控费",align:"center",prop:"surveillanceFee",width:"80"}}),a("el-table-column",{attrs:{label:"公告费",align:"center",prop:"announcementFee",width:"80"}}),a("el-table-column",{attrs:{label:"评估费",align:"center",prop:"appraisalFee",width:"80"}}),a("el-table-column",{attrs:{label:"执行费",align:"center",prop:"executionFee",width:"80"}}),a("el-table-column",{attrs:{label:"违约金",align:"center",prop:"penalty",width:"80"}}),a("el-table-column",{attrs:{label:"担保费",align:"center",prop:"guaranteeFee",width:"80"}}),a("el-table-column",{attrs:{label:"居间费",align:"center",prop:"intermediaryFee",width:"80"}}),a("el-table-column",{attrs:{label:"代偿金",align:"center",prop:"compensity",width:"80"}}),a("el-table-column",{attrs:{label:"判决金额",align:"center",prop:"judgmentAmount",width:"100"}}),a("el-table-column",{attrs:{label:"利息",align:"center",prop:"interest",width:"80"}}),a("el-table-column",{attrs:{label:"其他欠款",align:"center",prop:"otherAmountsOwed",width:"100"}}),a("el-table-column",{attrs:{label:"保险费",align:"center",prop:"insurance",width:"80"}}),a("el-table-column",{attrs:{label:"总费用",align:"center",prop:"totalMoney",width:"100"}}),a("el-table-column",{attrs:{label:"审批状态",align:"center",prop:"approvalStatus",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return["0"==t.row.approvalStatus||null==t.row.approvalStatus||""==t.row.approvalStatus?a("el-tag",{attrs:{type:"info"}},[e._v("未审批")]):"1"==t.row.approvalStatus?a("el-tag",{attrs:{type:"success"}},[e._v("全部同意")]):"2"==t.row.approvalStatus?a("el-tag",{attrs:{type:"danger"}},[e._v("已拒绝")]):"3"==t.row.approvalStatus?a("el-tag",{attrs:{type:"warning"}},[e._v("法诉主管审批")]):"4"==t.row.approvalStatus?a("el-tag",{attrs:{type:"warning"}},[e._v("总监审批")]):"5"==t.row.approvalStatus?a("el-tag",{attrs:{type:"warning"}},[e._v("财务主管/总监抄送")]):"6"==t.row.approvalStatus?a("el-tag",{attrs:{type:"warning"}},[e._v("总经理/董事长审批")]):a("el-tag",{attrs:{type:"warning"}},[e._v(e._s(e.getStatusText(t.row.approvalStatus)))])]}}])}),a("el-table-column",{attrs:{label:"审批时间",align:"center",prop:"approveTime",width:"150"}}),a("el-table-column",{attrs:{label:"审批人",align:"center",prop:"approveBy",width:"100"}}),a("el-table-column",{attrs:{label:"拒绝原因",align:"center",prop:"reasons",width:"150"}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"150",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.canApproveRecord(t.row)?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["litigation_cost_approval:litigation_cost_approval:approve"],expression:"['litigation_cost_approval:litigation_cost_approval:approve']"}],attrs:{size:"mini",type:"success"},on:{click:function(a){return e.handleSingleApprove(t.row,"approve")}}},[e._v(" 通过 ")]):e._e(),e.canApproveRecord(t.row)?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["litigation_cost_approval:litigation_cost_approval:approve"],expression:"['litigation_cost_approval:litigation_cost_approval:approve']"}],attrs:{size:"mini",type:"danger"},on:{click:function(a){return e.handleSingleApprove(t.row,"reject")}}},[e._v(" 拒绝 ")]):a("div",["1"==t.row.approvalStatus?a("el-tag",{attrs:{type:"success",size:"mini"}},[e._v("全部同意")]):"2"==t.row.approvalStatus?a("el-tag",{attrs:{type:"danger",size:"mini"}},[e._v("已拒绝")]):["0","3","4","5","6"].includes(t.row.approvalStatus)||null==t.row.approvalStatus||""==t.row.approvalStatus?a("div",[a("el-tag",{attrs:{type:"info",size:"mini"}},[e._v(e._s(e.getStatusText(t.row.approvalStatus)))]),a("div",{staticStyle:{"font-size":"11px",color:"#999","margin-top":"2px"}},[e._v(" 需要："+e._s(e.getRequiredRoleText(t.row.approvalStatus))+" ")])],1):a("el-tag",{attrs:{type:"warning",size:"mini"}},[e._v(e._s(e.getStatusText(t.row.approvalStatus)))])],1)]}}])})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.cancel}},[e._v("关 闭")])],1)],1),a("el-dialog",{attrs:{title:"审批确认",visible:e.singleApprovalOpen,width:"400px","append-to-body":""},on:{"update:visible":function(t){e.singleApprovalOpen=t}}},[a("el-form",{ref:"singleApprovalForm",attrs:{model:e.singleApprovalForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"审批结果"}},[a("el-tag",{attrs:{type:"approve"===e.singleApprovalForm.action?"success":"danger"}},[e._v(" "+e._s("approve"===e.singleApprovalForm.action?"通过":"拒绝")+" ")])],1),"reject"===e.singleApprovalForm.action?a("el-form-item",{attrs:{label:"拒绝原因",prop:"rejectReason",rules:[{required:!0,message:"请输入拒绝原因",trigger:"blur"}]}},[a("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入拒绝原因"},model:{value:e.singleApprovalForm.rejectReason,callback:function(t){e.$set(e.singleApprovalForm,"rejectReason",t)},expression:"singleApprovalForm.rejectReason"}})],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.confirmSingleApproval}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.singleApprovalOpen=!1}}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"批量审批确认",visible:e.batchApprovalOpen,width:"400px","append-to-body":""},on:{"update:visible":function(t){e.batchApprovalOpen=t}}},[a("el-form",{ref:"batchApprovalForm",attrs:{model:e.batchApprovalForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"审批结果"}},[a("el-tag",{attrs:{type:"approve"===e.batchApprovalForm.action?"success":"danger"}},[e._v(" "+e._s("approve"===e.batchApprovalForm.action?"批量通过":"批量拒绝")+" ")])],1),a("el-form-item",{attrs:{label:"选中记录"}},[a("span",[e._v(e._s(e.selectedRecords.length)+" 条记录")])]),"reject"===e.batchApprovalForm.action?a("el-form-item",{attrs:{label:"拒绝原因",prop:"rejectReason",rules:[{required:!0,message:"请输入拒绝原因",trigger:"blur"}]}},[a("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入拒绝原因"},model:{value:e.batchApprovalForm.rejectReason,callback:function(t){e.$set(e.batchApprovalForm,"rejectReason",t)},expression:"batchApprovalForm.rejectReason"}})],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.confirmBatchApproval}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.batchApprovalOpen=!1}}},[e._v("取 消")])],1)],1),a("userInfo",{ref:"userInfo",attrs:{visible:e.userInfoVisible,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.userInfoVisible=t}}})],1)},r=[],o=a("5530"),n=(a("99af"),a("4de4"),a("caad"),a("d81d"),a("d3b7"),a("2532"),a("0643"),a("2382"),a("a573"),a("9a9a"),a("b775"));function i(e){return Object(n["a"])({url:"/litigation_cost_approval/litigation_cost_approval/list",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/litigation_cost_approval/litigation_cost_approval/records/"+e,method:"get"})}function c(e){return Object(n["a"])({url:"/litigation_cost_approval/litigation_cost_approval/singleApproveNew",method:"post",data:e})}function p(e){return Object(n["a"])({url:"/litigation_cost_approval/litigation_cost_approval/batchApproveNew",method:"post",data:e})}var u=a("cf0d"),d=a("2eca"),m={name:"Vm_car_order",components:{userInfo:d["a"]},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,litigationCostApprovalList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,customerName:null,certId:null,jgName:null,lendingBank:null,litigationStatus:null,applicationBy:null,costCategory:null,approvalStatus:null,startTime:null,endTime:null,approvalStartTime:null,approvalEndTime:null},dateRange:[],approvalDateRange:[],form:{id:"",status:0,rejectReason:null},currentRecord:{},submissionRecords:[],recordsLoading:!1,selectedRecords:[],singleApprovalOpen:!1,singleApprovalForm:{id:"",action:"",rejectReason:""},batchApprovalOpen:!1,batchApprovalForm:{action:"",rejectReason:""},userInfoVisible:!1,customerInfo:{},litigationStatusOptions:[{value:"起诉",label:"起诉",children:[{value:"起诉-准备材料",label:"准备材料"},{value:"起诉-已提交",label:"已提交"},{value:"起诉-法院受理",label:"法院受理"}]},{value:"审理",label:"审理",children:[{value:"审理-开庭审理",label:"开庭审理"},{value:"审理-等待判决",label:"等待判决"},{value:"审理-一审判决",label:"一审判决"}]},{value:"执行",label:"执行",children:[{value:"执行-申请执行",label:"申请执行"},{value:"执行-执行中",label:"执行中"},{value:"执行-执行完毕",label:"执行完毕"}]},{value:"结案",label:"结案",children:[{value:"结案-胜诉结案",label:"胜诉结案"},{value:"结案-败诉结案",label:"败诉结案"},{value:"结案-和解结案",label:"和解结案"}]}],rules:{keyProvince:"",keyCity:"",keyBorough:"",keyDetailAddress:""},provinceList:u,cityList:[],districtList:[]}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,i(this.queryParams).then((function(t){e.litigationCostApprovalList=t.rows,e.total=t.total,e.loading=!1})).catch((function(t){console.error("查询失败:",t),e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:"",status:0,rejectReason:null},this.currentRecord={},this.submissionRecords=[],this.selectedRecords=[],this.singleApprovalOpen=!1,this.batchApprovalOpen=!1,this.singleApprovalForm={id:"",action:"",rejectReason:""},this.batchApprovalForm={action:"",rejectReason:""},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.approvalDateRange=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加VIEW"},handleUpdate:function(e){this.currentRecord=e,this.loadSubmissionRecords(e.litigationCaseId),this.open=!0},handleBatchEdit:function(){this.$modal.msgError("请选择单条记录进行审批操作")},loadSubmissionRecords:function(e){var t=this;this.recordsLoading=!0,s(e).then((function(e){t.submissionRecords=e.data||[],t.recordsLoading=!1})).catch((function(){t.recordsLoading=!1}))},handleRecordSelectionChange:function(e){this.selectedRecords=e},handleSingleApprove:function(e,t){this.singleApprovalForm.id=e.id,this.singleApprovalForm.action=t,this.singleApprovalForm.rejectReason="",this.singleApprovalOpen=!0},confirmSingleApproval:function(){var e=this;"reject"===this.singleApprovalForm.action?this.$refs["singleApprovalForm"].validate((function(t){t&&e.executeSingleApproval()})):this.executeSingleApproval()},executeSingleApproval:function(){var e=this,t={id:this.singleApprovalForm.id,action:this.singleApprovalForm.action,rejectReason:this.singleApprovalForm.rejectReason};c(t).then((function(){e.$modal.msgSuccess("".concat("approve"===e.singleApprovalForm.action?"通过":"拒绝","审批成功")),e.singleApprovalOpen=!1,e.loadSubmissionRecords(e.currentRecord.litigationCaseId),e.getList()})).catch((function(e){console.error("审批失败:",e)}))},handleBatchApprove:function(e){0!==this.selectedRecords.length?(this.batchApprovalForm.action=e,this.batchApprovalForm.rejectReason="",this.batchApprovalOpen=!0):this.$modal.msgError("请选择要审批的记录")},confirmBatchApproval:function(){var e=this;"reject"===this.batchApprovalForm.action?this.$refs["batchApprovalForm"].validate((function(t){t&&e.executeBatchApproval()})):this.executeBatchApproval()},executeBatchApproval:function(){var e=this,t={ids:this.selectedRecords.map((function(e){return e.id})),action:this.batchApprovalForm.action,rejectReason:this.batchApprovalForm.rejectReason};p(t).then((function(){e.$modal.msgSuccess("批量".concat("approve"===e.batchApprovalForm.action?"通过":"拒绝","审批成功")),e.batchApprovalOpen=!1,e.selectedRecords=[],e.loadSubmissionRecords(e.currentRecord.litigationCaseId),e.getList()})).catch((function(e){console.error("批量审批失败:",e)}))},handleBatchApproveMain:function(e){var t=this;if(0!==this.ids.length){var a="0"===e?"通过":"拒绝";this.$modal.confirm("确认要批量".concat(a,"选中的 ").concat(this.ids.length," 条记录吗？")).then((function(){var a={ids:t.ids,action:"0"===e?"approve":"reject",rejectReason:"1"===e?"批量拒绝":""};return p(a)})).then((function(){t.$modal.msgSuccess("批量".concat(a,"成功")),t.getList()})).catch((function(){}))}else this.$modal.msgError("请选择要审批的记录")},handleDelete:function(){var e=this;this.$modal.confirm("是否确认删除选中的数据项？").then((function(){e.$modal.msgSuccess("删除功能暂未实现")})).catch((function(){}))},handleExport:function(){this.download("litigation_cost_approval/litigation_cost_approval/export",Object(o["a"])({},this.queryParams),"litigation_cost_approval_".concat((new Date).getTime(),".xlsx"))},openUserInfo:function(e){e.customerId||e.applyId?(this.customerInfo={customerId:e.customerId,applyId:e.applyId,customerName:e.customerName},this.userInfoVisible=!0):this.$modal.msgError("无法获取贷款人信息")},handleDateRangeChange:function(e){e&&2===e.length?(this.queryParams.startTime=e[0],this.queryParams.endTime=e[1]):(this.queryParams.startTime=null,this.queryParams.endTime=null),this.handleQuery()},handleApprovalDateRangeChange:function(e){e&&2===e.length?(this.queryParams.approvalStartTime=e[0],this.queryParams.approvalEndTime=e[1]):(this.queryParams.approvalStartTime=null,this.queryParams.approvalEndTime=null),this.handleQuery()},getStatusText:function(e){var t={0:"未审批",1:"全部同意",2:"已拒绝",3:"法诉主管审批",4:"总监审批",5:"财务主管/总监抄送",6:"总经理/董事长审批"};return t[e]||"未知状态"},canApproveRecord:function(e){return"1"!=e.approvalStatus&&"2"!=e.approvalStatus&&this.canUserApproveStatus(e.approvalStatus)},canUserApproveStatus:function(e){var t=this.$store.getters.roles||[],a=t.filter((function(e){return"string"===typeof e}));if(a.includes("admin"))return!0;var l=function(e){return a.some((function(t){return t===e}))};if(null==e||void 0===e||""===e)return l("法诉主管")||l("litigation_supervisor")||l("judicial_director");var r=!1;switch(e){case"0":r=l("法诉主管")||l("litigation_supervisor")||l("judicial_director");break;case"3":r=l("总监")||l("director");break;case"4":r=l("财务主管")||l("财务总监")||l("finance_supervisor")||l("finance_director");break;case"5":r=l("总经理")||l("董事长")||l("general_manager")||l("chairman");break;case"6":r=!1;break;default:r=!1}return r},getRequiredRoleText:function(e){switch(e){case"0":case null:case"":return"法诉主管";case"3":return"总监";case"4":return"财务主管/总监";case"5":return"总经理/董事长";case"6":return"已完成";default:return"未知"}}}},v=m,h=(a("325c"),a("2877")),g=Object(h["a"])(v,l,r,!1,null,"99ae4254",null);t["default"]=g.exports},bd52:function(e,t,a){"use strict";a.d(t,"l",(function(){return r})),a.d(t,"n",(function(){return o})),a.d(t,"h",(function(){return n})),a.d(t,"i",(function(){return i})),a.d(t,"o",(function(){return s})),a.d(t,"e",(function(){return c})),a.d(t,"s",(function(){return p})),a.d(t,"t",(function(){return u})),a.d(t,"a",(function(){return d})),a.d(t,"A",(function(){return m})),a.d(t,"g",(function(){return v})),a.d(t,"k",(function(){return h})),a.d(t,"w",(function(){return g})),a.d(t,"z",(function(){return b})),a.d(t,"f",(function(){return f})),a.d(t,"x",(function(){return _})),a.d(t,"c",(function(){return y})),a.d(t,"b",(function(){return w})),a.d(t,"v",(function(){return A})),a.d(t,"y",(function(){return S})),a.d(t,"j",(function(){return k})),a.d(t,"q",(function(){return x})),a.d(t,"B",(function(){return R})),a.d(t,"m",(function(){return j})),a.d(t,"r",(function(){return O})),a.d(t,"p",(function(){return P})),a.d(t,"d",(function(){return F})),a.d(t,"u",(function(){return I}));var l=a("b775");function r(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/list",method:"get",params:e})}function o(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/listBySlippageStatus3",method:"get",params:e})}function n(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/byLoanId/"+e,method:"get"})}function i(){return Object(l["a"])({url:"/bank_account/bank_account/bank?pageSize=30",method:"get"})}function s(e){return Object(l["a"])({url:"/loan_compensation/loan_compensation/detail",method:"get",params:e})}function c(e){return Object(l["a"])({url:"/vw_car/vw_car/"+e,method:"get"})}function p(e){return Object(l["a"])({url:"/loan_reminder/loan_reminder/list",method:"get",params:e})}function u(e){return Object(l["a"])({url:"/loan_reminder/loan_reminder",method:"post",data:e})}function d(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan",method:"post",data:e})}function m(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan",method:"put",data:e})}function v(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/"+e,method:"delete"})}function h(e,t){return Object(l["a"])({url:"/vw_customer_info/vw_customer_info/"+e+"?applyNo="+t,method:"get"})}function g(e){return Object(l["a"])({url:"/loan_settle/loan_settle/detail",method:"get",params:e})}function b(e){return Object(l["a"])({url:"/trial_balance/trial_balance/submit",method:"post",data:e})}function f(e){return Object(l["a"])({url:"/trial_balance/trial_balance/submitdc",method:"post",data:e})}function _(e){return Object(l["a"])({url:"/loan_settle/loan_settle",method:"post",data:e})}function y(e,t){return Object(l["a"])({url:"/loan_settle/loan_settle/process",method:"post",data:{loanId:e,status:t}})}function w(e){return Object(l["a"])({url:"/loan_compensation/loan_compensation/initiate",method:"post",data:e})}function A(e){return Object(l["a"])({url:"/loan_compensation/loan_compensation",method:"put",data:e})}function S(e){return Object(l["a"])({url:"/loan_settle/loan_settle",method:"put",data:e})}function k(e){return Object(l["a"])({url:"/system/user/listByRoleId",method:"get",params:{roleId:e}})}function x(e){return Object(l["a"])({url:"/loan_list/loan_list/batchUpdateUrgeUser",method:"put",data:e})}function R(e){return Object(l["a"])({url:"/loan_list/loan_list",method:"put",data:e})}function j(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/extension_list",method:"get",params:e})}function O(e){return Object(l["a"])({url:"/loan_extension/loan_extension/extension_detail",method:"get",params:{loan_id:e}})}function P(e){return Object(l["a"])({url:"/loan_extension/loan_extension/approve",method:"put",data:e})}function F(e){return Object(l["a"])({url:"/loan_list/loan_list/batchAssignPetitionUser",method:"put",data:e})}function I(e){return Object(l["a"])({url:"/loan_list/loan_list/revokePetitionUser/".concat(e),method:"put"})}},bff0:function(e,t,a){},d6fd:function(e,t,a){"use strict";a("1791")}}]);