{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _regenerator2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/regenerator.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nvar _toConsumableArray2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/toConsumableArray.js\"));\nrequire(\"core-js/modules/es.array.filter.js\");\nrequire(\"core-js/modules/es.array.from.js\");\nrequire(\"core-js/modules/es.array.includes.js\");\nrequire(\"core-js/modules/es.array.map.js\");\nrequire(\"core-js/modules/es.array.sort.js\");\nrequire(\"core-js/modules/es.number.constructor.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/es.set.js\");\nrequire(\"core-js/modules/es.set.difference.v2.js\");\nrequire(\"core-js/modules/es.set.intersection.v2.js\");\nrequire(\"core-js/modules/es.set.is-disjoint-from.v2.js\");\nrequire(\"core-js/modules/es.set.is-subset-of.v2.js\");\nrequire(\"core-js/modules/es.set.is-superset-of.v2.js\");\nrequire(\"core-js/modules/es.set.symmetric-difference.v2.js\");\nrequire(\"core-js/modules/es.set.union.v2.js\");\nrequire(\"core-js/modules/es.string.includes.js\");\nrequire(\"core-js/modules/es.string.iterator.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.filter.js\");\nrequire(\"core-js/modules/esnext.iterator.for-each.js\");\nrequire(\"core-js/modules/esnext.iterator.map.js\");\nrequire(\"core-js/modules/esnext.iterator.some.js\");\nrequire(\"core-js/modules/web.dom-collections.for-each.js\");\nrequire(\"core-js/modules/web.dom-collections.iterator.js\");\nvar _litigation = require(\"@/api/litigation/litigation\");\nvar _loanReminderLog = _interopRequireDefault(require(\"@/layout/components/Dialog/loanReminderLog.vue\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default2 = exports.default = {\n  name: 'LitigationLogView',\n  components: {\n    LoanReminderLog: _loanReminderLog.default\n  },\n  props: {\n    data: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  watch: {\n    data: {\n      handler: function handler(newVal) {\n        console.log('newVal:', newVal);\n        if (newVal && newVal.序号) {\n          this.loadLogData();\n        }\n      },\n      immediate: true\n    }\n  },\n  data: function data() {\n    return {\n      visible: false,\n      loading: false,\n      // 诉讼状态树结构\n      reminderLogLoanId: '',\n      litigationStatusTree: [{\n        label: '立案前',\n        value: '立案前',\n        children: [{\n          label: '准备资料',\n          value: '准备资料'\n        }, {\n          label: '已邮寄',\n          value: '撤案已邮寄'\n        }, {\n          label: '待立案',\n          value: '待立案'\n        }]\n      }, {\n        label: '立案-判决',\n        value: '立案-判决',\n        children: [{\n          label: '待出民初号',\n          value: '待出民初号'\n        }, {\n          label: '待开庭',\n          value: '待开庭'\n        }, {\n          label: '待出法院文书',\n          value: '待出法院文书'\n        }]\n      }, {\n        label: '判决-执行',\n        value: '判决-执行',\n        children: [{\n          label: '待出申请书',\n          value: '待出申请书'\n        }, {\n          label: '已提交执行书',\n          value: '已提交执行书'\n        }]\n      }, {\n        label: '执行后',\n        value: '执行后',\n        children: [{\n          label: '执行中',\n          value: '执行中'\n        }, {\n          label: '待送车',\n          value: '待送车'\n        }, {\n          label: '待法拍',\n          value: '待法拍'\n        }, {\n          label: '继续执行',\n          value: '继续执行'\n        }, {\n          label: '执行终本',\n          value: '执行终本'\n        }]\n      }, {\n        label: '结案',\n        value: '结案',\n        children: [{\n          label: '法诉减免结清',\n          value: '法诉减免结清'\n        }, {\n          label: '法诉全额结清',\n          value: '法诉全额结清'\n        }]\n      }, {\n        label: '撤案',\n        value: '撤案'\n      }],\n      // 当前激活的步骤索引\n      activeStep: 0,\n      logList: [],\n      // 新增的数据属性\n      searchKeyword: '',\n      filterStatus: '',\n      dateRange: [],\n      viewMode: 'table' // table 或 timeline\n    };\n  },\n  computed: {\n    // 从状态树中提取父节点的label作为进度条步骤\n    statusSteps: function statusSteps() {\n      return this.litigationStatusTree.map(function (item) {\n        return item.label;\n      });\n    },\n    // 筛选后的日志列表\n    filteredLogList: function filteredLogList() {\n      var _this = this;\n      var filtered = (0, _toConsumableArray2.default)(this.logList);\n\n      // 关键词搜索\n      if (this.searchKeyword) {\n        var keyword = this.searchKeyword.toLowerCase();\n        filtered = filtered.filter(function (log) {\n          return log.docName && log.docName.toLowerCase().includes(keyword) || log.createBy && log.createBy.toLowerCase().includes(keyword) || log.status && log.status.toLowerCase().includes(keyword) || log.docNumber && log.docNumber.toLowerCase().includes(keyword);\n        });\n      }\n\n      // 状态筛选\n      if (this.filterStatus) {\n        filtered = filtered.filter(function (log) {\n          return log.status === _this.filterStatus;\n        });\n      }\n\n      // 日期范围筛选\n      if (this.dateRange && this.dateRange.length === 2) {\n        var startDate = new Date(this.dateRange[0]);\n        var endDate = new Date(this.dateRange[1]);\n        endDate.setHours(23, 59, 59, 999); // 设置为当天结束时间\n\n        filtered = filtered.filter(function (log) {\n          if (!log.createTime) return false;\n          var logDate = new Date(log.createTime);\n          return logDate >= startDate && logDate <= endDate;\n        });\n      }\n\n      // 按时间倒序排列\n      return filtered.sort(function (a, b) {\n        return new Date(b.createTime) - new Date(a.createTime);\n      });\n    },\n    // 所有状态列表（用于筛选下拉框）\n    allStatuses: function allStatuses() {\n      var statuses = new Set();\n      this.logList.forEach(function (log) {\n        if (log.status) {\n          statuses.add(log.status);\n        }\n      });\n      return Array.from(statuses).sort();\n    }\n  },\n  methods: {\n    // 加载日志数据\n    loadLogData: function loadLogData() {\n      var _this2 = this;\n      return (0, _asyncToGenerator2.default)(/*#__PURE__*/(0, _regenerator2.default)().m(function _callee() {\n        var res, lastLogStatus, _t;\n        return (0, _regenerator2.default)().w(function (_context) {\n          while (1) switch (_context.p = _context.n) {\n            case 0:\n              if (!(!_this2.data || !_this2.data.序号)) {\n                _context.n = 1;\n                break;\n              }\n              return _context.a(2);\n            case 1:\n              _this2.loading = true;\n              _context.p = 2;\n              _context.n = 3;\n              return (0, _litigation.listLitigation_log)({\n                litigationId: _this2.data.序号\n              });\n            case 3:\n              res = _context.v;\n              _this2.logList = res.rows || [];\n\n              // 获取最后一个日志的状态，并设置对应的进度条步骤\n              if (_this2.logList.length > 0) {\n                lastLogStatus = _this2.logList[_this2.logList.length - 1].status;\n                _this2.setActiveStepByStatus(lastLogStatus);\n              }\n              _context.n = 5;\n              break;\n            case 4:\n              _context.p = 4;\n              _t = _context.v;\n              console.error('加载日志数据失败:', _t);\n              _this2.$message.error('加载日志数据失败');\n              _this2.logList = [];\n            case 5:\n              _context.p = 5;\n              _this2.loading = false;\n              return _context.f(5);\n            case 6:\n              return _context.a(2);\n          }\n        }, _callee, null, [[2, 4, 5, 6]]);\n      }))();\n    },\n    // 根据状态设置激活的步骤\n    setActiveStepByStatus: function setActiveStepByStatus(status) {\n      if (!status) {\n        this.activeStep = 0;\n        return;\n      }\n\n      // 查找状态对应的父节点索引\n      var parentIndex = this.findParentIndexByStatus(status);\n      this.activeStep = parentIndex >= 0 ? parentIndex : 0;\n    },\n    // 根据状态找到父节点的索引\n    findParentIndexByStatus: function findParentIndexByStatus(status) {\n      for (var i = 0; i < this.litigationStatusTree.length; i++) {\n        var item = this.litigationStatusTree[i];\n\n        // 如果是父节点本身\n        if (item.label === status || item.value === status) {\n          return i;\n        }\n\n        // 如果有子节点，在子节点中查找\n        if (item.children && item.children.length > 0) {\n          var childFound = item.children.some(function (child) {\n            return child.label === status || child.value === status;\n          });\n          if (childFound) {\n            return i;\n          }\n        }\n      }\n      return -1;\n    },\n    // 搜索处理\n    handleSearch: function handleSearch() {\n      // 搜索逻辑在computed中处理，这里可以添加防抖等优化\n    },\n    // 状态筛选处理\n    handleFilter: function handleFilter() {\n      // 筛选逻辑在computed中处理\n    },\n    // 日期筛选处理\n    handleDateFilter: function handleDateFilter() {\n      // 日期筛选逻辑在computed中处理\n    },\n    // 刷新数据\n    refreshData: function refreshData() {\n      this.loadLogData();\n    },\n    // 视图模式切换\n    handleViewModeChange: function handleViewModeChange() {\n      // 视图切换逻辑\n    },\n    // 查看日志详情\n    viewLogDetail: function viewLogDetail(log) {\n      if (log.docUploadUrl) {\n        // 打开文档链接\n        window.open(log.docUploadUrl, '_blank');\n      } else {\n        this.$message.info('暂无文档链接');\n      }\n    },\n    // 导出日志\n    exportLogs: function exportLogs() {\n      if (this.filteredLogList.length === 0) {\n        this.$message.warning('暂无数据可导出');\n        return;\n      }\n\n      // 这里可以实现导出功能\n      this.$message.success('导出功能开发中...');\n    },\n    // 格式化日期\n    formatDate: function formatDate(date) {\n      if (!date) return '-';\n      return new Date(date).toLocaleDateString('zh-CN');\n    },\n    // 格式化日期时间\n    formatDateTime: function formatDateTime(datetime) {\n      if (!datetime) return '-';\n      return new Date(datetime).toLocaleString('zh-CN');\n    },\n    // 格式化金额\n    formatMoney: function formatMoney(amount) {\n      if (amount === null || amount === undefined || amount === '') {\n        return '0.00';\n      }\n      return Number(amount).toLocaleString('zh-CN', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      });\n    },\n    // 获取催记类型文本\n    getUrgeStatusText: function getUrgeStatusText(value) {\n      var urgeStatusMap = {\n        1: '继续跟踪',\n        2: '约定还款',\n        3: '无法跟进'\n      };\n      return urgeStatusMap[value] || value || '-';\n    },\n    // 获取催记类型样式类\n    getUrgeStatusClass: function getUrgeStatusClass(value) {\n      var classMap = {\n        1: 'status-tracking',\n        2: 'status-appointment',\n        3: 'status-unable'\n      };\n      return classMap[value] || '';\n    },\n    // 获取状态标签类型\n    getStatusTagType: function getStatusTagType(status) {\n      var typeMap = {\n        '准备资料': 'info',\n        '待立案': 'warning',\n        '待出民初号': 'warning',\n        '待开庭': 'primary',\n        '待出法院文书': 'primary',\n        '执行中': 'success',\n        '结案': 'success',\n        '撤案': 'danger'\n      };\n      return typeMap[status] || 'info';\n    },\n    // 获取时间线类型\n    getTimelineType: function getTimelineType(status) {\n      var typeMap = {\n        '结案': 'success',\n        '撤案': 'danger',\n        '执行中': 'primary'\n      };\n      return typeMap[status] || 'primary';\n    },\n    // 获取时间线颜色\n    getTimelineColor: function getTimelineColor(status) {\n      var colorMap = {\n        '准备资料': '#909399',\n        '待立案': '#E6A23C',\n        '待出民初号': '#E6A23C',\n        '待开庭': '#409EFF',\n        '待出法院文书': '#409EFF',\n        '执行中': '#67C23A',\n        '结案': '#67C23A',\n        '撤案': '#F56C6C'\n      };\n      return colorMap[status] || '#409EFF';\n    },\n    openDialog: function openDialog() {\n      this.visible = true;\n      this.loadLogData();\n    },\n    handleUrgeLog: function handleUrgeLog() {\n      // 打开催记日志对话框，传入当前的 data\n      this.reminderLogLoanId = String(this.data.流程序号);\n      this.$refs.loanReminderLog.openLogDialog();\n    },\n    handleConfirm: function handleConfirm() {\n      this.visible = false;\n    },\n    handleCancel: function handleCancel() {\n      this.visible = false;\n      // 重置筛选条件\n      this.searchKeyword = '';\n      this.filterStatus = '';\n      this.dateRange = [];\n      this.viewMode = 'table';\n    }\n  }\n};", "map": {"version": 3, "names": ["_litigation", "require", "_loanReminderLog", "_interopRequireDefault", "name", "components", "LoanReminderLog", "props", "data", "type", "Object", "default", "watch", "handler", "newVal", "console", "log", "序号", "loadLogData", "immediate", "visible", "loading", "reminderLogLoanId", "litigationStatusTree", "label", "value", "children", "activeStep", "logList", "searchKeyword", "filterStatus", "date<PERSON><PERSON><PERSON>", "viewMode", "computed", "statusSteps", "map", "item", "filteredLogList", "_this", "filtered", "_toConsumableArray2", "keyword", "toLowerCase", "filter", "doc<PERSON>ame", "includes", "createBy", "status", "docNumber", "length", "startDate", "Date", "endDate", "setHours", "createTime", "logDate", "sort", "a", "b", "allStatuses", "statuses", "Set", "for<PERSON>ach", "add", "Array", "from", "methods", "_this2", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "res", "lastLogStatus", "_t", "w", "_context", "p", "n", "listLitigation_log", "litigationId", "v", "rows", "setActiveStepByStatus", "error", "$message", "f", "parentIndex", "findParentIndexByStatus", "i", "childFound", "some", "child", "handleSearch", "handleFilter", "handleDateFilter", "refreshData", "handleViewModeChange", "viewLogDetail", "docUploadUrl", "window", "open", "info", "exportLogs", "warning", "success", "formatDate", "date", "toLocaleDateString", "formatDateTime", "datetime", "toLocaleString", "formatMoney", "amount", "undefined", "Number", "minimumFractionDigits", "maximumFractionDigits", "getUrgeStatusText", "urgeStatusMap", "getUrgeStatusClass", "classMap", "getStatusTagType", "typeMap", "getTimelineType", "getTimelineColor", "colorMap", "openDialog", "handleUrgeLog", "String", "流程序号", "$refs", "loanReminderLog", "openLogDialog", "handleConfirm", "handleCancel"], "sources": ["src/views/litigation/litigation/modules/litigationLogView.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :visible.sync=\"visible\" title=\"法诉日志查看\" width=\"1200px\" @close=\"handleCancel\" class=\"litigation-log-dialog\">\r\n      <!-- 案件基本信息 -->\r\n      <div class=\"case-info-card\" v-if=\"data\">\r\n        <el-card shadow=\"never\" style=\"margin-bottom: 20px;\">\r\n          <div slot=\"header\" class=\"card-header\">\r\n            <span class=\"card-title\">案件基本信息</span>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">贷款人：</span>\r\n                <span class=\"value\">{{ data.贷款人 || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">身份证：</span>\r\n                <span class=\"value\">{{ data.身份证 || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">车牌号：</span>\r\n                <span class=\"value\">{{ data.车辆牌号 || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">法诉文员：</span>\r\n                <span class=\"value\">{{ data.法诉文员 || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 10px;\">\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">发起法诉日：</span>\r\n                <span class=\"value\">{{ formatDate(data.发起法诉日) || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">法诉状态：</span>\r\n                <span class=\"value\">{{ data.法诉子状态 || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">剩余金额：</span>\r\n                <span class=\"value amount\">{{ formatMoney(data.剩余金额) || '-' }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <div class=\"info-item\">\r\n                <span class=\"label\">最新催记类型：</span>\r\n                <span class=\"value\" :class=\"getUrgeStatusClass(data.日志类型)\">\r\n                  {{ getUrgeStatusText(data.日志类型) || '-' }}\r\n                </span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </div>\r\n\r\n      <!-- 进度条 -->\r\n      <el-steps :active=\"activeStep\" finish-status=\"success\" process-status=\"process\" align-center style=\"margin-bottom: 24px\" class=\"custom-steps\">\r\n        <el-step v-for=\"(item, idx) in statusSteps\" :key=\"idx\" :title=\"item\" />\r\n      </el-steps>\r\n\r\n      <!-- 筛选和搜索 -->\r\n      <div class=\"filter-section\" style=\"margin-bottom: 20px;\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-input\r\n              v-model=\"searchKeyword\"\r\n              placeholder=\"搜索日志内容、操作人\"\r\n              prefix-icon=\"el-icon-search\"\r\n              clearable\r\n              @input=\"handleSearch\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-select v-model=\"filterStatus\" placeholder=\"筛选状态\" clearable @change=\"handleFilter\">\r\n              <el-option label=\"全部\" value=\"\" />\r\n              <el-option v-for=\"status in allStatuses\" :key=\"status\" :label=\"status\" :value=\"status\" />\r\n            </el-select>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              format=\"yyyy-MM-dd\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              @change=\"handleDateFilter\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-button type=\"primary\" icon=\"el-icon-refresh\" @click=\"refreshData\">刷新</el-button>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 视图切换 -->\r\n      <div class=\"view-toggle\" style=\"margin-bottom: 20px;\">\r\n        <el-radio-group v-model=\"viewMode\" @change=\"handleViewModeChange\">\r\n          <el-radio-button label=\"table\">表格视图</el-radio-button>\r\n          <el-radio-button label=\"timeline\">时间线视图</el-radio-button>\r\n        </el-radio-group>\r\n      </div>\r\n\r\n      <!-- 表格视图 -->\r\n      <div v-if=\"viewMode === 'table'\" v-loading=\"loading\">\r\n        <el-table :data=\"filteredLogList\" border style=\"width: 100%; margin-bottom: 24px\" :height=\"400\">\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n          <el-table-column prop=\"createTime\" label=\"操作时间\" width=\"160\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatDateTime(scope.row.createTime) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"createBy\" label=\"操作人\" width=\"120\" align=\"center\" />\r\n          <el-table-column prop=\"status\" label=\"法诉状态\" width=\"140\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag :type=\"getStatusTagType(scope.row.status)\" size=\"small\">\r\n                {{ scope.row.status || '-' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"docName\" label=\"文档名称\" width=\"150\" align=\"center\" />\r\n          <el-table-column prop=\"docNumber\" label=\"文档编号\" width=\"120\" align=\"center\" />\r\n          <el-table-column prop=\"openDate\" label=\"生效日期\" width=\"120\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatDate(scope.row.openDate) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"100\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                type=\"text\"\r\n                size=\"mini\"\r\n                @click=\"viewLogDetail(scope.row)\"\r\n                v-if=\"scope.row.docUploadUrl\"\r\n              >\r\n                查看文档\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 时间线视图 -->\r\n      <div v-if=\"viewMode === 'timeline'\" v-loading=\"loading\" class=\"timeline-container\">\r\n        <el-timeline>\r\n          <el-timeline-item\r\n            v-for=\"(log, index) in filteredLogList\"\r\n            :key=\"index\"\r\n            :timestamp=\"formatDateTime(log.createTime)\"\r\n            placement=\"top\"\r\n            :type=\"getTimelineType(log.status)\"\r\n            :color=\"getTimelineColor(log.status)\"\r\n            size=\"large\"\r\n          >\r\n            <el-card shadow=\"hover\" class=\"timeline-card\">\r\n              <div class=\"timeline-header\">\r\n                <span class=\"timeline-title\">{{ log.status || '状态更新' }}</span>\r\n                <span class=\"timeline-operator\">{{ log.createBy }}</span>\r\n              </div>\r\n              <div class=\"timeline-content\">\r\n                <p v-if=\"log.docName\"><strong>文档：</strong>{{ log.docName }}</p>\r\n                <p v-if=\"log.docNumber\"><strong>编号：</strong>{{ log.docNumber }}</p>\r\n                <p v-if=\"log.openDate\"><strong>生效日期：</strong>{{ formatDate(log.openDate) }}</p>\r\n                <div v-if=\"log.docUploadUrl\" class=\"timeline-actions\">\r\n                  <el-button type=\"text\" size=\"mini\" @click=\"viewLogDetail(log)\">查看文档</el-button>\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n\r\n        <!-- 空状态 -->\r\n        <div v-if=\"filteredLogList.length === 0\" class=\"empty-state\">\r\n          <el-empty description=\"暂无日志记录\" />\r\n        </div>\r\n      </div>\r\n\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleUrgeLog\" icon=\"el-icon-document\">催记日志</el-button>\r\n        <el-button @click=\"exportLogs\" icon=\"el-icon-download\">导出日志</el-button>\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确认</el-button>\r\n        <el-button @click=\"handleCancel\">取消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 催记日志组件 -->\r\n    <loan-reminder-log ref=\"loanReminderLog\" :loan-id=\"reminderLogLoanId\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listLitigation_log } from '@/api/litigation/litigation'\r\nimport LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'\r\n\r\nexport default {\r\n  name: 'LitigationLogView',\r\n  components: {\r\n    LoanReminderLog,\r\n  },\r\n  props: {\r\n    data: {\r\n      type: Object,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        console.log('newVal:', newVal)\r\n        if (newVal && newVal.序号) {\r\n          this.loadLogData()\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      loading: false,\r\n      // 诉讼状态树结构\r\n      reminderLogLoanId: '',\r\n      litigationStatusTree: [\r\n        {\r\n          label: '立案前',\r\n          value: '立案前',\r\n          children: [\r\n            { label: '准备资料', value: '准备资料' },\r\n            { label: '已邮寄', value: '撤案已邮寄' },\r\n            { label: '待立案', value: '待立案' },\r\n          ],\r\n        },\r\n        {\r\n          label: '立案-判决',\r\n          value: '立案-判决',\r\n          children: [\r\n            { label: '待出民初号', value: '待出民初号' },\r\n            { label: '待开庭', value: '待开庭' },\r\n            { label: '待出法院文书', value: '待出法院文书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '判决-执行',\r\n          value: '判决-执行',\r\n          children: [\r\n            { label: '待出申请书', value: '待出申请书' },\r\n            { label: '已提交执行书', value: '已提交执行书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '执行后',\r\n          value: '执行后',\r\n          children: [\r\n            { label: '执行中', value: '执行中' },\r\n            { label: '待送车', value: '待送车' },\r\n            { label: '待法拍', value: '待法拍' },\r\n            { label: '继续执行', value: '继续执行' },\r\n            { label: '执行终本', value: '执行终本' },\r\n          ],\r\n        },\r\n        {\r\n          label: '结案',\r\n          value: '结案',\r\n          children: [\r\n            { label: '法诉减免结清', value: '法诉减免结清' },\r\n            { label: '法诉全额结清', value: '法诉全额结清' },\r\n          ],\r\n        },\r\n        { label: '撤案', value: '撤案' },\r\n      ],\r\n      // 当前激活的步骤索引\r\n      activeStep: 0,\r\n      logList: [],\r\n      // 新增的数据属性\r\n      searchKeyword: '',\r\n      filterStatus: '',\r\n      dateRange: [],\r\n      viewMode: 'table', // table 或 timeline\r\n    }\r\n  },\r\n  computed: {\r\n    // 从状态树中提取父节点的label作为进度条步骤\r\n    statusSteps() {\r\n      return this.litigationStatusTree.map(item => item.label)\r\n    },\r\n\r\n    // 筛选后的日志列表\r\n    filteredLogList() {\r\n      let filtered = [...this.logList]\r\n\r\n      // 关键词搜索\r\n      if (this.searchKeyword) {\r\n        const keyword = this.searchKeyword.toLowerCase()\r\n        filtered = filtered.filter(log =>\r\n          (log.docName && log.docName.toLowerCase().includes(keyword)) ||\r\n          (log.createBy && log.createBy.toLowerCase().includes(keyword)) ||\r\n          (log.status && log.status.toLowerCase().includes(keyword)) ||\r\n          (log.docNumber && log.docNumber.toLowerCase().includes(keyword))\r\n        )\r\n      }\r\n\r\n      // 状态筛选\r\n      if (this.filterStatus) {\r\n        filtered = filtered.filter(log => log.status === this.filterStatus)\r\n      }\r\n\r\n      // 日期范围筛选\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        const startDate = new Date(this.dateRange[0])\r\n        const endDate = new Date(this.dateRange[1])\r\n        endDate.setHours(23, 59, 59, 999) // 设置为当天结束时间\r\n\r\n        filtered = filtered.filter(log => {\r\n          if (!log.createTime) return false\r\n          const logDate = new Date(log.createTime)\r\n          return logDate >= startDate && logDate <= endDate\r\n        })\r\n      }\r\n\r\n      // 按时间倒序排列\r\n      return filtered.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))\r\n    },\r\n\r\n    // 所有状态列表（用于筛选下拉框）\r\n    allStatuses() {\r\n      const statuses = new Set()\r\n      this.logList.forEach(log => {\r\n        if (log.status) {\r\n          statuses.add(log.status)\r\n        }\r\n      })\r\n      return Array.from(statuses).sort()\r\n    }\r\n  },\r\n  methods: {\r\n    // 加载日志数据\r\n    async loadLogData() {\r\n      if (!this.data || !this.data.序号) return\r\n\r\n      this.loading = true\r\n      try {\r\n        const res = await listLitigation_log({ litigationId: this.data.序号 })\r\n        this.logList = res.rows || []\r\n\r\n        // 获取最后一个日志的状态，并设置对应的进度条步骤\r\n        if (this.logList.length > 0) {\r\n          const lastLogStatus = this.logList[this.logList.length - 1].status\r\n          this.setActiveStepByStatus(lastLogStatus)\r\n        }\r\n      } catch (error) {\r\n        console.error('加载日志数据失败:', error)\r\n        this.$message.error('加载日志数据失败')\r\n        this.logList = []\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 根据状态设置激活的步骤\r\n    setActiveStepByStatus(status) {\r\n      if (!status) {\r\n        this.activeStep = 0\r\n        return\r\n      }\r\n\r\n      // 查找状态对应的父节点索引\r\n      const parentIndex = this.findParentIndexByStatus(status)\r\n      this.activeStep = parentIndex >= 0 ? parentIndex : 0\r\n    },\r\n\r\n    // 根据状态找到父节点的索引\r\n    findParentIndexByStatus(status) {\r\n      for (let i = 0; i < this.litigationStatusTree.length; i++) {\r\n        const item = this.litigationStatusTree[i]\r\n\r\n        // 如果是父节点本身\r\n        if (item.label === status || item.value === status) {\r\n          return i\r\n        }\r\n\r\n        // 如果有子节点，在子节点中查找\r\n        if (item.children && item.children.length > 0) {\r\n          const childFound = item.children.some(child => child.label === status || child.value === status)\r\n          if (childFound) {\r\n            return i\r\n          }\r\n        }\r\n      }\r\n      return -1\r\n    },\r\n\r\n    // 搜索处理\r\n    handleSearch() {\r\n      // 搜索逻辑在computed中处理，这里可以添加防抖等优化\r\n    },\r\n\r\n    // 状态筛选处理\r\n    handleFilter() {\r\n      // 筛选逻辑在computed中处理\r\n    },\r\n\r\n    // 日期筛选处理\r\n    handleDateFilter() {\r\n      // 日期筛选逻辑在computed中处理\r\n    },\r\n\r\n    // 刷新数据\r\n    refreshData() {\r\n      this.loadLogData()\r\n    },\r\n\r\n    // 视图模式切换\r\n    handleViewModeChange() {\r\n      // 视图切换逻辑\r\n    },\r\n\r\n    // 查看日志详情\r\n    viewLogDetail(log) {\r\n      if (log.docUploadUrl) {\r\n        // 打开文档链接\r\n        window.open(log.docUploadUrl, '_blank')\r\n      } else {\r\n        this.$message.info('暂无文档链接')\r\n      }\r\n    },\r\n\r\n    // 导出日志\r\n    exportLogs() {\r\n      if (this.filteredLogList.length === 0) {\r\n        this.$message.warning('暂无数据可导出')\r\n        return\r\n      }\r\n\r\n      // 这里可以实现导出功能\r\n      this.$message.success('导出功能开发中...')\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(date) {\r\n      if (!date) return '-'\r\n      return new Date(date).toLocaleDateString('zh-CN')\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(datetime) {\r\n      if (!datetime) return '-'\r\n      return new Date(datetime).toLocaleString('zh-CN')\r\n    },\r\n\r\n    // 格式化金额\r\n    formatMoney(amount) {\r\n      if (amount === null || amount === undefined || amount === '') {\r\n        return '0.00'\r\n      }\r\n      return Number(amount).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      })\r\n    },\r\n\r\n    // 获取催记类型文本\r\n    getUrgeStatusText(value) {\r\n      const urgeStatusMap = {\r\n        1: '继续跟踪',\r\n        2: '约定还款',\r\n        3: '无法跟进'\r\n      }\r\n      return urgeStatusMap[value] || value || '-'\r\n    },\r\n\r\n    // 获取催记类型样式类\r\n    getUrgeStatusClass(value) {\r\n      const classMap = {\r\n        1: 'status-tracking',\r\n        2: 'status-appointment',\r\n        3: 'status-unable'\r\n      }\r\n      return classMap[value] || ''\r\n    },\r\n\r\n    // 获取状态标签类型\r\n    getStatusTagType(status) {\r\n      const typeMap = {\r\n        '准备资料': 'info',\r\n        '待立案': 'warning',\r\n        '待出民初号': 'warning',\r\n        '待开庭': 'primary',\r\n        '待出法院文书': 'primary',\r\n        '执行中': 'success',\r\n        '结案': 'success',\r\n        '撤案': 'danger'\r\n      }\r\n      return typeMap[status] || 'info'\r\n    },\r\n\r\n    // 获取时间线类型\r\n    getTimelineType(status) {\r\n      const typeMap = {\r\n        '结案': 'success',\r\n        '撤案': 'danger',\r\n        '执行中': 'primary'\r\n      }\r\n      return typeMap[status] || 'primary'\r\n    },\r\n\r\n    // 获取时间线颜色\r\n    getTimelineColor(status) {\r\n      const colorMap = {\r\n        '准备资料': '#909399',\r\n        '待立案': '#E6A23C',\r\n        '待出民初号': '#E6A23C',\r\n        '待开庭': '#409EFF',\r\n        '待出法院文书': '#409EFF',\r\n        '执行中': '#67C23A',\r\n        '结案': '#67C23A',\r\n        '撤案': '#F56C6C'\r\n      }\r\n      return colorMap[status] || '#409EFF'\r\n    },\r\n\r\n    openDialog() {\r\n      this.visible = true\r\n      this.loadLogData()\r\n    },\r\n\r\n    handleUrgeLog() {\r\n      // 打开催记日志对话框，传入当前的 data\r\n      this.reminderLogLoanId = String(this.data.流程序号)\r\n      this.$refs.loanReminderLog.openLogDialog()\r\n    },\r\n\r\n    handleConfirm() {\r\n      this.visible = false\r\n    },\r\n\r\n    handleCancel() {\r\n      this.visible = false\r\n      // 重置筛选条件\r\n      this.searchKeyword = ''\r\n      this.filterStatus = ''\r\n      this.dateRange = []\r\n      this.viewMode = 'table'\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 案件信息卡片 */\r\n.case-info-card .card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.case-info-card .card-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.case-info-card .info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.case-info-card .info-item .label {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  min-width: 80px;\r\n}\r\n\r\n.case-info-card .info-item .value {\r\n  color: #303133;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.case-info-card .info-item .value.amount {\r\n  color: #E6A23C;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 催记状态样式 */\r\n.status-tracking {\r\n  color: #409EFF;\r\n}\r\n\r\n.status-appointment {\r\n  color: #67C23A;\r\n}\r\n\r\n.status-unable {\r\n  color: #F56C6C;\r\n}\r\n\r\n/* 筛选区域 */\r\n.filter-section {\r\n  background: #f5f7fa;\r\n  padding: 16px;\r\n  border-radius: 4px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 视图切换 */\r\n.view-toggle {\r\n  text-align: center;\r\n}\r\n\r\n/* 时间线容器 */\r\n.timeline-container {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n  padding: 0 20px;\r\n}\r\n\r\n/* 时间线卡片 */\r\n.timeline-card {\r\n  margin-bottom: 16px;\r\n\r\n  .timeline-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 12px;\r\n\r\n    .timeline-title {\r\n      font-size: 16px;\r\n      font-weight: bold;\r\n      color: #303133;\r\n    }\r\n\r\n    .timeline-operator {\r\n      font-size: 14px;\r\n      color: #909399;\r\n    }\r\n  }\r\n\r\n  .timeline-content {\r\n    p {\r\n      margin: 8px 0;\r\n      font-size: 14px;\r\n      color: #606266;\r\n\r\n      strong {\r\n        color: #303133;\r\n      }\r\n    }\r\n  }\r\n\r\n  .timeline-actions {\r\n    margin-top: 12px;\r\n    text-align: right;\r\n  }\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 40px 0;\r\n}\r\n\r\n/* 自定义步骤条样式 - 激活节点为蓝色 */\r\n::v-deep .custom-steps .el-step__head.is-process {\r\n  color: #409eff;\r\n  border-color: #409eff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__head.is-process .el-step__icon {\r\n  background-color: #409eff;\r\n  border-color: #409eff;\r\n  color: #fff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__title.is-process {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 时间线样式优化 */\r\n::v-deep .el-timeline-item__timestamp {\r\n  font-size: 13px;\r\n  color: #909399;\r\n}\r\n\r\n::v-deep .el-timeline-item__wrapper {\r\n  padding-left: 28px;\r\n}\r\n\r\n::v-deep .el-timeline-item__tail {\r\n  border-left: 2px solid #e4e7ed;\r\n}\r\n\r\n/* 表格样式优化 */\r\n::v-deep .el-table {\r\n  .el-table__header-wrapper {\r\n    background: #fafafa;\r\n  }\r\n\r\n  .el-table__row:hover > td {\r\n    background-color: #f5f7fa;\r\n  }\r\n}\r\n\r\n/* 对话框底部按钮 */\r\n.dialog-footer {\r\n  text-align: right;\r\n\r\n  .el-button {\r\n    margin-left: 10px;\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .litigation-log-dialog {\r\n    width: 95% !important;\r\n  }\r\n\r\n  .case-info-card .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .filter-section .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2MA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,eAAA,EAAAA;EACA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,KAAA;IACAJ,IAAA;MACAK,OAAA,WAAAA,QAAAC,MAAA;QACAC,OAAA,CAAAC,GAAA,YAAAF,MAAA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAG,EAAA;UACA,KAAAC,WAAA;QACA;MACA;MACAC,SAAA;IACA;EACA;EACAX,IAAA,WAAAA,KAAA;IACA;MACAY,OAAA;MACAC,OAAA;MACA;MACAC,iBAAA;MACAC,oBAAA,GACA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACA;MACAE,UAAA;MACAC,OAAA;MACA;MACAC,aAAA;MACAC,YAAA;MACAC,SAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAX,oBAAA,CAAAY,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAZ,KAAA;MAAA;IACA;IAEA;IACAa,eAAA,WAAAA,gBAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,QAAA,OAAAC,mBAAA,CAAA7B,OAAA,OAAAiB,OAAA;;MAEA;MACA,SAAAC,aAAA;QACA,IAAAY,OAAA,QAAAZ,aAAA,CAAAa,WAAA;QACAH,QAAA,GAAAA,QAAA,CAAAI,MAAA,WAAA3B,GAAA;UAAA,OACAA,GAAA,CAAA4B,OAAA,IAAA5B,GAAA,CAAA4B,OAAA,CAAAF,WAAA,GAAAG,QAAA,CAAAJ,OAAA,KACAzB,GAAA,CAAA8B,QAAA,IAAA9B,GAAA,CAAA8B,QAAA,CAAAJ,WAAA,GAAAG,QAAA,CAAAJ,OAAA,KACAzB,GAAA,CAAA+B,MAAA,IAAA/B,GAAA,CAAA+B,MAAA,CAAAL,WAAA,GAAAG,QAAA,CAAAJ,OAAA,KACAzB,GAAA,CAAAgC,SAAA,IAAAhC,GAAA,CAAAgC,SAAA,CAAAN,WAAA,GAAAG,QAAA,CAAAJ,OAAA;QAAA,CACA;MACA;;MAEA;MACA,SAAAX,YAAA;QACAS,QAAA,GAAAA,QAAA,CAAAI,MAAA,WAAA3B,GAAA;UAAA,OAAAA,GAAA,CAAA+B,MAAA,KAAAT,KAAA,CAAAR,YAAA;QAAA;MACA;;MAEA;MACA,SAAAC,SAAA,SAAAA,SAAA,CAAAkB,MAAA;QACA,IAAAC,SAAA,OAAAC,IAAA,MAAApB,SAAA;QACA,IAAAqB,OAAA,OAAAD,IAAA,MAAApB,SAAA;QACAqB,OAAA,CAAAC,QAAA;;QAEAd,QAAA,GAAAA,QAAA,CAAAI,MAAA,WAAA3B,GAAA;UACA,KAAAA,GAAA,CAAAsC,UAAA;UACA,IAAAC,OAAA,OAAAJ,IAAA,CAAAnC,GAAA,CAAAsC,UAAA;UACA,OAAAC,OAAA,IAAAL,SAAA,IAAAK,OAAA,IAAAH,OAAA;QACA;MACA;;MAEA;MACA,OAAAb,QAAA,CAAAiB,IAAA,WAAAC,CAAA,EAAAC,CAAA;QAAA,WAAAP,IAAA,CAAAO,CAAA,CAAAJ,UAAA,QAAAH,IAAA,CAAAM,CAAA,CAAAH,UAAA;MAAA;IACA;IAEA;IACAK,WAAA,WAAAA,YAAA;MACA,IAAAC,QAAA,OAAAC,GAAA;MACA,KAAAjC,OAAA,CAAAkC,OAAA,WAAA9C,GAAA;QACA,IAAAA,GAAA,CAAA+B,MAAA;UACAa,QAAA,CAAAG,GAAA,CAAA/C,GAAA,CAAA+B,MAAA;QACA;MACA;MACA,OAAAiB,KAAA,CAAAC,IAAA,CAAAL,QAAA,EAAAJ,IAAA;IACA;EACA;EACAU,OAAA;IACA;IACAhD,WAAA,WAAAA,YAAA;MAAA,IAAAiD,MAAA;MAAA,WAAAC,kBAAA,CAAAzD,OAAA,mBAAA0D,aAAA,CAAA1D,OAAA,IAAA2D,CAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA,EAAAC,aAAA,EAAAC,EAAA;QAAA,WAAAL,aAAA,CAAA1D,OAAA,IAAAgE,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cAAA,MACA,CAAAX,MAAA,CAAA3D,IAAA,KAAA2D,MAAA,CAAA3D,IAAA,CAAAS,EAAA;gBAAA2D,QAAA,CAAAE,CAAA;gBAAA;cAAA;cAAA,OAAAF,QAAA,CAAAnB,CAAA;YAAA;cAEAU,MAAA,CAAA9C,OAAA;cAAAuD,QAAA,CAAAC,CAAA;cAAAD,QAAA,CAAAE,CAAA;cAAA,OAEA,IAAAC,8BAAA;gBAAAC,YAAA,EAAAb,MAAA,CAAA3D,IAAA,CAAAS;cAAA;YAAA;cAAAuD,GAAA,GAAAI,QAAA,CAAAK,CAAA;cACAd,MAAA,CAAAvC,OAAA,GAAA4C,GAAA,CAAAU,IAAA;;cAEA;cACA,IAAAf,MAAA,CAAAvC,OAAA,CAAAqB,MAAA;gBACAwB,aAAA,GAAAN,MAAA,CAAAvC,OAAA,CAAAuC,MAAA,CAAAvC,OAAA,CAAAqB,MAAA,MAAAF,MAAA;gBACAoB,MAAA,CAAAgB,qBAAA,CAAAV,aAAA;cACA;cAAAG,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAK,CAAA;cAEAlE,OAAA,CAAAqE,KAAA,cAAAV,EAAA;cACAP,MAAA,CAAAkB,QAAA,CAAAD,KAAA;cACAjB,MAAA,CAAAvC,OAAA;YAAA;cAAAgD,QAAA,CAAAC,CAAA;cAEAV,MAAA,CAAA9C,OAAA;cAAA,OAAAuD,QAAA,CAAAU,CAAA;YAAA;cAAA,OAAAV,QAAA,CAAAnB,CAAA;UAAA;QAAA,GAAAc,OAAA;MAAA;IAEA;IAEA;IACAY,qBAAA,WAAAA,sBAAApC,MAAA;MACA,KAAAA,MAAA;QACA,KAAApB,UAAA;QACA;MACA;;MAEA;MACA,IAAA4D,WAAA,QAAAC,uBAAA,CAAAzC,MAAA;MACA,KAAApB,UAAA,GAAA4D,WAAA,QAAAA,WAAA;IACA;IAEA;IACAC,uBAAA,WAAAA,wBAAAzC,MAAA;MACA,SAAA0C,CAAA,MAAAA,CAAA,QAAAlE,oBAAA,CAAA0B,MAAA,EAAAwC,CAAA;QACA,IAAArD,IAAA,QAAAb,oBAAA,CAAAkE,CAAA;;QAEA;QACA,IAAArD,IAAA,CAAAZ,KAAA,KAAAuB,MAAA,IAAAX,IAAA,CAAAX,KAAA,KAAAsB,MAAA;UACA,OAAA0C,CAAA;QACA;;QAEA;QACA,IAAArD,IAAA,CAAAV,QAAA,IAAAU,IAAA,CAAAV,QAAA,CAAAuB,MAAA;UACA,IAAAyC,UAAA,GAAAtD,IAAA,CAAAV,QAAA,CAAAiE,IAAA,WAAAC,KAAA;YAAA,OAAAA,KAAA,CAAApE,KAAA,KAAAuB,MAAA,IAAA6C,KAAA,CAAAnE,KAAA,KAAAsB,MAAA;UAAA;UACA,IAAA2C,UAAA;YACA,OAAAD,CAAA;UACA;QACA;MACA;MACA;IACA;IAEA;IACAI,YAAA,WAAAA,aAAA;MACA;IAAA,CACA;IAEA;IACAC,YAAA,WAAAA,aAAA;MACA;IAAA,CACA;IAEA;IACAC,gBAAA,WAAAA,iBAAA;MACA;IAAA,CACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAA9E,WAAA;IACA;IAEA;IACA+E,oBAAA,WAAAA,qBAAA;MACA;IAAA,CACA;IAEA;IACAC,aAAA,WAAAA,cAAAlF,GAAA;MACA,IAAAA,GAAA,CAAAmF,YAAA;QACA;QACAC,MAAA,CAAAC,IAAA,CAAArF,GAAA,CAAAmF,YAAA;MACA;QACA,KAAAd,QAAA,CAAAiB,IAAA;MACA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAA;MACA,SAAAlE,eAAA,CAAAY,MAAA;QACA,KAAAoC,QAAA,CAAAmB,OAAA;QACA;MACA;;MAEA;MACA,KAAAnB,QAAA,CAAAoB,OAAA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAAC,IAAA;MACA,KAAAA,IAAA;MACA,WAAAxD,IAAA,CAAAwD,IAAA,EAAAC,kBAAA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAAC,QAAA;MACA,KAAAA,QAAA;MACA,WAAA3D,IAAA,CAAA2D,QAAA,EAAAC,cAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAAC,MAAA;MACA,IAAAA,MAAA,aAAAA,MAAA,KAAAC,SAAA,IAAAD,MAAA;QACA;MACA;MACA,OAAAE,MAAA,CAAAF,MAAA,EAAAF,cAAA;QACAK,qBAAA;QACAC,qBAAA;MACA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAA7F,KAAA;MACA,IAAA8F,aAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,aAAA,CAAA9F,KAAA,KAAAA,KAAA;IACA;IAEA;IACA+F,kBAAA,WAAAA,mBAAA/F,KAAA;MACA,IAAAgG,QAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAhG,KAAA;IACA;IAEA;IACAiG,gBAAA,WAAAA,iBAAA3E,MAAA;MACA,IAAA4E,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA5E,MAAA;IACA;IAEA;IACA6E,eAAA,WAAAA,gBAAA7E,MAAA;MACA,IAAA4E,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA5E,MAAA;IACA;IAEA;IACA8E,gBAAA,WAAAA,iBAAA9E,MAAA;MACA,IAAA+E,QAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAA/E,MAAA;IACA;IAEAgF,UAAA,WAAAA,WAAA;MACA,KAAA3G,OAAA;MACA,KAAAF,WAAA;IACA;IAEA8G,aAAA,WAAAA,cAAA;MACA;MACA,KAAA1G,iBAAA,GAAA2G,MAAA,MAAAzH,IAAA,CAAA0H,IAAA;MACA,KAAAC,KAAA,CAAAC,eAAA,CAAAC,aAAA;IACA;IAEAC,aAAA,WAAAA,cAAA;MACA,KAAAlH,OAAA;IACA;IAEAmH,YAAA,WAAAA,aAAA;MACA,KAAAnH,OAAA;MACA;MACA,KAAAS,aAAA;MACA,KAAAC,YAAA;MACA,KAAAC,SAAA;MACA,KAAAC,QAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}