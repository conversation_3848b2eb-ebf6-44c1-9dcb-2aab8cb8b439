(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c5cc2128"],{"7da3":function(t,a,e){"use strict";e.d(a,"a",(function(){return n}));var l=e("b775");function n(t){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/replayList",method:"get",params:t})}},9016:function(t,a,e){"use strict";e.r(a);var l=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"app-container"},[e("el-button",{staticStyle:{"margin-bottom":"10px"},attrs:{type:"primary",size:"mini"}},[t._v("代扣账单")]),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.vw_planList}},[e("el-table-column",{attrs:{label:"期次",align:"center",prop:"followUp"}}),e("el-table-column",{attrs:{label:"本金余额",align:"center",prop:"balance",width:"130"}}),e("el-table-column",{attrs:{label:"应还款日",align:"center",prop:"repayDate",width:"130"}}),e("el-table-column",{attrs:{label:"实际还款日",align:"center",prop:"actualRepayDate",width:"130"}}),e("el-table-column",{attrs:{label:"总月供",align:"center",prop:"repayAmount"}}),e("el-table-column",{attrs:{label:"担保费",align:"center",prop:"guaranteeFee"}}),e("el-table-column",{attrs:{label:"蓝海月供",align:"center",prop:"lhRepayAmount"}}),e("el-table-column",{attrs:{label:"蓝海收益",align:"center",prop:"lhEarnings"}}),e("el-table-column",{attrs:{label:"广明收益",align:"center",prop:"gmEarnings"}}),e("el-table-column",{attrs:{label:"应还本金",align:"center",prop:"capital"}}),e("el-table-column",{attrs:{label:"实还本金",align:"center",prop:"actualCapital"}}),e("el-table-column",{attrs:{label:"应还利息",align:"center",prop:"interest"}}),e("el-table-column",{attrs:{label:"实还利息",align:"center",prop:"actualInterest"}}),e("el-table-column",{attrs:{label:"应还罚息",align:"center",prop:"defInterest"}}),e("el-table-column",{attrs:{label:"应还复利",align:"center",prop:"compoundInterest"}}),e("el-table-column",{attrs:{label:"逾期天数",align:"center",prop:"overdueDays"}}),e("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s("1"==a.row.status?"待还款":"2"==a.row.status?"部分还款":"3"==a.row.status?"已还款":"4"==a.row.status?"已逾期":"5"==a.row.status?"逾期部分还款":"6"==a.row.status?"逾期已还款":"7"==a.row.status?"已结清":"代偿"))])]}}])}),e("el-table-column",{attrs:{label:"更新时间",align:"center",prop:"updateDate",width:"130"}})],1),e("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(a){return t.$set(t.queryParams,"pageNum",a)},"update:limit":function(a){return t.$set(t.queryParams,"pageSize",a)},pagination:t.getList}})],1)},n=[],r=e("7da3"),o=e("5f87"),s={name:"repayment_plan",data:function(){return{loading:!0,total:0,vw_planList:[],queryParams:{pageNum:1,pageSize:15,partnerId:null,applyId:null},headers:{Authorization:"Bearer "+Object(o["a"])()}}},created:function(){this.queryParams.applyId=this.$route.query.applyId,this.queryParams.partnerId=this.$route.query.partnerId,this.getList()},methods:{getList:function(){var t=this;this.loading=!0,Object(r["a"])(this.queryParams).then((function(a){t.vw_planList=a.rows,t.total=a.total,t.loading=!1}))}}},i=s,u=e("2877"),p=Object(u["a"])(i,l,n,!1,null,null,null);a["default"]=p.exports}}]);