(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2ad6a9d7"],{"0f5f":function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{attrs:{title:t.title,visible:t.dialogVisible,width:"800px"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.close}},[a("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==t.permission||"2"==t.permission?a("el-descriptions-item",{attrs:{label:"车牌号"}},[t._v(t._s(t.carInfo.plateNo||"-"))]):t._e(),"1"==t.permission?a("el-descriptions-item",{attrs:{label:"车架号"}},[t._v(t._s(t.carInfo.identityNo||"-"))]):t._e(),"1"==t.permission?a("el-descriptions-item",{attrs:{label:"发动机号"}},[t._v(t._s(t.carInfo.engineNumber||"-"))]):t._e(),"1"==t.permission?a("el-descriptions-item",{attrs:{label:"车辆状态"}},[t._v(t._s(t.carStatusLabel))]):t._e(),"1"==t.permission?a("el-descriptions-item",{attrs:{label:"车辆位置"}},[t._v(t._s(t.carInfo.carAddress||"-"))]):t._e(),"1"==t.permission?a("el-descriptions-item",{attrs:{label:"GPS状态"}},[t._v(t._s(t.gpsStatusLabel))]):t._e(),"1"==t.permission||"2"==t.permission?a("el-descriptions-item",{attrs:{label:"车牌照片"}},[a("el-button",{attrs:{type:"text"}},[t._v("查看车牌照片")])],1):t._e(),"1"==t.permission||"2"==t.permission?a("el-descriptions-item",{attrs:{label:"行驶证照片"}},[a("el-button",{attrs:{type:"text"}},[t._v("查看行驶证照片")])],1):t._e(),"1"==t.permission||"2"==t.permission?a("el-descriptions-item",{attrs:{label:"登记证照片"}},[a("el-button",{attrs:{type:"text"}},[t._v("查看登记证照片")])],1):t._e()],1)],1)},l=[],r=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),a("bd52")),o={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var t=this,e=this.carStatusList.find((function(e){return e.value===t.carInfo.carStatus}));return e?e.label:"未知状态"},gpsStatusLabel:function(){var t=this,e=this.gpsStatusList.find((function(e){return e.value===t.carInfo.gpsStatus}));return e?e.label:"未知状态"}},watch:{visible:{handler:function(t){var e=this;this.dialogVisible=t,t&&this.plateNo&&Object(r["e"])(this.plateNo).then((function(t){e.carInfo=t.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},s=o,i=a("2877"),u=Object(i["a"])(s,n,l,!1,null,null,null);e["a"]=u.exports},1791:function(t,e,a){},"2eca":function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{attrs:{title:t.title,customerInfo:t.customerInfo,visible:t.dialogVisible,width:"800px"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[t._v("姓名")]),t._v(" "+t._s(t.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[t._v("电话")]),t._v(" "+t._s(t.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[t._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[t._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[t._v("身份证号")]),t._v(" "+t._s(t.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[t._v("住址")]),t._v(" "+t._s(t.userInfo.liveAddress)+t._s(t.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[t._v("身份证地址")]),t._v(" "+t._s(t.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[t._v("文书送达地址")]),t._v(" "+t._s(t.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[t._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:t.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},l=[],r=a("bd52"),o={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(t){var e=this;this.dialogVisible=t,console.log(this.customerInfo),t&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(r["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(t){e.userInfo=t.data.customerInfo,e.coborrowerList=t.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},s=o,i=(a("d6fd"),a("2877")),u=Object(i["a"])(s,n,l,!1,null,"8a3d4978",null);e["a"]=u.exports},"99c9":function(t,e,a){"use strict";a.d(e,"b",(function(){return l})),a.d(e,"a",(function(){return r})),a.d(e,"c",(function(){return o}));var n=a("b775");function l(t){return Object(n["a"])({url:"/installment_application_audit/installment_application_audit/list",method:"get",params:t})}function r(t){return Object(n["a"])({url:"/installment_application_audit/installment_application_audit",method:"post",data:t})}function o(t){return Object(n["a"])({url:"/installment_application_audit/installment_application_audit",method:"put",data:t})}},bd52:function(t,e,a){"use strict";a.d(e,"l",(function(){return l})),a.d(e,"n",(function(){return r})),a.d(e,"h",(function(){return o})),a.d(e,"i",(function(){return s})),a.d(e,"o",(function(){return i})),a.d(e,"e",(function(){return u})),a.d(e,"s",(function(){return c})),a.d(e,"t",(function(){return m})),a.d(e,"a",(function(){return p})),a.d(e,"A",(function(){return d})),a.d(e,"g",(function(){return f})),a.d(e,"k",(function(){return b})),a.d(e,"w",(function(){return _})),a.d(e,"z",(function(){return v})),a.d(e,"f",(function(){return y})),a.d(e,"x",(function(){return h})),a.d(e,"c",(function(){return g})),a.d(e,"b",(function(){return I})),a.d(e,"v",(function(){return w})),a.d(e,"y",(function(){return k})),a.d(e,"j",(function(){return N})),a.d(e,"q",(function(){return x})),a.d(e,"B",(function(){return O})),a.d(e,"m",(function(){return j})),a.d(e,"r",(function(){return P})),a.d(e,"p",(function(){return S})),a.d(e,"d",(function(){return q})),a.d(e,"u",(function(){return D}));var n=a("b775");function l(t){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/list",method:"get",params:t})}function r(t){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/listBySlippageStatus3",method:"get",params:t})}function o(t){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/byLoanId/"+t,method:"get"})}function s(){return Object(n["a"])({url:"/bank_account/bank_account/bank?pageSize=30",method:"get"})}function i(t){return Object(n["a"])({url:"/loan_compensation/loan_compensation/detail",method:"get",params:t})}function u(t){return Object(n["a"])({url:"/vw_car/vw_car/"+t,method:"get"})}function c(t){return Object(n["a"])({url:"/loan_reminder/loan_reminder/list",method:"get",params:t})}function m(t){return Object(n["a"])({url:"/loan_reminder/loan_reminder",method:"post",data:t})}function p(t){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan",method:"post",data:t})}function d(t){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan",method:"put",data:t})}function f(t){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/"+t,method:"delete"})}function b(t,e){return Object(n["a"])({url:"/vw_customer_info/vw_customer_info/"+t+"?applyNo="+e,method:"get"})}function _(t){return Object(n["a"])({url:"/loan_settle/loan_settle/detail",method:"get",params:t})}function v(t){return Object(n["a"])({url:"/trial_balance/trial_balance/submit",method:"post",data:t})}function y(t){return Object(n["a"])({url:"/trial_balance/trial_balance/submitdc",method:"post",data:t})}function h(t){return Object(n["a"])({url:"/loan_settle/loan_settle",method:"post",data:t})}function g(t,e){return Object(n["a"])({url:"/loan_settle/loan_settle/process",method:"post",data:{loanId:t,status:e}})}function I(t){return Object(n["a"])({url:"/loan_compensation/loan_compensation/initiate",method:"post",data:t})}function w(t){return Object(n["a"])({url:"/loan_compensation/loan_compensation",method:"put",data:t})}function k(t){return Object(n["a"])({url:"/loan_settle/loan_settle",method:"put",data:t})}function N(t){return Object(n["a"])({url:"/system/user/listByRoleId",method:"get",params:{roleId:t}})}function x(t){return Object(n["a"])({url:"/loan_list/loan_list/batchUpdateUrgeUser",method:"put",data:t})}function O(t){return Object(n["a"])({url:"/loan_list/loan_list",method:"put",data:t})}function j(t){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/extension_list",method:"get",params:t})}function P(t){return Object(n["a"])({url:"/loan_extension/loan_extension/extension_detail",method:"get",params:{loan_id:t}})}function S(t){return Object(n["a"])({url:"/loan_extension/loan_extension/approve",method:"put",data:t})}function q(t){return Object(n["a"])({url:"/loan_list/loan_list/batchAssignPetitionUser",method:"put",data:t})}function D(t){return Object(n["a"])({url:"/loan_list/loan_list/revokePetitionUser/".concat(t),method:"put"})}},d6fd:function(t,e,a){"use strict";a("1791")},dea3:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:t.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.customerName,callback:function(e){t.$set(t.queryParams,"customerName",e)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"车牌号",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.plateNo,callback:function(e){t.$set(t.queryParams,"plateNo",e)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"jgName"}},[a("el-input",{attrs:{placeholder:"录单渠道名称",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.jgName,callback:function(e){t.$set(t.queryParams,"jgName",e)},expression:"queryParams.jgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"partnerId"}},[a("el-select",{attrs:{placeholder:"放款银行",clearable:""},model:{value:t.queryParams.partnerId,callback:function(e){t.$set(t.queryParams,"partnerId",e)},expression:"queryParams.partnerId"}},t._l(t.bankList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"status"}},[a("el-select",{attrs:{placeholder:"审批状态",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.status,callback:function(e){t.$set(t.queryParams,"status",e)},expression:"queryParams.status"}},[a("el-option",{attrs:{label:"待审批",value:"1"}}),a("el-option",{attrs:{label:"审批通过",value:"2"}}),a("el-option",{attrs:{label:"审批拒绝",value:"3"}})],1)],1),a("el-form-item",{attrs:{label:"全额时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.queryParams.fullTime,callback:function(e){t.$set(t.queryParams,"fullTime",e)},expression:"queryParams.fullTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:t.handleQuery}},[t._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:t.resetQuery}},[t._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.vw_account_loanList,"row-key":"id"}},[a("el-table-column",{attrs:{label:"贷款人姓名",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.openUserInfo({customerId:e.row.compensateDetail.customerId,applyId:e.row.compensateDetail.applyId})}}},[t._v(" "+t._s(e.row.compensateDetail.customerName)+" ")])]}}])}),a("el-table-column",{attrs:{label:"出单渠道",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.compensateDetail.orgName))])]}}])}),a("el-table-column",{attrs:{label:"业务员",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.compensateDetail.salesman))])]}}])}),a("el-table-column",{attrs:{label:"车牌号",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.openCarInfo(e.row.compensateDetail.plateNo)}}},[t._v(t._s(e.row.compensateDetail.plateNo))])]}}])}),a("el-table-column",{attrs:{label:"车辆位置",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.compensateDetail.carDetailAddress))])]}}])}),a("el-table-column",{attrs:{label:"欠款总金额",align:"center",prop:"totalMoney"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.compensateDetail.totalMoney||" "))])]}}])}),a("el-table-column",{attrs:{label:"申请分期总金额",align:"center",prop:"applyAmount"}}),a("el-table-column",{attrs:{label:"分期期数",align:"center",prop:"periodCount"}}),a("el-table-column",{attrs:{label:"每期金额",align:"center",prop:"billAmount"}}),a("el-table-column",{attrs:{label:"还款日",align:"center",prop:"repayDay"}}),a("el-table-column",{attrs:{label:"尾款余额",align:"center",prop:"tailAmount"}}),a("el-table-column",{attrs:{label:"尾款归还日",align:"center",prop:"tailPayTime"}}),a("el-table-column",{attrs:{label:"审批状态",align:"center",prop:"status"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(1===e.row.status?"待审批":2===e.row.status?"审批通过":3===e.row.status?"审批拒绝":" "))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.status?a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return t.handleAudit(e.row)}}},[t._v("审批")]):t._e(),2==e.row.status||3==e.row.status?a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return t.handleAudit(e.row,!0)}}},[t._v("查看")]):t._e()]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),a("el-dialog",{staticClass:"dialogBox",attrs:{title:t.form._readonly?"详情":"审批",visible:t.open,width:"800px"},on:{"update:visible":function(e){t.open=e},close:t.cancel}},[a("el-descriptions",{staticStyle:{"margin-bottom":"20px"},attrs:{column:2,border:""}},[a("el-descriptions-item",{attrs:{label:"贷款人"}},[a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.openUserInfo({customerId:t.form.customerId,applyId:t.form.applyId})}}},[t._v(" "+t._s(t.form.customerName)+" ")])],1),a("el-descriptions-item",{attrs:{label:"车牌号"}},[a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.openCarInfo(t.form.plateNo)}}},[t._v(t._s(t.form.plateNo))])],1),a("el-descriptions-item",{attrs:{label:"出单渠道"}},[t._v(t._s(t.form.jgName||""))]),a("el-descriptions-item",{attrs:{label:"申请分期总金额"}},[t._v(t._s(t.form.applyAmount||""))]),a("el-descriptions-item",{attrs:{label:"分期期数"}},[t._v(t._s(t.form.periodCount||""))]),a("el-descriptions-item",{attrs:{label:"每期金额"}},[t._v(t._s(t.form.billAmount||""))]),a("el-descriptions-item",{attrs:{label:"还款日"}},[t._v(t._s(t.form.repayDay||""))]),a("el-descriptions-item",{attrs:{label:"尾款余额"}},[t._v(t._s(t.form.tailAmount||""))]),a("el-descriptions-item",{attrs:{label:"尾款归还日"}},[t._v(t._s(t.form.tailPayTime||""))])],1),a("el-form",{ref:"form",attrs:{model:t.form,"label-width":"100px"}},[t.form._readonly&&2===t.form.status?[a("div",{staticStyle:{color:"#67c23a","font-size":"18px","margin-bottom":"16px"}},[t._v("已同意")])]:t.form._readonly&&3===t.form.status?[a("div",{staticStyle:{color:"#f56c6c","font-size":"18px","margin-bottom":"16px"}},[t._v("已拒绝")])]:t._e(),t.form._readonly?t._e():[a("el-form-item",{attrs:{label:"审批状态",prop:"status"}},[a("el-radio-group",{model:{value:t.form.status,callback:function(e){t.$set(t.form,"status",e)},expression:"form.status"}},[a("el-radio",{attrs:{label:2}},[t._v("审批通过")]),a("el-radio",{attrs:{label:3}},[t._v("审批拒绝")])],1)],1),3==t.form.status?a("el-form-item",{attrs:{label:"拒绝理由",prop:"reason"}},[a("el-input",{attrs:{placeholder:"请输入拒绝理由"},model:{value:t.form.reason,callback:function(e){t.$set(t.form,"reason",e)},expression:"form.reason"}})],1):t._e()]],2),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t.form._readonly?t._e():a("el-button",{on:{click:t.cancel}},[t._v("取 消")]),t.form._readonly?t._e():a("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确 定")]),t.form._readonly?a("el-button",{on:{click:t.cancel}},[t._v("关 闭")]):t._e()],1)],1),a("userInfo",{ref:"userInfo",attrs:{visible:t.lenderShow,title:"贷款人信息",customerInfo:t.customerInfo},on:{"update:visible":function(e){t.lenderShow=e}}}),a("carInfo",{ref:"carInfo",attrs:{visible:t.carInfoVisible,title:"车辆信息",plateNo:t.plateNo,permission:"2"},on:{"update:visible":function(e){t.carInfoVisible=e}}})],1)},l=[],r=a("99c9"),o=a("2eca"),s=a("0f5f"),i={name:"Vw_account_loan",components:{userInfo:o["a"],carInfo:s["a"]},data:function(){return{loading:!1,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,vw_account_loanList:[],open:!1,form:{},queryParams:{pageNum:1,pageSize:15,customerName:null,plateNo:null,jgName:null,partnerId:null,status:null,fullTime:null,startTime:null,endTime:null},bankList:[{value:"**********",label:"苏银金租"},{value:"**********",label:"浙商银行"},{value:"**********",label:"中关村银行"},{value:"**********",label:"蓝海银行"},{value:"**********",label:"华瑞银行"},{value:"**********",label:"皖新租赁"}],customerInfo:{},lenderShow:!1,plateNo:"",carInfoVisible:!1}},created:function(){this.getList()},methods:{handleQuery:function(){this.queryParams.fullTime&&(this.queryParams.startTime=this.queryParams.fullTime[0],this.queryParams.endTime=this.queryParams.fullTime[1]),this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.customerName=null,this.queryParams.plateNo=null,this.queryParams.jgName=null,this.queryParams.partnerId=null,this.queryParams.status=null,this.queryParams.fullTime=null,this.queryParams.startTime=null,this.queryParams.endTime=null,this.handleQuery()},getList:function(){var t=this;this.loading=!0,Object(r["b"])(this.queryParams).then((function(e){t.vw_account_loanList=e.rows,t.total=e.total,t.loading=!1}))},handleAudit:function(t){var e=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.form={id:t.id,status:t.status,reason:t.reason,customerName:t.compensateDetail&&t.compensateDetail.customerName,customerId:t.compensateDetail&&t.compensateDetail.customerId,applyId:t.compensateDetail&&t.compensateDetail.applyId,plateNo:t.compensateDetail&&t.compensateDetail.plateNo,jgName:t.compensateDetail&&t.compensateDetail.orgName,bankName:t.compensateDetail&&t.compensateDetail.bankName,applyAmount:t.applyAmount,periodCount:t.periodCount,billAmount:t.billAmount,repayDay:t.repayDay,tailAmount:t.tailAmount,tailPayTime:t.tailPayTime,_readonly:!!a},this.$nextTick((function(){e.open=!0}))},cancel:function(){var t=this;this.open=!1,this.$nextTick((function(){t.form={}}))},submitForm:function(){var t=this;3===this.form.status||2===this.form.status?Object(r["c"])(this.form).then((function(e){t.$modal.msgSuccess("审批成功"),t.cancel(),t.getList()})):this.$modal.msgError("请选择审批状态")},openUserInfo:function(t){this.customerInfo=t,this.lenderShow=!0},openCarInfo:function(t){this.plateNo=t,this.carInfoVisible=!0}}},u=i,c=a("2877"),m=Object(c["a"])(u,n,l,!1,null,null,null);e["default"]=m.exports}}]);