(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-64ecc772"],{"0f5f":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.carInfo.plateNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车架号"}},[e._v(e._s(e.carInfo.identityNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"发动机号"}},[e._v(e._s(e.carInfo.engineNumber||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(e._s(e.carStatusLabel))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆位置"}},[e._v(e._s(e.carInfo.carAddress||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(e._s(e.gpsStatusLabel))]):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看车牌照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"行驶证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看行驶证照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"登记证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看登记证照片")])],1):e._e()],1)],1)},n=[],r=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),a("bd52")),o={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var e=this,t=this.carStatusList.find((function(t){return t.value===e.carInfo.carStatus}));return t?t.label:"未知状态"},gpsStatusLabel:function(){var e=this,t=this.gpsStatusList.find((function(t){return t.value===e.carInfo.gpsStatus}));return t?t.label:"未知状态"}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,e&&this.plateNo&&Object(r["e"])(this.plateNo).then((function(e){t.carInfo=e.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},s=o,i=a("2877"),u=Object(i["a"])(s,l,n,!1,null,null,null);t["a"]=u.exports},1791:function(e,t,a){},"2d47":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"请输入车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"jgName"}},[a("el-input",{attrs:{placeholder:"录单渠道",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jgName,callback:function(t){e.$set(e.queryParams,"jgName",t)},expression:"queryParams.jgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"garageName"}},[a("el-input",{attrs:{placeholder:"车库名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.garageName,callback:function(t){e.$set(e.queryParams,"garageName",t)},expression:"queryParams.garageName"}})],1),a("el-form-item",{attrs:{label:"",prop:"keyStatus"}},[a("el-select",{attrs:{placeholder:"请选择钥匙状态",clearable:""},model:{value:e.queryParams.keyStatus,callback:function(t){e.$set(e.queryParams,"keyStatus",t)},expression:"queryParams.keyStatus"}},e._l(e.keyStatusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"collectionMethod"}},[a("el-select",{attrs:{placeholder:"请选择收车方式",clearable:""},model:{value:e.queryParams.collectionMethod,callback:function(t){e.$set(e.queryParams,"collectionMethod",t)},expression:"queryParams.collectionMethod"}},e._l(e.collectionList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"teamName"}},[a("el-input",{attrs:{placeholder:"找车团队",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.teamName,callback:function(t){e.$set(e.queryParams,"teamName",t)},expression:"queryParams.teamName"}})],1),a("el-form-item",{attrs:{label:"入库时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.queryParams.originallyTime,callback:function(t){e.$set(e.queryParams,"originallyTime",t)},expression:"queryParams.originallyTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vw_car_warehousingList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"customerName"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openUserInfo({customerId:t.row.customerId,applyId:t.row.applyId})}}},[e._v(" "+e._s(t.row.customerName)+" ")])]}}])}),a("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"jgName"}}),a("el-table-column",{attrs:{label:"业务员",align:"center",prop:"nickName"}}),a("el-table-column",{attrs:{label:"车牌号",align:"center",prop:"plateNo"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openCarInfo(t.row.plateNo)}}},[e._v(e._s(t.row.plateNo))])]}}])}),a("el-table-column",{attrs:{label:"车辆状态",align:"center",prop:"libraryStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(1==t.row.libraryStatus?"入库":2==t.row.libraryStatus?"出库":"未知"))])]}}])}),a("el-table-column",{attrs:{label:"GPS状态",align:"center",prop:"gpsStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(" "+e._s(1==t.row.gpsStatus?"部分拆除":2==t.row.gpsStatus?"全部拆除":3==t.row.gpsStatus?"GPS正常":"停车30天以上")+" ")])]}}])}),a("el-table-column",{attrs:{label:"车库名称",align:"center",prop:"garageName"}}),a("el-table-column",{attrs:{label:"停车费",align:"center",prop:"parkingFee"}}),a("el-table-column",{attrs:{label:"出库状态",align:"center",prop:"outbounStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(1==t.row.outbounStatus?"归还车主":2==t.row.outbounStatus?"协商卖车":"法院扣车"))])]}}])}),a("el-table-column",{attrs:{label:"是否事故车",align:"center",prop:"outbounStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(1==t.row.outbounStatus?"是":"否"))])]}}])}),a("el-table-column",{attrs:{label:"钥匙状态",align:"center",prop:"keyStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(1==t.row.keyStatus?"已邮寄":2==t.row.keyStatus?"已收回":"未归还"))])]}}])}),a("el-table-column",{attrs:{label:"收车方式",align:"center",prop:"collectionMethod"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(1==t.row.collectionMethod?"主动交车":2==t.row.collectionMethod?"钥匙开车":"板车拖车"))])]}}])}),a("el-table-column",{attrs:{label:"收车位置",align:"center",prop:"carDetailAddress"}}),a("el-table-column",{attrs:{label:"找车团队",align:"center",prop:"teamName"}}),a("el-table-column",{attrs:{label:"找车佣金",align:"center",prop:"locatingCommission"}}),a("el-table-column",{attrs:{label:"入库时间",align:"center",prop:"inboundTime",width:"180"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["garage:garage:edit"],expression:"['garage:garage:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.warehousingChange(t.row)}}},[e._v(" "+e._s(1==t.row.libraryStatus?"出库":"入库")+" ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:"出库",visible:e.outbounopen,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.outbounopen=t}}},[a("el-form",{ref:"formNew",attrs:{model:e.formNew,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"停车费",prop:"parkingFee"}},[a("el-input",{attrs:{placeholder:"请输入停车费"},model:{value:e.formNew.parkingFee,callback:function(t){e.$set(e.formNew,"parkingFee",t)},expression:"formNew.parkingFee"}})],1),a("el-form-item",{attrs:{label:"出库状态",prop:"outbounStatus"}},[a("el-select",{attrs:{placeholder:"出库状态",clearable:""},model:{value:e.formNew.outbounStatus,callback:function(t){e.$set(e.formNew,"outbounStatus",t)},expression:"formNew.outbounStatus"}},e._l(e.outbounList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),2==e.formNew.outbounStatus?a("el-form-item",{attrs:{label:"卖车价格",prop:"sellingFares"}},[a("el-input",{attrs:{placeholder:"卖车价格"},model:{value:e.formNew.sellingFares,callback:function(t){e.$set(e.formNew,"sellingFares",t)},expression:"formNew.sellingFares"}})],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("出 库")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"入库",visible:e.storeopen,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.storeopen=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"是否是事故车",prop:"accident_flag"}},[a("el-select",{attrs:{placeholder:"是否是事故车",clearable:""},model:{value:e.form.accidentFlag,callback:function(t){e.$set(e.form,"accidentFlag",t)},expression:"form.accidentFlag"}},e._l(e.accidentList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitAccident}},[e._v("入 库")]),a("el-button",{on:{click:e.cancelAccident}},[e._v("取 消")])],1)],1),a("userInfo",{ref:"userInfo",attrs:{visible:e.userInfoVisible,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.userInfoVisible=t}}}),a("carInfo",{ref:"carInfo",attrs:{visible:e.carInfoVisible,title:"车辆信息",plateNo:e.plateNo,permission:"2"},on:{"update:visible":function(t){e.carInfoVisible=t}}})],1)},n=[],r=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("b775"));function o(e){return Object(r["a"])({url:"/vw_car_warehousing/vw_car_warehousing/list",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/car_warehousing/car_warehousing",method:"put",data:e})}var i=a("2eca"),u=a("0f5f"),c={name:"Vw_car_warehousing",components:{userInfo:i["a"],carInfo:u["a"]},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,vw_car_warehousingList:[],title:"",outbounopen:!1,storeopen:!1,queryParams:{pageNum:1,pageSize:15,customerName:null,plateNo:null,jgName:null,garageName:null,keyStatus:null,collectionMethod:null,teamName:null,originallyTime:null,startTime:"",endTime:""},form:{},formNew:{},keyStatusList:[{label:"已邮寄",value:1},{label:"已收回",value:2},{label:"已归还",value:3}],collectionList:[{label:"主动交车",value:1},{label:"钥匙",value:2},{label:"板车",value:3}],gpsList:[{label:"部分拆除",value:1},{label:"全部拆除",value:2},{label:"GPS正常",value:3},{label:"停车30天以上",value:4}],outbounList:[{label:"归还车主",value:1},{label:"协商卖车",value:2},{label:"法院扣车",value:3}],accidentList:[{label:"事故车",value:1},{label:"非事故车",value:2}],customerInfo:{customerId:"",applyId:""},userInfoVisible:!1,plateNo:"",carInfoVisible:!1}},created:function(){this.getList()},methods:{submitAccident:function(){var e=this;s(this.form).then((function(t){e.$modal.msgSuccess("提交成功"),e.storeopen=!1,e.getList()}))},handleChange:function(e){this.queryParams.jgName=e},getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.vw_car_warehousingList=t.rows,e.total=t.total,e.loading=!1}))},cancelAccident:function(){this.outbounopen=!1,this.form={}},cancel:function(){this.open=!1,this.reset()},reset:function(){this.formNew={parkingFee:null,outbounStatus:null,sellingFares:null,applyNo:null,libraryStatus:null}},handleQuery:function(){this.queryParams.originallyTime&&(this.queryParams.startTime=this.queryParams.originallyTime[0],this.queryParams.endTime=this.queryParams.originallyTime[1]),this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.customerName=null,this.queryParams.plateNo=null,this.queryParams.jgName=null,this.queryParams.garageName=null,this.queryParams.keyStatus=null,this.queryParams.collectionMethod=null,this.queryParams.teamName=null,this.queryParams.originallyTime=null,this.queryParams.startTime=null,this.queryParams.endTime=null,this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},submitForm:function(){var e=this;2!=this.formNew.outbounStatus||this.formNew.sellingFares?s(this.formNew).then((function(t){e.$modal.msgSuccess("提交成功"),e.outbounopen=!1,e.getList()})):this.$modal.msgError("卖车价格不能为空")},warehousingChange:function(e){var t={applyNo:e.applyNo,libraryStatus:1==e.libraryStatus?2:1};1==e.libraryStatus?(this.formNew.applyNo=e.applyNo,this.formNew.libraryStatus=t.libraryStatus,this.outbounopen=!0):(this.form.applyNo=e.applyNo,this.form.libraryStatus=t.libraryStatus,this.storeopen=!0)},openUserInfo:function(e){this.customerInfo=e,this.userInfoVisible=!0},openCarInfo:function(e){this.plateNo=e,this.carInfoVisible=!0}}},m=c,p=a("2877"),d=Object(p["a"])(m,l,n,!1,null,null,null);t["default"]=d.exports},"2eca":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[e._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},n=[],r=a("bd52"),o={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(r["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},s=o,i=(a("d6fd"),a("2877")),u=Object(i["a"])(s,l,n,!1,null,"8a3d4978",null);t["a"]=u.exports},bd52:function(e,t,a){"use strict";a.d(t,"l",(function(){return n})),a.d(t,"n",(function(){return r})),a.d(t,"h",(function(){return o})),a.d(t,"i",(function(){return s})),a.d(t,"o",(function(){return i})),a.d(t,"e",(function(){return u})),a.d(t,"s",(function(){return c})),a.d(t,"t",(function(){return m})),a.d(t,"a",(function(){return p})),a.d(t,"A",(function(){return d})),a.d(t,"g",(function(){return f})),a.d(t,"k",(function(){return b})),a.d(t,"w",(function(){return h})),a.d(t,"z",(function(){return _})),a.d(t,"f",(function(){return y})),a.d(t,"x",(function(){return g})),a.d(t,"c",(function(){return v})),a.d(t,"b",(function(){return w})),a.d(t,"v",(function(){return N})),a.d(t,"y",(function(){return k})),a.d(t,"j",(function(){return S})),a.d(t,"q",(function(){return I})),a.d(t,"B",(function(){return P})),a.d(t,"m",(function(){return x})),a.d(t,"r",(function(){return q})),a.d(t,"p",(function(){return O})),a.d(t,"d",(function(){return j})),a.d(t,"u",(function(){return L}));var l=a("b775");function n(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/list",method:"get",params:e})}function r(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/listBySlippageStatus3",method:"get",params:e})}function o(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/byLoanId/"+e,method:"get"})}function s(){return Object(l["a"])({url:"/bank_account/bank_account/bank?pageSize=30",method:"get"})}function i(e){return Object(l["a"])({url:"/loan_compensation/loan_compensation/detail",method:"get",params:e})}function u(e){return Object(l["a"])({url:"/vw_car/vw_car/"+e,method:"get"})}function c(e){return Object(l["a"])({url:"/loan_reminder/loan_reminder/list",method:"get",params:e})}function m(e){return Object(l["a"])({url:"/loan_reminder/loan_reminder",method:"post",data:e})}function p(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan",method:"post",data:e})}function d(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan",method:"put",data:e})}function f(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/"+e,method:"delete"})}function b(e,t){return Object(l["a"])({url:"/vw_customer_info/vw_customer_info/"+e+"?applyNo="+t,method:"get"})}function h(e){return Object(l["a"])({url:"/loan_settle/loan_settle/detail",method:"get",params:e})}function _(e){return Object(l["a"])({url:"/trial_balance/trial_balance/submit",method:"post",data:e})}function y(e){return Object(l["a"])({url:"/trial_balance/trial_balance/submitdc",method:"post",data:e})}function g(e){return Object(l["a"])({url:"/loan_settle/loan_settle",method:"post",data:e})}function v(e,t){return Object(l["a"])({url:"/loan_settle/loan_settle/process",method:"post",data:{loanId:e,status:t}})}function w(e){return Object(l["a"])({url:"/loan_compensation/loan_compensation/initiate",method:"post",data:e})}function N(e){return Object(l["a"])({url:"/loan_compensation/loan_compensation",method:"put",data:e})}function k(e){return Object(l["a"])({url:"/loan_settle/loan_settle",method:"put",data:e})}function S(e){return Object(l["a"])({url:"/system/user/listByRoleId",method:"get",params:{roleId:e}})}function I(e){return Object(l["a"])({url:"/loan_list/loan_list/batchUpdateUrgeUser",method:"put",data:e})}function P(e){return Object(l["a"])({url:"/loan_list/loan_list",method:"put",data:e})}function x(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/extension_list",method:"get",params:e})}function q(e){return Object(l["a"])({url:"/loan_extension/loan_extension/extension_detail",method:"get",params:{loan_id:e}})}function O(e){return Object(l["a"])({url:"/loan_extension/loan_extension/approve",method:"put",data:e})}function j(e){return Object(l["a"])({url:"/loan_list/loan_list/batchAssignPetitionUser",method:"put",data:e})}function L(e){return Object(l["a"])({url:"/loan_list/loan_list/revokePetitionUser/".concat(e),method:"put"})}},d6fd:function(e,t,a){"use strict";a("1791")}}]);