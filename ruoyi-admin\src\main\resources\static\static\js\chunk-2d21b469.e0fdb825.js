(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d21b469"],{bea1:function(e,a,t){"use strict";t.r(a);var n=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"app-container"},[t("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[t("el-form-item",{attrs:{label:"找车团队",prop:"teamId"}},[t("el-input",{attrs:{placeholder:"请输入找车团队",clearable:""},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleQuery(a)}},model:{value:e.queryParams.teamId,callback:function(a){e.$set(e.queryParams,"teamId",a)},expression:"queryParams.teamId"}})],1),t("el-form-item",{attrs:{label:"车库id",prop:"garageId"}},[t("el-input",{attrs:{placeholder:"请输入车库id",clearable:""},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleQuery(a)}},model:{value:e.queryParams.garageId,callback:function(a){e.$set(e.queryParams,"garageId",a)},expression:"queryParams.garageId"}})],1),t("el-form-item",{attrs:{label:"入库时间",prop:"inboundTime"}},[t("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择入库时间"},model:{value:e.queryParams.inboundTime,callback:function(a){e.$set(e.queryParams,"inboundTime",a)},expression:"queryParams.inboundTime"}})],1),t("el-form-item",{attrs:{label:"收车方式：1-主动交车，2-钥匙开车，3-板车拖车",prop:"collectionMethod"}},[t("el-input",{attrs:{placeholder:"请输入收车方式：1-主动交车，2-钥匙开车，3-板车拖车",clearable:""},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleQuery(a)}},model:{value:e.queryParams.collectionMethod,callback:function(a){e.$set(e.queryParams,"collectionMethod",a)},expression:"queryParams.collectionMethod"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),t("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),t("el-row",{staticClass:"mb8",attrs:{gutter:10}},[t("el-col",{attrs:{span:1.5}},[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_warehousing:car_warehousing:add"],expression:"['car_warehousing:car_warehousing:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),t("el-col",{attrs:{span:1.5}},[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_warehousing:car_warehousing:edit"],expression:"['car_warehousing:car_warehousing:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),t("el-col",{attrs:{span:1.5}},[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_warehousing:car_warehousing:remove"],expression:"['car_warehousing:car_warehousing:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),t("el-col",{attrs:{span:1.5}},[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_warehousing:car_warehousing:export"],expression:"['car_warehousing:car_warehousing:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),t("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(a){e.showSearch=a},"update:show-search":function(a){e.showSearch=a},queryTable:e.getList}})],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.car_warehousingList},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),t("el-table-column",{attrs:{label:"${comment}",align:"center",prop:"id"}}),t("el-table-column",{attrs:{label:"入库时间",align:"center",prop:"inboundTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e.parseTime(a.row.inboundTime,"{y}-{m}-{d}")))])]}}])}),t("el-table-column",{attrs:{label:"找车佣金",align:"center",prop:"locatingCommission"}}),t("el-table-column",{attrs:{label:"钥匙状态：1-已邮寄，2-已收回，3-未归还",align:"center",prop:"keyStatus"}}),t("el-table-column",{attrs:{label:"收车方式：1-主动交车，2-钥匙开车，3-板车拖车",align:"center",prop:"collectionMethod"}}),t("el-table-column",{attrs:{label:"收车位置",align:"center",prop:"carLocation"}}),t("el-table-column",{attrs:{label:"订单状态，0-发起，1-已分配，2-已完成，3-未完成，4-已撤销",align:"center",prop:"status"}}),t("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_warehousing:car_warehousing:edit"],expression:"['car_warehousing:car_warehousing:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(t){return e.handleUpdate(a.row)}}},[e._v("修改")]),t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["car_warehousing:car_warehousing:remove"],expression:"['car_warehousing:car_warehousing:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(t){return e.handleDelete(a.row)}}},[e._v("删除")])]}}])})],1),t("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(a){return e.$set(e.queryParams,"pageNum",a)},"update:limit":function(a){return e.$set(e.queryParams,"pageSize",a)},pagination:e.getList}}),t("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(a){e.open=a}}},[t("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}}),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),t("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],i=t("5530"),l=(t("d81d"),t("d3b7"),t("0643"),t("a573"),t("b775"));function o(e){return Object(l["a"])({url:"/car_warehousing/car_warehousing/list",method:"get",params:e})}function s(e){return Object(l["a"])({url:"/car_warehousing/car_warehousing/"+e,method:"get"})}function u(e){return Object(l["a"])({url:"/car_warehousing/car_warehousing",method:"post",data:e})}function c(e){return Object(l["a"])({url:"/car_warehousing/car_warehousing",method:"put",data:e})}function d(e){return Object(l["a"])({url:"/car_warehousing/car_warehousing/"+e,method:"delete"})}var m={name:"Car_warehousing",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,car_warehousingList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,teamId:null,garageId:null,inboundTime:null,keyStatus:null,collectionMethod:null},form:{},rules:{}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(a){e.car_warehousingList=a.rows,e.total=a.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,createBy:null,createDate:null,updateBy:null,updateDate:null,applyNo:null,teamId:null,garageId:null,libraryStatus:null,inboundTime:null,outboundTime:null,locatingCommission:null,keyStatus:null,collectionMethod:null,carLocation:null,status:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加车辆出入库"},handleUpdate:function(e){var a=this;this.reset();var t=e.id||this.ids;s(t).then((function(e){a.form=e.data,a.open=!0,a.title="修改车辆出入库"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(a){a&&(null!=e.form.id?c(e.form).then((function(a){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(a){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var a=this,t=e.id||this.ids;this.$modal.confirm('是否确认删除车辆出入库编号为"'+t+'"的数据项？').then((function(){return d(t)})).then((function(){a.getList(),a.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("car_warehousing/car_warehousing/export",Object(i["a"])({},this.queryParams),"car_warehousing_".concat((new Date).getTime(),".xlsx"))}}},h=m,p=t("2877"),g=Object(p["a"])(h,n,r,!1,null,null,null);a["default"]=g.exports}}]);