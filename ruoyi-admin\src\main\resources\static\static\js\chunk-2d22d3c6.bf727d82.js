(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d22d3c6"],{f743:function(e,a,l){"use strict";l.r(a);var t=function(){var e=this,a=e.$createElement,l=e._self._c||a;return l("div",{staticClass:"app-container"},[l("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"90px"}},[l("el-form-item",{attrs:{label:""}},[l("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},model:{value:e.queryParams.customerName,callback:function(a){e.$set(e.queryParams,"customerName",a)},expression:"queryParams.customerName"}})],1),l("el-form-item",{attrs:{label:""}},[l("el-input",{attrs:{placeholder:"贷款人身份证",clearable:""},model:{value:e.queryParams.certId,callback:function(a){e.$set(e.queryParams,"certId",a)},expression:"queryParams.certId"}})],1),l("el-form-item",{attrs:{label:""}},[l("el-input",{attrs:{placeholder:"车牌号码",clearable:""},model:{value:e.queryParams.plateNo,callback:function(a){e.$set(e.queryParams,"plateNo",a)},expression:"queryParams.plateNo"}})],1),l("el-form-item",{attrs:{label:""}},[l("el-input",{attrs:{placeholder:"业务员",clearable:""},model:{value:e.queryParams.salesman,callback:function(a){e.$set(e.queryParams,"salesman",a)},expression:"queryParams.salesman"}})],1),l("el-form-item",{attrs:{label:""}},[l("el-input",{attrs:{placeholder:"录单渠道名称",clearable:""},model:{value:e.queryParams.jgName,callback:function(a){e.$set(e.queryParams,"jgName",a)},expression:"queryParams.jgName"}})],1),l("el-form-item",{attrs:{label:""}},[l("el-select",{attrs:{placeholder:"还款状态",clearable:""},model:{value:e.queryParams.repaymentStatus,callback:function(a){e.$set(e.queryParams,"repaymentStatus",a)},expression:"queryParams.repaymentStatus"}},e._l(e.repaymentList,(function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),l("el-form-item",{attrs:{label:""}},[l("el-select",{attrs:{placeholder:"放款银行",clearable:""},model:{value:e.queryParams.bank,callback:function(a){e.$set(e.queryParams,"bank",a)},expression:"queryParams.bank"}},e._l(e.bankList,(function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),l("el-form-item",{attrs:{label:""}},[l("el-select",{attrs:{placeholder:"产品名称",clearable:""},model:{value:e.queryParams.productName,callback:function(a){e.$set(e.queryParams,"productName",a)},expression:"queryParams.productName"}},e._l(e.productList,(function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),l("el-form-item",{attrs:{label:"还款时间"}},[l("el-date-picker",{staticStyle:{width:"240px"},attrs:{type:"daterange","value-format":"yyyy-MM-dd","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.queryParams.repayTime,callback:function(a){e.$set(e.queryParams,"repayTime",a)},expression:"queryParams.repayTime"}})],1),l("el-form-item",[l("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),l("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vw_account_loanList,"row-key":"id"}},[l("el-table-column",{attrs:{label:"贷款人信息",align:"center",prop:"customerName"}}),l("el-table-column",{attrs:{label:"业务员",align:"center",prop:"salesman"}}),l("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"jgName"}}),l("el-table-column",{attrs:{label:"车牌号码",align:"center",prop:"plateNo"}}),l("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"bank"}}),l("el-table-column",{attrs:{label:"产品名称",align:"center",prop:"productName"}}),l("el-table-column",{attrs:{label:"剩余账单金额",align:"center",prop:"remainAmount"}}),l("el-table-column",{attrs:{label:"剩余代扣金额",align:"center",prop:"remainDeductAmount"}}),l("el-table-column",{attrs:{label:"代扣逾期金额",align:"center",prop:"deductOverdueAmount"}}),l("el-table-column",{attrs:{label:"代扣本期金额",align:"center",prop:"deductCurrentAmount"}}),l("el-table-column",{attrs:{label:"期数",align:"center",prop:"periods"}}),l("el-table-column",{attrs:{label:"还款日",align:"center",prop:"repayDate"}}),l("el-table-column",{attrs:{label:"还款类型",align:"center",prop:"repaymentType"}}),l("el-table-column",{attrs:{label:"还款状态",align:"center",prop:"repaymentStatus"}}),l("el-table-column",{attrs:{label:"实还金额",align:"center",prop:"actualRepayAmount"}}),l("el-table-column",{attrs:{label:"实际扣款时间",align:"center",prop:"actualRepayTime"}}),l("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"}},[[l("el-button",{attrs:{size:"mini",type:"text"}},[e._v("审批")])]],2)],1),l("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(a){return e.$set(e.queryParams,"pageNum",a)},"update:limit":function(a){return e.$set(e.queryParams,"pageSize",a)},pagination:e.getList}})],1)},r=[],s={name:"Vw_account_loan",data:function(){return{loading:!1,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,vw_account_loanList:[],open:!1,queryParams:{pageNum:1,pageSize:15,customerName:null,certId:null,plateNo:null,salesman:null,jgName:null,repaymentStatus:null,bank:null,productName:null,repayTime:null},repaymentList:[{label:"未还款",value:0},{label:"还款",value:1},{label:"部分还款",value:2},{label:"分期还款",value:3},{label:"协商卖车",value:4},{label:"法诉结清",value:5},{label:"法诉减免结清",value:6},{label:"拍卖回款",value:7},{label:"法院划扣",value:8},{label:"参与分配回款",value:9}],bankList:[{value:"**********",label:"苏银金租"},{value:"**********",label:"浙商银行"},{value:"**********",label:"中关村银行"},{value:"**********",label:"蓝海银行"},{value:"**********",label:"华瑞银行"},{value:"**********",label:"皖新租赁"}],productList:[]}},methods:{handleQuery:function(){this.queryParams.repayTime&&(this.queryParams.startTime=this.queryParams.repayTime[0],this.queryParams.endTime=this.queryParams.repayTime[1]),this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.customerName=null,this.queryParams.certId=null,this.queryParams.plateNo=null,this.queryParams.salesman=null,this.queryParams.jgName=null,this.queryParams.repaymentStatus=null,this.queryParams.bank=null,this.queryParams.productName=null,this.queryParams.repayTime=null,this.queryParams.startTime=null,this.queryParams.endTime=null,this.handleQuery()},getList:function(){this.loading=!0,this.vw_account_loanList=[],this.total=0,this.loading=!1}}},n=s,u=l("2877"),m=Object(u["a"])(n,t,r,!1,null,null,null);a["default"]=m.exports}}]);