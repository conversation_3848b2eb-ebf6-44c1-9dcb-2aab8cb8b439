(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a8de9f9a"],{"31c1":function(e,t,a){"use strict";a("91b6")},"6c9b":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入车库名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),a("el-form-item",{attrs:{label:"",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择车库状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.startStop,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"启用时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.queryParams.originallyTime,callback:function(t){e.$set(e.queryParams,"originallyTime",t)},expression:"queryParams.originallyTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["garage:garage:add"],expression:"['garage:garage:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["garage:garage:edit"],expression:"['garage:garage:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["garage:garage:remove"],expression:"['garage:garage:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.garageList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"车库名称",align:"center",prop:"name"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.status?a("span",{staticClass:"dict1"},[e._v("启用")]):e._e(),2==t.row.status?a("span",{staticClass:"dict2"},[e._v("禁用")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"位置",align:"center",prop:"position"}}),a("el-table-column",{attrs:{label:"管理人",align:"center",prop:"contacts"}}),a("el-table-column",{attrs:{label:"电话",align:"center",prop:"mobile"}}),a("el-table-column",{attrs:{label:"入库车辆数量",align:"center",prop:"num1"}}),a("el-table-column",{attrs:{label:"在库车辆数量",align:"center",prop:"num3"}}),a("el-table-column",{attrs:{label:"已出库数量",align:"center",prop:"num2"}}),a("el-table-column",{attrs:{label:"启用时间",align:"center",prop:"inboundTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.inboundTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["garage:garage:edit"],expression:"['garage:garage:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"车库名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入车库名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"管理人",prop:"contacts"}},[a("el-input",{attrs:{placeholder:"请输入管理人"},model:{value:e.form.contacts,callback:function(t){e.$set(e.form,"contacts",t)},expression:"form.contacts"}})],1),a("el-form-item",{attrs:{label:"电话",prop:"mobile"}},[a("el-input",{attrs:{placeholder:"请输入电话"},model:{value:e.form.mobile,callback:function(t){e.$set(e.form,"mobile",t)},expression:"form.mobile"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.startStop,(function(t){return a("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1),a("el-form-item",{attrs:{label:"入库数量",prop:"num1"}},[a("el-input",{attrs:{placeholder:"请输入入库数量"},model:{value:e.form.num1,callback:function(t){e.$set(e.form,"num1",t)},expression:"form.num1"}})],1),a("el-form-item",{attrs:{label:"出库数量",prop:"num2"}},[a("el-input",{attrs:{placeholder:"请输入出库数量"},model:{value:e.form.num2,callback:function(t){e.$set(e.form,"num2",t)},expression:"form.num2"}})],1),a("el-form-item",{attrs:{label:"在库数量",prop:"num3"}},[a("el-input",{attrs:{placeholder:"请输入在库数量"},model:{value:e.form.num3,callback:function(t){e.$set(e.form,"num3",t)},expression:"form.num3"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],n=a("5530"),i=(a("d81d"),a("b0c0"),a("d3b7"),a("0643"),a("a573"),a("b775"));function s(e){return Object(i["a"])({url:"/garage/garage/list",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/garage/garage/"+e,method:"get"})}function u(e){return Object(i["a"])({url:"/garage/garage",method:"post",data:e})}function m(e){return Object(i["a"])({url:"/garage/garage",method:"put",data:e})}function c(e){return Object(i["a"])({url:"/garage/garage/"+e,method:"delete"})}var d={name:"Garage",dicts:["sys_normal_disable"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,garageList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:15,name:null,status:null,originallyTime:null,startTime:"",endTime:""},form:{},rules:{},value:"停用",startStop:[{value:"1",label:"启用"},{value:"2",label:"禁用"}]}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,s(this.queryParams).then((function(t){e.garageList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,name:null,contacts:null,mobile:null,province:null,city:null,borough:null,address:null,status:null,lat:null,lng:null,num1:null,num2:null,num3:null,createBy:null,createDate:null,updateBy:null,updateDate:null,delFlag:null,startDate:null,endDate:null},this.resetForm("form")},handleQuery:function(){this.queryParams.originallyTime&&(this.queryParams.startTime=this.queryParams.originallyTime[0],this.queryParams.endTime=this.queryParams.originallyTime[1]),this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.name=null,this.queryParams.status=null,this.queryParams.originallyTime=null,this.queryParams.startTime=null,this.queryParams.endTime=null,this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加车库"},handleUpdate:function(e){var t=this,a=e.id||this.ids;o(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改车库"}))},modifyState:function(e){},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?m(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除车库编号为"'+a+'"的数据项？').then((function(){return c(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("garage/garage/export",Object(n["a"])({},this.queryParams),"garage_".concat((new Date).getTime(),".xlsx"))}}},p=d,g=(a("31c1"),a("2877")),h=Object(g["a"])(p,l,r,!1,null,null,null);t["default"]=h.exports},"91b6":function(e,t,a){}}]);