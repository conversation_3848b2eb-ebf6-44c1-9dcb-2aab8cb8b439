<template>
  <el-cascader
    v-model="proxyValue"
    :options="litigationStatusTree"
    :props="cascaderProps"
    :placeholder="placeholder"
    clearable
    @change="handleChange" />
</template>

<script>
export default {
  name: 'LitigationStatusCascader',
  props: {
    value: {
      type: [String, Array],
      default: '',
    },
    placeholder: {
      type: String,
      default: '法诉状态',
    },
  },
  data() {
    return {
      litigationStatusTree: [
        // 独立状态（不需要分类）
        { label: '暂不起诉', value: '暂不起诉' },
        { label: '撤案', value: '撤案' },
        {
          label: '立案前',
          value: '立案前',
          children: [
            { label: '准备资料', value: '准备资料' },
            { label: '已邮寄', value: '已邮寄' },
            { label: '待立案', value: '待立案' },
          ],
        },
        {
          label: '立案-判决',
          value: '立案-判决',
          children: [
            { label: '获取案件号', value: '获取案件号' },
            { label: '待出民初号', value: '待出民初号' },
            { label: '待开庭', value: '待开庭' },
            { label: '待出法院文书', value: '待出法院文书' },
          ],
        },
        {
          label: '判决-执行',
          value: '判决-执行',
          children: [
            { label: '待执行', value: '待执行' },
            { label: '待出申请书', value: '待出申请书' },
            { label: '已提交执行书', value: '已提交执行书' },
          ],
        },
        {
          label: '执行后',
          value: '执行后',
          children: [
            { label: '执行中', value: '执行中' },
            { label: '执行终本', value: '执行终本' },
            { label: '继续执行', value: '继续执行' },
            { label: '待送车', value: '待送车' },
            { label: '待法拍', value: '待法拍' },
          ],
        },
        {
          label: '结案',
          value: '结案',
          children: [
            { label: '法诉减免结清', value: '法诉减免结清' },
            { label: '法诉全额结清', value: '法诉全额结清' },
          ],
        },
      ],
      cascaderProps: {
        emitPath: false,
        value: 'value',
        label: 'label',
        children: 'children',
        // 只允许选择子节点（有children的父节点不可选）
        disabled: node => !!node.children && node.children.length > 0,
      },
    }
  },
  computed: {
    proxyValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
        this.$emit('update:value', val)
      },
    },
  },
  methods: {
    handleChange(val) {
      if (this.$listeners && this.$listeners.change) {
        this.$emit('change', val)
      }
    },
  },
}
</script>
