(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-06d24d5f"],{"4b64":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"cardID"}},[a("el-input",{attrs:{placeholder:"贷款人身份证号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.cardID,callback:function(t){e.$set(e.queryParams,"cardID",t)},expression:"queryParams.cardID"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"请输入车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"jgName"}},[a("el-cascader",{attrs:{placeholder:"录单渠道名称",options:e.jgNameList,props:{expandTrigger:"hover",label:"name",value:"id"}},on:{change:e.handleChange},model:{value:e.queryParams.jgName,callback:function(t){e.$set(e.queryParams,"jgName",t)},expression:"queryParams.jgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"lendingBank"}},[a("el-select",{attrs:{placeholder:"放款银行",clearable:""},model:{value:e.queryParams.lendingBank,callback:function(t){e.$set(e.queryParams,"lendingBank",t)},expression:"queryParams.lendingBank"}},e._l(e.lendingBankList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"clerk"}},[a("el-input",{attrs:{placeholder:"业务员",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.clerk,callback:function(t){e.$set(e.queryParams,"clerk",t)},expression:"queryParams.clerk"}})],1),a("el-form-item",{attrs:{label:"",prop:"applicant"}},[a("el-input",{attrs:{placeholder:"申请人",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.applicant,callback:function(t){e.$set(e.queryParams,"applicant",t)},expression:"queryParams.applicant"}})],1),a("el-form-item",{attrs:{label:"",prop:"paymentStatus"}},[a("el-select",{attrs:{placeholder:"打款状态",clearable:""},model:{value:e.queryParams.paymentStatus,callback:function(t){e.$set(e.queryParams,"paymentStatus",t)},expression:"queryParams.paymentStatus"}},e._l(e.paymentList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vm_car_orderList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"customerName"}}),a("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"jgName",width:"130"}}),a("el-table-column",{attrs:{label:"业务员",align:"center",prop:"nickName"}}),a("el-table-column",{attrs:{label:"车牌号",align:"center",prop:"plateNo",width:"130"}}),a("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"lendingBank",width:"130"}}),a("el-table-column",{attrs:{label:"总欠款",align:"center",prop:"lendingBank",width:"130"}}),a("el-table-column",{attrs:{label:"已还金额",align:"center",prop:"lendingBank",width:"100"}}),a("el-table-column",{attrs:{label:"剩余未还金额",align:"center",prop:"lendingBank",width:"100"}}),a("el-table-column",{attrs:{label:"风险金划扣金额",align:"center",prop:"lendingBank"}}),a("el-table-column",{attrs:{label:"账号类型",align:"center",prop:"lendingBank"}}),a("el-table-column",{attrs:{label:"退还登记日",align:"center",prop:"keyTime",width:"130"}}),a("el-table-column",{attrs:{label:"渠道转入金额",align:"center",prop:"lendingBank"}}),a("el-table-column",{attrs:{label:"返回日期",align:"center",prop:"keyTime",width:"130"}}),a("el-table-column",{attrs:{label:"广明借款金额",align:"center",prop:"lendingBank"}}),a("el-table-column",{attrs:{label:"归还日期",align:"center",prop:"keyTime",width:"130"}}),a("el-table-column",{attrs:{label:"科技借款金额",align:"center",prop:"lendingBank"}}),a("el-table-column",{attrs:{label:"借款还款日",align:"center",prop:"keyTime",width:"130"}}),a("el-table-column",{attrs:{label:"科技出资金额",align:"center",prop:"lendingBank"}}),a("el-table-column",{attrs:{label:"回款日期",align:"center",prop:"keyTime",width:"130"}}),a("el-table-column",{attrs:{label:"代扣金额",align:"center",prop:"lendingBank"}}),a("el-table-column",{attrs:{label:"分账日",align:"center",prop:"keyTime",width:"130"}}),a("el-table-column",{attrs:{label:"违约金",align:"center",prop:"lendingBank"}}),a("el-table-column",{attrs:{label:"本金偏差",align:"center",prop:"lendingBank"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"130",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vm_car_order:vm_car_order:edit"],expression:"['vm_car_order:vm_car_order:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleFz(t.row)}}},[e._v("分账登记")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:"分账登记",visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],l=a("5530"),i=(a("d81d"),a("b0c0"),a("d3b7"),a("0643"),a("a573"),a("5029")),o=a("cf0d"),s={name:"Vm_car_order",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,vm_car_orderList:[],title:"",open:!1,queryParams:{pageSize:10,customerName:"",plateNo:"",jgName:"",garageId:"",lendingBank:"",paymentStatus:"",pageNum:1,cardID:"",clerk:"",applicant:""},jgNameList:[{label:"A公司",value:1},{label:"B公司",value:2}],lendingBankList:[{label:"A银行",value:1},{label:"B银行",value:2}],followUp:[{label:"无法跟进",value:1},{label:"约定还款",value:2},{label:"继续联系",value:3}],examineList:[{label:"代偿逾期",value:1},{label:"本期成功",value:2},{label:"代偿完结",value:3},{label:"代偿还款中",value:4}],paymentList:[{label:"待打款",value:1},{label:"已打款",value:2}],form:{id:"",keyProvince:"",keyCity:"",keyBorough:"",keyAddress:""},rules:{keyProvince:"",keyCity:"",keyBorough:"",keyAddress:""},provinceList:o,cityList:[],districtList:[],revokeList:{id:"",status:4}}},created:function(){this.getTeam(),this.getList()},methods:{handleFz:function(){this.open=!0},handleChange:function(e){this.queryParams.jgName=e},provinceChange:function(e){this.form.keyProvince=e.name,this.cityList=e.children},cityChange:function(e){this.form.keyCity=e.name,this.districtList=e.children},districtChange:function(e){this.form.keyBorough=e.name},getTeam:function(){var e=this;Object(i["g"])().then((function(t){e.teamList=t.team,e.jgNameList=t.office}))},getList:function(){var e=this;this.loading=!0,Object(i["d"])(this.queryParams).then((function(t){e.vm_car_orderList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:"",keyProvince:"",keyCity:"",keyBorough:"",keyAddress:""},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.restSearch(),this.handleQuery()},restSearch:function(){this.queryParams={pageSize:10,customerName:"",plateNo:"",cardID:"",jgName:"",garageId:"",lendingBank:"",paymentStatus:"",pageNum:1,clerk:"",applicant:""}},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加VIEW"},handleUpdate:function(e){var t=this;this.reset(),this.form.id=e.id,this.form.keyProvince=e.keyProvince,this.form.keyCity=e.keyCity,this.form.keyBorough=e.keyBorough,this.form.keyAddress=e.keyAddress;var a=e.id||this.ids;Object(i["c"])(a).then((function(e){t.open=!0,t.title="邮寄钥匙"}))},submitForm:function(){var e=this;Object(i["a"])(this.form).then((function(t){e.$modal.msgSuccess("提交成功"),e.open=!1,e.getList()}))},handleRevoke:function(e){var t=this;console.log("1111"),this.revokeList.id=e.id;var a={id:e.id,status:4};this.$modal.confirm("是否确认撤销？").then((function(){return Object(i["a"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("撤销成功")})).catch((function(e){console.log(e)}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认撤销编号为"'+a+'"的数据项？').then((function(){return Object(i["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("撤销成功")})).catch((function(){}))},handleExport:function(){this.download("vm_car_order/vm_car_order/export",Object(l["a"])({},this.queryParams),"vm_car_order_".concat((new Date).getTime(),".xlsx"))}}},c=s,u=a("2877"),m=Object(u["a"])(c,r,n,!1,null,null,null);t["default"]=m.exports},5029:function(e,t,a){"use strict";a.d(t,"d",(function(){return n})),a.d(t,"g",(function(){return l})),a.d(t,"c",(function(){return i})),a.d(t,"b",(function(){return o})),a.d(t,"a",(function(){return s})),a.d(t,"f",(function(){return c})),a.d(t,"e",(function(){return u}));var r=a("b775");function n(e){return Object(r["a"])({url:"/vm_car_order/vm_car_order/list",method:"get",params:e})}function l(){return Object(r["a"])({url:"/vm_car_order/vm_car_order/cate",method:"get"})}function i(e){return Object(r["a"])({url:"/vm_car_order/vm_car_order/"+e,method:"get"})}function o(e){return Object(r["a"])({url:"/vm_car_order/vm_car_order/"+e,method:"delete"})}function s(e){return Object(r["a"])({url:"/car_order/car_order",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/vm_car_order/vm_car_order/submitCost",method:"post",data:e})}function u(e){return Object(r["a"])({url:"/vm_car_order/vm_car_order/mailKey",method:"post",data:e})}}}]);