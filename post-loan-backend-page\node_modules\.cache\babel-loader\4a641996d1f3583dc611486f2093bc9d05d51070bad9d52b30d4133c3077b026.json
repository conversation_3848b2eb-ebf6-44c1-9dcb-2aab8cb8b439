{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _regenerator2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/regenerator.js\"));\nvar _objectSpread2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nrequire(\"core-js/modules/es.array.map.js\");\nrequire(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/es.array.sort.js\");\nrequire(\"core-js/modules/es.object.keys.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.for-each.js\");\nrequire(\"core-js/modules/esnext.iterator.map.js\");\nrequire(\"core-js/modules/esnext.iterator.some.js\");\nrequire(\"core-js/modules/web.dom-collections.for-each.js\");\nvar _litigation = require(\"@/api/litigation/litigation\");\nvar _reminde_view = require(\"@/api/reminde_view/reminde_view\");\nvar _loanReminderLog = _interopRequireDefault(require(\"@/layout/components/Dialog/loanReminderLog.vue\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default2 = exports.default = {\n  name: 'LitigationLogView',\n  components: {\n    LoanReminderLog: _loanReminderLog.default\n  },\n  props: {\n    data: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  watch: {\n    data: {\n      handler: function handler(newVal) {\n        console.log('newVal:', newVal);\n        if (newVal && newVal.id) {\n          this.loadLitigationLogs(newVal.id, newVal.流程序号);\n        }\n      }\n    }\n  },\n  data: function data() {\n    return {\n      visible: false,\n      loading: false,\n      // 诉讼状态树结构\n      reminderLogLoanId: '',\n      // 文档预览相关\n      docPreviewVisible: false,\n      docPreviewList: [],\n      litigationStatusTree: [{\n        label: '立案前',\n        value: '立案前',\n        children: [{\n          label: '准备资料',\n          value: '准备资料'\n        }, {\n          label: '已邮寄',\n          value: '撤案已邮寄'\n        }, {\n          label: '待立案',\n          value: '待立案'\n        }]\n      }, {\n        label: '立案-判决',\n        value: '立案-判决',\n        children: [{\n          label: '待出民初号',\n          value: '待出民初号'\n        }, {\n          label: '待开庭',\n          value: '待开庭'\n        }, {\n          label: '待出法院文书',\n          value: '待出法院文书'\n        }]\n      }, {\n        label: '判决-执行',\n        value: '判决-执行',\n        children: [{\n          label: '待出申请书',\n          value: '待出申请书'\n        }, {\n          label: '已提交执行书',\n          value: '已提交执行书'\n        }]\n      }, {\n        label: '执行后',\n        value: '执行后',\n        children: [{\n          label: '执行中',\n          value: '执行中'\n        }, {\n          label: '待送车',\n          value: '待送车'\n        }, {\n          label: '待法拍',\n          value: '待法拍'\n        }, {\n          label: '继续执行',\n          value: '继续执行'\n        }, {\n          label: '执行终本',\n          value: '执行终本'\n        }]\n      }, {\n        label: '结案',\n        value: '结案',\n        children: [{\n          label: '法诉减免结清',\n          value: '法诉减免结清'\n        }, {\n          label: '法诉全额结清',\n          value: '法诉全额结清'\n        }]\n      }, {\n        label: '撤案',\n        value: '撤案'\n      }],\n      // 当前激活的步骤索引\n      activeStep: 0,\n      logList: []\n    };\n  },\n  computed: {\n    // 从状态树中提取父节点的label作为进度条步骤\n    statusSteps: function statusSteps() {\n      return this.litigationStatusTree.map(function (item) {\n        return item.label;\n      });\n    }\n  },\n  methods: {\n    // 加载法诉日志数据\n    loadLitigationLogs: function loadLitigationLogs(litigationCaseId, loanId) {\n      var _this = this;\n      return (0, _asyncToGenerator2.default)(/*#__PURE__*/(0, _regenerator2.default)().m(function _callee() {\n        var litigationLogRes, reminderLogRes, combinedLogs, lastLogStatus, _t;\n        return (0, _regenerator2.default)().w(function (_context) {\n          while (1) switch (_context.p = _context.n) {\n            case 0:\n              _context.p = 0;\n              _context.n = 1;\n              return (0, _litigation.listLitigation_log)({\n                litigationCaseId: litigationCaseId\n              });\n            case 1:\n              litigationLogRes = _context.v;\n              _context.n = 2;\n              return (0, _reminde_view.loan_reminder_order)({\n                loanId: loanId,\n                status: 2,\n                litigationId: litigationCaseId\n              });\n            case 2:\n              reminderLogRes = _context.v;\n              // 合并数据\n              combinedLogs = []; // 添加法诉日志\n              if (litigationLogRes.rows) {\n                litigationLogRes.rows.forEach(function (log) {\n                  combinedLogs.push((0, _objectSpread2.default)((0, _objectSpread2.default)({}, log), {}, {\n                    type: 'litigation',\n                    urgeDescribe: log.remark || '-'\n                  }));\n                });\n              }\n\n              // 添加催记日志\n              if (reminderLogRes.rows) {\n                reminderLogRes.rows.forEach(function (reminder) {\n                  combinedLogs.push((0, _objectSpread2.default)((0, _objectSpread2.default)({}, reminder), {}, {\n                    type: 'reminder',\n                    status: _this.getUrgeStatusText(reminder.urgeStatus),\n                    docName: '',\n                    docNumber: '',\n                    docUploadUrl: '',\n                    openDate: '',\n                    docEffectiveDate: ''\n                  }));\n                });\n              }\n\n              // 按创建时间排序\n              combinedLogs.sort(function (a, b) {\n                return new Date(b.createTime) - new Date(a.createTime);\n              });\n              _this.logList = combinedLogs;\n\n              // 获取最后一个日志的状态，并设置对应的进度条步骤\n              if (combinedLogs.length > 0) {\n                lastLogStatus = combinedLogs[0].status;\n                _this.setActiveStepByStatus(lastLogStatus);\n              }\n              _context.n = 4;\n              break;\n            case 3:\n              _context.p = 3;\n              _t = _context.v;\n              console.error('加载法诉日志失败:', _t);\n              _this.$message.error('加载日志数据失败');\n            case 4:\n              return _context.a(2);\n          }\n        }, _callee, null, [[0, 3]]);\n      }))();\n    },\n    // 获取催记状态文本\n    getUrgeStatusText: function getUrgeStatusText(status) {\n      var statusMap = {\n        1: '继续跟踪',\n        2: '约定还款',\n        3: '无法跟进',\n        4: '暂时无需跟进'\n      };\n      return statusMap[status] || '未知状态';\n    },\n    // 根据状态设置激活的步骤\n    setActiveStepByStatus: function setActiveStepByStatus(status) {\n      if (!status) {\n        this.activeStep = 0;\n        return;\n      }\n\n      // 查找状态对应的父节点索引\n      var parentIndex = this.findParentIndexByStatus(status);\n      this.activeStep = parentIndex >= 0 ? parentIndex : 0;\n    },\n    // 根据状态找到父节点的索引\n    findParentIndexByStatus: function findParentIndexByStatus(status) {\n      for (var i = 0; i < this.litigationStatusTree.length; i++) {\n        var item = this.litigationStatusTree[i];\n\n        // 如果是父节点本身\n        if (item.label === status || item.value === status) {\n          return i;\n        }\n\n        // 如果有子节点，在子节点中查找\n        if (item.children && item.children.length > 0) {\n          var childFound = item.children.some(function (child) {\n            return child.label === status || child.value === status;\n          });\n          if (childFound) {\n            return i;\n          }\n        }\n      }\n      return -1;\n    },\n    // 格式化日期时间\n    formatDateTime: function formatDateTime(dateTime) {\n      if (!dateTime) return '-';\n      var date = new Date(dateTime);\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n    // 格式化日期\n    formatDate: function formatDate(date) {\n      if (!date) return '-';\n      var d = new Date(date);\n      return d.toLocaleDateString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit'\n      });\n    },\n    // 查看文档附件\n    viewDocuments: function viewDocuments(docUrls) {\n      if (!docUrls) return;\n      var urls = [];\n      if (typeof docUrls === 'string') {\n        try {\n          urls = JSON.parse(docUrls);\n        } catch (e) {\n          urls = [docUrls];\n        }\n      } else if (Array.isArray(docUrls)) {\n        urls = docUrls;\n      }\n      if (urls.length > 0) {\n        // 如果只有一个文件，直接打开\n        if (urls.length === 1) {\n          window.open(urls[0], '_blank');\n        } else {\n          // 多个文件，显示列表让用户选择\n          this.docPreviewList = urls.map(function (url, index) {\n            return {\n              name: \"\\u9644\\u4EF6\".concat(index + 1),\n              url: url\n            };\n          });\n          this.docPreviewVisible = true;\n        }\n      }\n    },\n    openDialog: function openDialog() {\n      this.visible = true;\n    },\n    handleUrgeLog: function handleUrgeLog() {\n      // 打开催记日志对话框，传入当前的 data\n      this.reminderLogLoanId = String(this.data.流程序号);\n      this.$refs.loanReminderLog.openLogDialog();\n    },\n    handleConfirm: function handleConfirm() {\n      return;\n    },\n    handleCancel: function handleCancel() {\n      this.visible = false;\n    }\n  }\n};", "map": {"version": 3, "names": ["_litigation", "require", "_reminde_view", "_loanReminderLog", "_interopRequireDefault", "name", "components", "LoanReminderLog", "props", "data", "type", "Object", "default", "watch", "handler", "newVal", "console", "log", "id", "loadLitigationLogs", "流程序号", "visible", "loading", "reminderLogLoanId", "docPreviewVisible", "docPreviewList", "litigationStatusTree", "label", "value", "children", "activeStep", "logList", "computed", "statusSteps", "map", "item", "methods", "litigationCaseId", "loanId", "_this", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "litigationLogRes", "reminderLogRes", "combinedLogs", "lastLogStatus", "_t", "w", "_context", "p", "n", "listLitigation_log", "v", "loan_reminder_order", "status", "litigationId", "rows", "for<PERSON>ach", "push", "_objectSpread2", "urgeDescribe", "remark", "reminder", "getUrgeStatusText", "urgeStatus", "doc<PERSON>ame", "docNumber", "docUploadUrl", "openDate", "docEffectiveDate", "sort", "a", "b", "Date", "createTime", "length", "setActiveStepByStatus", "error", "$message", "statusMap", "parentIndex", "findParentIndexByStatus", "i", "childFound", "some", "child", "formatDateTime", "dateTime", "date", "toLocaleString", "year", "month", "day", "hour", "minute", "formatDate", "d", "toLocaleDateString", "viewDocuments", "doc<PERSON>rls", "urls", "JSON", "parse", "e", "Array", "isArray", "window", "open", "url", "index", "concat", "openDialog", "handleUrgeLog", "String", "$refs", "loanReminderLog", "openLogDialog", "handleConfirm", "handleCancel"], "sources": ["src/views/litigation/litigation/modules/litigationLogView.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :visible.sync=\"visible\" title=\"日志查看\" width=\"1200px\" @close=\"handleCancel\">\r\n      <!-- 进度条 -->\r\n      <el-steps :active=\"activeStep\" finish-status=\"success\" process-status=\"process\" align-center style=\"margin-bottom: 24px\" class=\"custom-steps\">\r\n        <el-step v-for=\"(item, idx) in statusSteps\" :key=\"idx\" :title=\"item\" />\r\n      </el-steps>\r\n\r\n      <!-- 日志表格 -->\r\n      <el-table v-loading=\"loading\" :data=\"logList\" border style=\"width: 100%; margin-bottom: 24px\" element-loading-text=\"加载日志数据中...\">\r\n        <el-table-column prop=\"createTime\" label=\"时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ formatDateTime(scope.row.createTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"createBy\" label=\"跟踪人\" width=\"120\" />\r\n        <el-table-column prop=\"status\" label=\"跟踪动作\" width=\"120\" />\r\n        <el-table-column label=\"文书信息\" width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.docName || scope.row.docNumber\">\r\n              <div v-if=\"scope.row.docName\" style=\"margin-bottom: 4px;\">\r\n                <el-tag size=\"mini\" type=\"info\">{{ scope.row.docName }}</el-tag>\r\n              </div>\r\n              <div v-if=\"scope.row.docNumber\" style=\"font-size: 12px; color: #666;\">\r\n                文书号：{{ scope.row.docNumber }}\r\n              </div>\r\n            </div>\r\n            <span v-else style=\"color: #999;\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"开庭时间\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.openDate\">{{ formatDate(scope.row.openDate) }}</span>\r\n            <span v-else style=\"color: #999;\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"文书生效时间\" width=\"140\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.docEffectiveDate\">{{ formatDateTime(scope.row.docEffectiveDate) }}</span>\r\n            <span v-else style=\"color: #999;\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"文书附件\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              v-if=\"scope.row.docUploadUrl\"\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"viewDocuments(scope.row.docUploadUrl)\"\r\n            >\r\n              查看附件\r\n            </el-button>\r\n            <span v-else style=\"color: #999;\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"urgeDescribe\" label=\"描述\" min-width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.urgeDescribe\">{{ scope.row.urgeDescribe }}</span>\r\n            <span v-else style=\"color: #999;\">-</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleUrgeLog\">催记日志</el-button>\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确认</el-button>\r\n        <el-button @click=\"handleCancel\">取消</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 催记日志组件 -->\r\n    <loan-reminder-log ref=\"loanReminderLog\" :loan-id=\"reminderLogLoanId\" />\r\n\r\n    <!-- 文档预览对话框 -->\r\n    <el-dialog :visible.sync=\"docPreviewVisible\" title=\"文档附件\" width=\"400px\">\r\n      <div style=\"max-height: 300px; overflow-y: auto;\">\r\n        <div v-for=\"(doc, index) in docPreviewList\" :key=\"index\" style=\"margin-bottom: 10px;\">\r\n          <div class=\"doc-preview-btn\" @click=\"window.open(doc.url, '_blank')\" style=\"cursor: pointer;\">\r\n            <i class=\"el-icon-document\" style=\"margin-right: 8px; color: #409eff;\"></i>\r\n            {{ doc.name }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"docPreviewVisible = false\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listLitigation_log } from '@/api/litigation/litigation'\r\nimport { loan_reminder_order } from '@/api/reminde_view/reminde_view'\r\nimport LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'\r\n\r\nexport default {\r\n  name: 'LitigationLogView',\r\n  components: {\r\n    LoanReminderLog,\r\n  },\r\n  props: {\r\n    data: {\r\n      type: Object,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        console.log('newVal:', newVal)\r\n        if (newVal && newVal.id) {\r\n          this.loadLitigationLogs(newVal.id, newVal.流程序号)\r\n        }\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      loading: false,\r\n      // 诉讼状态树结构\r\n      reminderLogLoanId: '',\r\n      // 文档预览相关\r\n      docPreviewVisible: false,\r\n      docPreviewList: [],\r\n      litigationStatusTree: [\r\n        {\r\n          label: '立案前',\r\n          value: '立案前',\r\n          children: [\r\n            { label: '准备资料', value: '准备资料' },\r\n            { label: '已邮寄', value: '撤案已邮寄' },\r\n            { label: '待立案', value: '待立案' },\r\n          ],\r\n        },\r\n        {\r\n          label: '立案-判决',\r\n          value: '立案-判决',\r\n          children: [\r\n            { label: '待出民初号', value: '待出民初号' },\r\n            { label: '待开庭', value: '待开庭' },\r\n            { label: '待出法院文书', value: '待出法院文书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '判决-执行',\r\n          value: '判决-执行',\r\n          children: [\r\n            { label: '待出申请书', value: '待出申请书' },\r\n            { label: '已提交执行书', value: '已提交执行书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '执行后',\r\n          value: '执行后',\r\n          children: [\r\n            { label: '执行中', value: '执行中' },\r\n            { label: '待送车', value: '待送车' },\r\n            { label: '待法拍', value: '待法拍' },\r\n            { label: '继续执行', value: '继续执行' },\r\n            { label: '执行终本', value: '执行终本' },\r\n          ],\r\n        },\r\n        {\r\n          label: '结案',\r\n          value: '结案',\r\n          children: [\r\n            { label: '法诉减免结清', value: '法诉减免结清' },\r\n            { label: '法诉全额结清', value: '法诉全额结清' },\r\n          ],\r\n        },\r\n        { label: '撤案', value: '撤案' },\r\n      ],\r\n      // 当前激活的步骤索引\r\n      activeStep: 0,\r\n      logList: [],\r\n    }\r\n  },\r\n  computed: {\r\n    // 从状态树中提取父节点的label作为进度条步骤\r\n    statusSteps() {\r\n      return this.litigationStatusTree.map(item => item.label)\r\n    },\r\n  },\r\n  methods: {\r\n    // 加载法诉日志数据\r\n    async loadLitigationLogs(litigationCaseId, loanId) {\r\n      try {\r\n        // 获取法诉日志\r\n        const litigationLogRes = await listLitigation_log({ litigationCaseId })\r\n\r\n        // 获取法诉相关的催记日志（status=2表示法诉日志）\r\n        const reminderLogRes = await loan_reminder_order({\r\n          loanId: loanId,\r\n          status: 2,\r\n          litigationId: litigationCaseId\r\n        })\r\n\r\n        // 合并数据\r\n        const combinedLogs = []\r\n\r\n        // 添加法诉日志\r\n        if (litigationLogRes.rows) {\r\n          litigationLogRes.rows.forEach(log => {\r\n            combinedLogs.push({\r\n              ...log,\r\n              type: 'litigation',\r\n              urgeDescribe: log.remark || '-'\r\n            })\r\n          })\r\n        }\r\n\r\n        // 添加催记日志\r\n        if (reminderLogRes.rows) {\r\n          reminderLogRes.rows.forEach(reminder => {\r\n            combinedLogs.push({\r\n              ...reminder,\r\n              type: 'reminder',\r\n              status: this.getUrgeStatusText(reminder.urgeStatus),\r\n              docName: '',\r\n              docNumber: '',\r\n              docUploadUrl: '',\r\n              openDate: '',\r\n              docEffectiveDate: ''\r\n            })\r\n          })\r\n        }\r\n\r\n        // 按创建时间排序\r\n        combinedLogs.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))\r\n\r\n        this.logList = combinedLogs\r\n\r\n        // 获取最后一个日志的状态，并设置对应的进度条步骤\r\n        if (combinedLogs.length > 0) {\r\n          const lastLogStatus = combinedLogs[0].status\r\n          this.setActiveStepByStatus(lastLogStatus)\r\n        }\r\n      } catch (error) {\r\n        console.error('加载法诉日志失败:', error)\r\n        this.$message.error('加载日志数据失败')\r\n      }\r\n    },\r\n\r\n    // 获取催记状态文本\r\n    getUrgeStatusText(status) {\r\n      const statusMap = {\r\n        1: '继续跟踪',\r\n        2: '约定还款',\r\n        3: '无法跟进',\r\n        4: '暂时无需跟进'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    },\r\n\r\n    // 根据状态设置激活的步骤\r\n    setActiveStepByStatus(status) {\r\n      if (!status) {\r\n        this.activeStep = 0\r\n        return\r\n      }\r\n\r\n      // 查找状态对应的父节点索引\r\n      const parentIndex = this.findParentIndexByStatus(status)\r\n      this.activeStep = parentIndex >= 0 ? parentIndex : 0\r\n    },\r\n\r\n    // 根据状态找到父节点的索引\r\n    findParentIndexByStatus(status) {\r\n      for (let i = 0; i < this.litigationStatusTree.length; i++) {\r\n        const item = this.litigationStatusTree[i]\r\n\r\n        // 如果是父节点本身\r\n        if (item.label === status || item.value === status) {\r\n          return i\r\n        }\r\n\r\n        // 如果有子节点，在子节点中查找\r\n        if (item.children && item.children.length > 0) {\r\n          const childFound = item.children.some(child => child.label === status || child.value === status)\r\n          if (childFound) {\r\n            return i\r\n          }\r\n        }\r\n      }\r\n      return -1\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(dateTime) {\r\n      if (!dateTime) return '-'\r\n      const date = new Date(dateTime)\r\n      return date.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit'\r\n      })\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(date) {\r\n      if (!date) return '-'\r\n      const d = new Date(date)\r\n      return d.toLocaleDateString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit'\r\n      })\r\n    },\r\n\r\n    // 查看文档附件\r\n    viewDocuments(docUrls) {\r\n      if (!docUrls) return\r\n\r\n      let urls = []\r\n      if (typeof docUrls === 'string') {\r\n        try {\r\n          urls = JSON.parse(docUrls)\r\n        } catch (e) {\r\n          urls = [docUrls]\r\n        }\r\n      } else if (Array.isArray(docUrls)) {\r\n        urls = docUrls\r\n      }\r\n\r\n      if (urls.length > 0) {\r\n        // 如果只有一个文件，直接打开\r\n        if (urls.length === 1) {\r\n          window.open(urls[0], '_blank')\r\n        } else {\r\n          // 多个文件，显示列表让用户选择\r\n          this.docPreviewList = urls.map((url, index) => ({\r\n            name: `附件${index + 1}`,\r\n            url: url\r\n          }))\r\n          this.docPreviewVisible = true\r\n        }\r\n      }\r\n    },\r\n\r\n    openDialog() {\r\n      this.visible = true\r\n    },\r\n    handleUrgeLog() {\r\n      // 打开催记日志对话框，传入当前的 data\r\n      this.reminderLogLoanId = String(this.data.流程序号)\r\n      this.$refs.loanReminderLog.openLogDialog()\r\n    },\r\n    handleConfirm() {\r\n      return\r\n    },\r\n    handleCancel() {\r\n      this.visible = false\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 自定义步骤条样式 - 激活节点为蓝色 */\r\n::v-deep .custom-steps .el-step__head.is-process {\r\n  color: #409eff;\r\n  border-color: #409eff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__head.is-process .el-step__icon {\r\n  background-color: #409eff;\r\n  border-color: #409eff;\r\n  color: #fff;\r\n}\r\n\r\n::v-deep .custom-steps .el-step__title.is-process {\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 表格样式优化 */\r\n::v-deep .el-table .el-table__cell {\r\n  padding: 8px 0;\r\n}\r\n\r\n::v-deep .el-table .el-table__header th {\r\n  background-color: #f5f7fa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 文档预览按钮样式 */\r\n.doc-preview-btn {\r\n  width: 100%;\r\n  text-align: left;\r\n  padding: 8px 12px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.doc-preview-btn:hover {\r\n  background-color: #f5f7fa;\r\n  border-color: #409eff;\r\n  color: #409eff;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AA2FA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,gBAAA,GAAAC,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAI,IAAA;EACAC,UAAA;IACAC,eAAA,EAAAA;EACA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,KAAA;IACAJ,IAAA;MACAK,OAAA,WAAAA,QAAAC,MAAA;QACAC,OAAA,CAAAC,GAAA,YAAAF,MAAA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAG,EAAA;UACA,KAAAC,kBAAA,CAAAJ,MAAA,CAAAG,EAAA,EAAAH,MAAA,CAAAK,IAAA;QACA;MACA;IACA;EACA;EACAX,IAAA,WAAAA,KAAA;IACA;MACAY,OAAA;MACAC,OAAA;MACA;MACAC,iBAAA;MACA;MACAC,iBAAA;MACAC,cAAA;MACAC,oBAAA,GACA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QACAD,KAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAF,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA;MAEA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACA;MACAE,UAAA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAP,oBAAA,CAAAQ,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAR,KAAA;MAAA;IACA;EACA;EACAS,OAAA;IACA;IACAjB,kBAAA,WAAAA,mBAAAkB,gBAAA,EAAAC,MAAA;MAAA,IAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAA5B,OAAA,mBAAA6B,aAAA,CAAA7B,OAAA,IAAA8B,CAAA,UAAAC,QAAA;QAAA,IAAAC,gBAAA,EAAAC,cAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,EAAA;QAAA,WAAAP,aAAA,CAAA7B,OAAA,IAAAqC,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAD,QAAA,CAAAE,CAAA;cAAA,OAGA,IAAAC,8BAAA;gBAAAhB,gBAAA,EAAAA;cAAA;YAAA;cAAAO,gBAAA,GAAAM,QAAA,CAAAI,CAAA;cAAAJ,QAAA,CAAAE,CAAA;cAAA,OAGA,IAAAG,iCAAA;gBACAjB,MAAA,EAAAA,MAAA;gBACAkB,MAAA;gBACAC,YAAA,EAAApB;cACA;YAAA;cAJAQ,cAAA,GAAAK,QAAA,CAAAI,CAAA;cAMA;cACAR,YAAA,OAEA;cACA,IAAAF,gBAAA,CAAAc,IAAA;gBACAd,gBAAA,CAAAc,IAAA,CAAAC,OAAA,WAAA1C,GAAA;kBACA6B,YAAA,CAAAc,IAAA,KAAAC,cAAA,CAAAjD,OAAA,MAAAiD,cAAA,CAAAjD,OAAA,MACAK,GAAA;oBACAP,IAAA;oBACAoD,YAAA,EAAA7C,GAAA,CAAA8C,MAAA;kBAAA,EACA;gBACA;cACA;;cAEA;cACA,IAAAlB,cAAA,CAAAa,IAAA;gBACAb,cAAA,CAAAa,IAAA,CAAAC,OAAA,WAAAK,QAAA;kBACAlB,YAAA,CAAAc,IAAA,KAAAC,cAAA,CAAAjD,OAAA,MAAAiD,cAAA,CAAAjD,OAAA,MACAoD,QAAA;oBACAtD,IAAA;oBACA8C,MAAA,EAAAjB,KAAA,CAAA0B,iBAAA,CAAAD,QAAA,CAAAE,UAAA;oBACAC,OAAA;oBACAC,SAAA;oBACAC,YAAA;oBACAC,QAAA;oBACAC,gBAAA;kBAAA,EACA;gBACA;cACA;;cAEA;cACAzB,YAAA,CAAA0B,IAAA,WAAAC,CAAA,EAAAC,CAAA;gBAAA,WAAAC,IAAA,CAAAD,CAAA,CAAAE,UAAA,QAAAD,IAAA,CAAAF,CAAA,CAAAG,UAAA;cAAA;cAEArC,KAAA,CAAAR,OAAA,GAAAe,YAAA;;cAEA;cACA,IAAAA,YAAA,CAAA+B,MAAA;gBACA9B,aAAA,GAAAD,YAAA,IAAAU,MAAA;gBACAjB,KAAA,CAAAuC,qBAAA,CAAA/B,aAAA;cACA;cAAAG,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEAtC,OAAA,CAAA+D,KAAA,cAAA/B,EAAA;cACAT,KAAA,CAAAyC,QAAA,CAAAD,KAAA;YAAA;cAAA,OAAA7B,QAAA,CAAAuB,CAAA;UAAA;QAAA,GAAA9B,OAAA;MAAA;IAEA;IAEA;IACAsB,iBAAA,WAAAA,kBAAAT,MAAA;MACA,IAAAyB,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAzB,MAAA;IACA;IAEA;IACAsB,qBAAA,WAAAA,sBAAAtB,MAAA;MACA,KAAAA,MAAA;QACA,KAAA1B,UAAA;QACA;MACA;;MAEA;MACA,IAAAoD,WAAA,QAAAC,uBAAA,CAAA3B,MAAA;MACA,KAAA1B,UAAA,GAAAoD,WAAA,QAAAA,WAAA;IACA;IAEA;IACAC,uBAAA,WAAAA,wBAAA3B,MAAA;MACA,SAAA4B,CAAA,MAAAA,CAAA,QAAA1D,oBAAA,CAAAmD,MAAA,EAAAO,CAAA;QACA,IAAAjD,IAAA,QAAAT,oBAAA,CAAA0D,CAAA;;QAEA;QACA,IAAAjD,IAAA,CAAAR,KAAA,KAAA6B,MAAA,IAAArB,IAAA,CAAAP,KAAA,KAAA4B,MAAA;UACA,OAAA4B,CAAA;QACA;;QAEA;QACA,IAAAjD,IAAA,CAAAN,QAAA,IAAAM,IAAA,CAAAN,QAAA,CAAAgD,MAAA;UACA,IAAAQ,UAAA,GAAAlD,IAAA,CAAAN,QAAA,CAAAyD,IAAA,WAAAC,KAAA;YAAA,OAAAA,KAAA,CAAA5D,KAAA,KAAA6B,MAAA,IAAA+B,KAAA,CAAA3D,KAAA,KAAA4B,MAAA;UAAA;UACA,IAAA6B,UAAA;YACA,OAAAD,CAAA;UACA;QACA;MACA;MACA;IACA;IAEA;IACAI,cAAA,WAAAA,eAAAC,QAAA;MACA,KAAAA,QAAA;MACA,IAAAC,IAAA,OAAAf,IAAA,CAAAc,QAAA;MACA,OAAAC,IAAA,CAAAC,cAAA;QACAC,IAAA;QACAC,KAAA;QACAC,GAAA;QACAC,IAAA;QACAC,MAAA;MACA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAAP,IAAA;MACA,KAAAA,IAAA;MACA,IAAAQ,CAAA,OAAAvB,IAAA,CAAAe,IAAA;MACA,OAAAQ,CAAA,CAAAC,kBAAA;QACAP,IAAA;QACAC,KAAA;QACAC,GAAA;MACA;IACA;IAEA;IACAM,aAAA,WAAAA,cAAAC,OAAA;MACA,KAAAA,OAAA;MAEA,IAAAC,IAAA;MACA,WAAAD,OAAA;QACA;UACAC,IAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,OAAA;QACA,SAAAI,CAAA;UACAH,IAAA,IAAAD,OAAA;QACA;MACA,WAAAK,KAAA,CAAAC,OAAA,CAAAN,OAAA;QACAC,IAAA,GAAAD,OAAA;MACA;MAEA,IAAAC,IAAA,CAAAzB,MAAA;QACA;QACA,IAAAyB,IAAA,CAAAzB,MAAA;UACA+B,MAAA,CAAAC,IAAA,CAAAP,IAAA;QACA;UACA;UACA,KAAA7E,cAAA,GAAA6E,IAAA,CAAApE,GAAA,WAAA4E,GAAA,EAAAC,KAAA;YAAA;cACA1G,IAAA,iBAAA2G,MAAA,CAAAD,KAAA;cACAD,GAAA,EAAAA;YACA;UAAA;UACA,KAAAtF,iBAAA;QACA;MACA;IACA;IAEAyF,UAAA,WAAAA,WAAA;MACA,KAAA5F,OAAA;IACA;IACA6F,aAAA,WAAAA,cAAA;MACA;MACA,KAAA3F,iBAAA,GAAA4F,MAAA,MAAA1G,IAAA,CAAAW,IAAA;MACA,KAAAgG,KAAA,CAAAC,eAAA,CAAAC,aAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAnG,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}