{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\index.vue", "mtime": 1754371133911}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0TGl0aWdhdGlvbiwgZ2V0TGl0aWdhdGlvbkNvc3RTdW1tYXJ5LCBnZXRMaXRpZ2F0aW9uQnlMb2FuSWQgfSBmcm9tICdAL2FwaS9saXRpZ2F0aW9uL2xpdGlnYXRpb24nDQppbXBvcnQgeyBsaXN0UGFydG5lcl9pbmZvX3NpbXBsZSB9IGZyb20gJ0AvYXBpL3BhcnRuZXJfaW5mby9wYXJ0bmVyX2luZm8nDQppbXBvcnQgbGl0aWdhdGlvblN0YXR1cyBmcm9tICdAL2xheW91dC9jb21wb25lbnRzL0RpYWxvZy9saXRpZ2F0aW9uU3RhdHVzLnZ1ZScNCmltcG9ydCBsaXRpZ2F0aW9uRm9ybSBmcm9tICcuL21vZHVsZXMvbGl0aWdhdGlvbkZvcm0nDQppbXBvcnQgbGl0aWdhdGlvbkZlZUZvcm0gZnJvbSAnLi9tb2R1bGVzL2xpdGlnYXRpb25GZWVGb3JtJw0KaW1wb3J0IGxpdGlnYXRpb25Mb2dGb3JtIGZyb20gJy4vbW9kdWxlcy9saXRpZ2F0aW9uTG9nRm9ybScNCmltcG9ydCBsaXRpZ2F0aW9uTG9nVmlldyBmcm9tICcuL21vZHVsZXMvbGl0aWdhdGlvbkxvZ1ZpZXcnDQppbXBvcnQgdXNlckluZm8gZnJvbSAnQC9sYXlvdXQvY29tcG9uZW50cy9EaWFsb2cvdXNlckluZm8udnVlJw0KaW1wb3J0IGNhckluZm8gZnJvbSAnQC9sYXlvdXQvY29tcG9uZW50cy9EaWFsb2cvY2FySW5mby52dWUnDQppbXBvcnQgZGlzcGF0Y2hWZWhpY2xlRm9ybSBmcm9tICdAL2xheW91dC9jb21wb25lbnRzL0RpYWxvZy9kaXNwYXRjaFZlaGljbGVGb3JtLnZ1ZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnTGl0aWdhdGlvbicsDQogIGNvbXBvbmVudHM6IHsNCiAgICBsaXRpZ2F0aW9uU3RhdHVzLA0KICAgIGxpdGlnYXRpb25Gb3JtLA0KICAgIGxpdGlnYXRpb25GZWVGb3JtLA0KICAgIGxpdGlnYXRpb25Mb2dGb3JtLA0KICAgIGxpdGlnYXRpb25Mb2dWaWV3LA0KICAgIHVzZXJJbmZvLA0KICAgIGNhckluZm8sDQogICAgZGlzcGF0Y2hWZWhpY2xlRm9ybSwNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOazleivieahiOS7tuihqOagvOaVsOaNrg0KICAgICAgbGl0aWdhdGlvbl9jYXNlTGlzdDogW10sDQogICAgICAvLyDms5Xor4notLnnlKjmsYfmgLvmlbDmja4gLSDmjInmoYjku7ZJROWtmOWCqA0KICAgICAgbGl0aWdhdGlvbkNvc3RTdW1tYXJ5OiB7fSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICcnLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICDotLfmrL7kuro6ICcnLA0KICAgICAgICDouqvku73or4E6ICcnLA0KICAgICAgICDovabovobniYzlj7c6ICcnLA0KICAgICAgICDovabovobnirbmgIE6ICcnLA0KICAgICAgICDlh7rljZXmuKDpgZM6ICcnLA0KICAgICAgICBsZW5kaW5nQmFuazogJycsDQogICAgICAgIOaUvuasvumTtuihjDogJycsDQogICAgICAgIGxpdGlnYXRpb25UaW1lOiAnJywNCiAgICAgICAgbGl0aWdhdGlvblN0YXJ0RGF0ZTogJycsDQogICAgICAgIGxpdGlnYXRpb25FbmREYXRlOiAnJywNCiAgICAgICAg5rOV6K+J5paH5ZGYOiAnJywNCiAgICAgICAg6Lef5YKs5ZGYOiAnJywNCiAgICAgICAg5qGI5Lu26LSf6LSj5Lq6OiAnJywNCiAgICAgICAg5rOV6K+J5a2Q54q25oCBOiAnJywNCiAgICAgICAgbG9nVHlwZTogJycsDQogICAgICAgIOivieiuvOazlemZojogJycsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHt9LA0KICAgICAgLy8g5b2T5YmN6KGM5pWw5o2uDQogICAgICBjdXJyZW50Um93OiB7fSwNCiAgICAgIHNob3dNb3JlOiBmYWxzZSwNCiAgICAgIC8vIOi0t+asvuS6uuS/oeaBrw0KICAgICAgdXNlckluZm9WaXNpYmxlOiBmYWxzZSwNCiAgICAgIGN1c3RvbWVySW5mbzogeyBjdXN0b21lcklkOiAnJywgYXBwbHlJZDogJycgfSwNCiAgICAgIC8vIOi9pui+huS/oeaBrw0KICAgICAgY2FyU2hvdzogZmFsc2UsDQogICAgICBwbGF0ZU5vOiAnJywNCiAgICAgIC8vIOasoOasvuivpuaDhQ0KICAgICAgZGVidERldGFpbFZpc2libGU6IGZhbHNlLA0KICAgICAgY3VycmVudERlYnRSb3c6IG51bGwsDQogICAgICBjYXJTdGF0dXNMaXN0OiBbDQogICAgICAgIHsgbGFiZWw6ICfnnIHlhoXmraPluLjooYzpqbYnLCB2YWx1ZTogJzEnIH0sDQogICAgICAgIHsgbGFiZWw6ICfnnIHlpJbmraPluLjooYzpqbYnLCB2YWx1ZTogJzInIH0sDQogICAgICAgIHsgbGFiZWw6ICfmirXmirwnLCB2YWx1ZTogJzMnIH0sDQogICAgICAgIHsgbGFiZWw6ICfnlpHkvLzmirXmirwnLCB2YWx1ZTogJzQnIH0sDQogICAgICAgIHsgbGFiZWw6ICfnlpHkvLzpu5HovaYnLCB2YWx1ZTogJzUnIH0sDQogICAgICAgIHsgbGFiZWw6ICflt7LlhaXlupMnLCB2YWx1ZTogJzYnIH0sDQogICAgICAgIHsgbGFiZWw6ICfovablnKjms5XpmaInLCB2YWx1ZTogJzcnIH0sDQogICAgICAgIHsgbGFiZWw6ICflt7Lms5Xmi40nLCB2YWx1ZTogJzgnIH0sDQogICAgICAgIHsgbGFiZWw6ICfljY/llYbljZbovaYnLCB2YWx1ZTogJzknIH0sDQogICAgICBdLA0KICAgICAgbGVuZGluZ0JhbmtMaXN0OiBbXSwNCiAgICAgIGxpdGlnYXRpb25TdGF0dXNMaXN0OiBbDQogICAgICAgIHsgbGFiZWw6ICflvoXnq4vmoYgnLCB2YWx1ZTogJzEnIH0sDQogICAgICAgIHsgbGFiZWw6ICflt7Lnq4vmoYgnLCB2YWx1ZTogJzInIH0sDQogICAgICAgIHsgbGFiZWw6ICflvIDluq0nLCB2YWx1ZTogJzMnIH0sDQogICAgICAgIHsgbGFiZWw6ICfliKTlhrMnLCB2YWx1ZTogJzQnIH0sDQogICAgICAgIHsgbGFiZWw6ICfnu5PmoYgnLCB2YWx1ZTogJzUnIH0sDQogICAgICBdLA0KICAgICAgbG9nVHlwZUxpc3Q6IFsNCiAgICAgICAgeyBsYWJlbDogJ+e7p+e7rei3n+i4qicsIHZhbHVlOiAxIH0sDQogICAgICAgIHsgbGFiZWw6ICfnuqblrprov5jmrL4nLCB2YWx1ZTogMiB9LA0KICAgICAgICB7IGxhYmVsOiAn5peg5rOV6Lef6L+bJywgdmFsdWU6IDMgfSwNCiAgICAgIF0sDQogICAgICBsYXdzdWl0Q291cnRMaXN0OiBbDQogICAgICAgIHsgbGFiZWw6ICfms5XpmaJBJywgdmFsdWU6ICdBJyB9LA0KICAgICAgICB7IGxhYmVsOiAn5rOV6ZmiQicsIHZhbHVlOiAnQicgfSwNCiAgICAgICAgeyBsYWJlbDogJ+azlemZokMnLCB2YWx1ZTogJ0MnIH0sDQogICAgICBdLA0KICAgICAgbGF3c3VpdFR5cGVMaXN0OiBbDQogICAgICAgIHsgbGFiZWw6ICflgLrovawnLCB2YWx1ZTogJzEnIH0sDQogICAgICAgIHsgbGFiZWw6ICflgLrliqAnLCB2YWx1ZTogJzInIH0sDQogICAgICAgIHsgbGFiZWw6ICfmi4Xkv53nianmnYMnLCB2YWx1ZTogJzMnIH0sDQogICAgICAgIHsgbGFiZWw6ICfku7Loo4EnLCB2YWx1ZTogJzQnIH0sDQogICAgICAgIHsgbGFiZWw6ICfotYvlvLrlhazor4EnLCB2YWx1ZTogJzUnIH0sDQogICAgICAgIHsgbGFiZWw6ICfmi43nirblhYMnLCB2YWx1ZTogJzYnIH0sDQogICAgICAgIHsgbGFiZWw6ICfmi43lj7jku6QnLCB2YWx1ZTogJzcnIH0sDQogICAgICAgIHsgbGFiZWw6ICflsZ7lnLDor4norrwnLCB2YWx1ZTogJzgnIH0sDQogICAgICAgIHsgbGFiZWw6ICfkvZnlgLzotbfor4knLCB2YWx1ZTogJzknIH0sDQogICAgICAgIHsgbGFiZWw6ICflgLrmnYPlh7rllK4nLCB2YWx1ZTogJzEwJyB9LA0KICAgICAgICB7IGxhYmVsOiAn562+57qm5Zyw6K+J6K68JywgdmFsdWU6ICcxMScgfSwNCiAgICAgICAgeyBsYWJlbDogJ+eJueauiuivieiuvOmAmumBkycsIHZhbHVlOiAnMTInIH0sDQogICAgICBdLA0KICAgICAgbGF3c3VpdENvbnRlbnRMaXN0OiBbDQogICAgICAgIHsgbGFiZWw6ICfpk7booYzku6Plgb/ph5Hpop0nLCB2YWx1ZTogJzEnIH0sDQogICAgICAgIHsgbGFiZWw6ICfku6PmiaPph5Hpop0nLCB2YWx1ZTogJzInIH0sDQogICAgICAgIHsgbGFiZWw6ICfov53nuqbph5EnLCB2YWx1ZTogJzMnIH0sDQogICAgICAgIHsgbGFiZWw6ICflhbbku5bmrKDmrL4nLCB2YWx1ZTogJzQnIH0sDQogICAgICBdLA0KICAgICAgZGlzcGF0Y2hMb2FuSWQ6ICcnLA0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKQ0KICAgIHRoaXMuZ2V0TGVuZGluZ0JhbmtMaXN0KCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6Lms5Xor4nmoYjku7bliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgbGlzdExpdGlnYXRpb24odGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMubGl0aWdhdGlvbl9jYXNlTGlzdCA9IHJlc3BvbnNlLnJvd3MNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsDQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICAgIC8vIOiOt+WPluazleiviei0ueeUqOaxh+aAu+aVsOaNrg0KICAgICAgICB0aGlzLmxvYWRMaXRpZ2F0aW9uQ29zdFN1bW1hcnkoKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLyoqIOWKoOi9veazleiviei0ueeUqOaxh+aAu+aVsOaNriAqLw0KICAgIGxvYWRMaXRpZ2F0aW9uQ29zdFN1bW1hcnkoKSB7DQogICAgICAvLyDojrflj5bmiYDmnInmoYjku7ZJRO+8jOehruS/nei9rOaNouS4uuaVsOWtl+exu+Weiw0KICAgICAgY29uc3QgY2FzZUlkcyA9IHRoaXMubGl0aWdhdGlvbl9jYXNlTGlzdA0KICAgICAgICAubWFwKGl0ZW0gPT4gew0KICAgICAgICAgIGNvbnN0IGlkID0gaXRlbS7luo/lj7cNCiAgICAgICAgICAvLyDnoa7kv51JROaYr+aVsOWtl+exu+Weiw0KICAgICAgICAgIHJldHVybiB0eXBlb2YgaWQgPT09ICdzdHJpbmcnID8gcGFyc2VJbnQoaWQpIDogTnVtYmVyKGlkKQ0KICAgICAgICB9KQ0KICAgICAgICAuZmlsdGVyKGlkID0+IGlkICYmICFpc05hTihpZCkpDQoNCiAgICAgIGlmIChjYXNlSWRzLmxlbmd0aCA9PT0gMCkgcmV0dXJuDQoNCiAgICAgIGNvbnNvbGUubG9nKCflj5HpgIHnmoTmoYjku7ZJROWIl+ihqDonLCBjYXNlSWRzKQ0KDQogICAgICAvLyDosIPnlKhBUEnojrflj5botLnnlKjmsYfmgLsNCiAgICAgIGdldExpdGlnYXRpb25Db3N0U3VtbWFyeShjYXNlSWRzKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMubGl0aWdhdGlvbkNvc3RTdW1tYXJ5ID0gcmVzcG9uc2UuZGF0YSB8fCB7fQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCfojrflj5bliLDnmoTotLnnlKjmsYfmgLvmlbDmja46JywgdGhpcy5saXRpZ2F0aW9uQ29zdFN1bW1hcnkpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5rOV6K+J6LS555So5rGH5oC75aSx6LSlOicsIHJlc3BvbnNlLm1zZykNCiAgICAgICAgICB0aGlzLmxpdGlnYXRpb25Db3N0U3VtbWFyeSA9IHt9DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5rOV6K+J6LS555So5rGH5oC75aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLmxpdGlnYXRpb25Db3N0U3VtbWFyeSA9IHt9DQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5pS+5qy+6ZO26KGM5YiX6KGoICovDQogICAgZ2V0TGVuZGluZ0JhbmtMaXN0KCkgew0KICAgICAgbGlzdFBhcnRuZXJfaW5mb19zaW1wbGUoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgLy8g5bCG6LWE6YeR5pa55pWw5o2u6L2s5o2i5Li65LiL5ouJ5qGG6ZyA6KaB55qE5qC85byPDQogICAgICAgIHRoaXMubGVuZGluZ0JhbmtMaXN0ID0gKHJlc3BvbnNlLmRhdGEgfHwgW10pLm1hcChpdGVtID0+ICh7DQogICAgICAgICAgbGFiZWw6IGl0ZW0ubmFtZSB8fCBpdGVtLm9yZ05hbWUsDQogICAgICAgICAgdmFsdWU6IGl0ZW0uaWQNCiAgICAgICAgfSkpDQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluaUvuasvumTtuihjOWIl+ihqOWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy5sZW5kaW5nQmFua0xpc3QgPSBbXQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluazleiviei0ueeUqOmHkeminSAqLw0KICAgIGdldExpdGlnYXRpb25GZWVBbW91bnQoY2FzZUlkLCBmZWVUeXBlKSB7DQogICAgICAvLyDnoa7kv51jYXNlSWTmmK/mraPnoa7nmoTnsbvlnosNCiAgICAgIGNvbnN0IG5vcm1hbGl6ZWRDYXNlSWQgPSB0eXBlb2YgY2FzZUlkID09PSAnc3RyaW5nJyA/IHBhcnNlSW50KGNhc2VJZCkgOiBOdW1iZXIoY2FzZUlkKQ0KICAgICAgY29uc3Qgc3VtbWFyeSA9IHRoaXMubGl0aWdhdGlvbkNvc3RTdW1tYXJ5W25vcm1hbGl6ZWRDYXNlSWRdDQoNCiAgICAgIGlmICghc3VtbWFyeSkgew0KICAgICAgICByZXR1cm4gMA0KICAgICAgfQ0KDQogICAgICBzd2l0Y2ggKGZlZVR5cGUpIHsNCiAgICAgICAgY2FzZSAnanVkZ21lbnRBbW91bnQnOg0KICAgICAgICAgIHJldHVybiBOdW1iZXIoc3VtbWFyeS5qdWRnbWVudEFtb3VudCB8fCAwKQ0KICAgICAgICBjYXNlICdpbnRlcmVzdCc6DQogICAgICAgICAgcmV0dXJuIE51bWJlcihzdW1tYXJ5LmludGVyZXN0IHx8IDApDQogICAgICAgIGNhc2UgJ2xpdGlnYXRpb24nOg0KICAgICAgICAgIC8vIOivieiuvOi0ueWMheWQq+Wkmuenjei0ueeUqOexu+Wei+eahOaAu+WSjA0KICAgICAgICAgIHJldHVybiBOdW1iZXIoc3VtbWFyeS5sYXd5ZXJGZWUgfHwgMCkgKw0KICAgICAgICAgICAgICAgICBOdW1iZXIoc3VtbWFyeS5saXRpZ2F0aW9uRmVlIHx8IDApICsNCiAgICAgICAgICAgICAgICAgTnVtYmVyKHN1bW1hcnkucHJlc2VydmF0aW9uRmVlIHx8IDApICsNCiAgICAgICAgICAgICAgICAgTnVtYmVyKHN1bW1hcnkuc3VydmVpbGxhbmNlRmVlIHx8IDApICsNCiAgICAgICAgICAgICAgICAgTnVtYmVyKHN1bW1hcnkuYW5ub3VuY2VtZW50RmVlIHx8IDApICsNCiAgICAgICAgICAgICAgICAgTnVtYmVyKHN1bW1hcnkuYXBwcmFpc2FsRmVlIHx8IDApICsNCiAgICAgICAgICAgICAgICAgTnVtYmVyKHN1bW1hcnkuZXhlY3V0aW9uRmVlIHx8IDApICsNCiAgICAgICAgICAgICAgICAgTnVtYmVyKHN1bW1hcnkucGVuYWx0eSB8fCAwKSArDQogICAgICAgICAgICAgICAgIE51bWJlcihzdW1tYXJ5Lmd1YXJhbnRlZUZlZSB8fCAwKSArDQogICAgICAgICAgICAgICAgIE51bWJlcihzdW1tYXJ5LmludGVybWVkaWFyeUZlZSB8fCAwKSArDQogICAgICAgICAgICAgICAgIE51bWJlcihzdW1tYXJ5LmNvbXBlbnNpdHkgfHwgMCkgKw0KICAgICAgICAgICAgICAgICBOdW1iZXIoc3VtbWFyeS5vdGhlckFtb3VudHNPd2VkIHx8IDApICsNCiAgICAgICAgICAgICAgICAgTnVtYmVyKHN1bW1hcnkuaW5zdXJhbmNlIHx8IDApDQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgcmV0dXJuIDANCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOafpeeci+ivieiuvOi0ueivpuaDhSAqLw0KICAgIHZpZXdMaXRpZ2F0aW9uRmVlRGV0YWlscyhjYXNlSWQsIGZlZVR5cGUpIHsNCiAgICAgIC8vIOehruS/nWNhc2VJZOaYr+ato+ehrueahOexu+Weiw0KICAgICAgY29uc3Qgbm9ybWFsaXplZENhc2VJZCA9IHR5cGVvZiBjYXNlSWQgPT09ICdzdHJpbmcnID8gcGFyc2VJbnQoY2FzZUlkKSA6IE51bWJlcihjYXNlSWQpDQoNCiAgICAgIC8vIOWPquWkhOeQhuivieiuvOi0ueivpuaDhQ0KICAgICAgaWYgKGZlZVR5cGUgPT09ICdsaXRpZ2F0aW9uJykgew0KICAgICAgICBjb25zdCB0aXRsZSA9ICfor4norrzotLnor6bmg4UnDQogICAgICAgIGNvbnN0IGNvbnRlbnQgPSB0aGlzLmZvcm1hdExpdGlnYXRpb25GZWVEZXRhaWwobm9ybWFsaXplZENhc2VJZCwgWw0KICAgICAgICAgICdsYXd5ZXJGZWUnLCAnbGl0aWdhdGlvbkZlZScsICdwcmVzZXJ2YXRpb25GZWUnLCAnc3VydmVpbGxhbmNlRmVlJywNCiAgICAgICAgICAnYW5ub3VuY2VtZW50RmVlJywgJ2FwcHJhaXNhbEZlZScsICdleGVjdXRpb25GZWUnLCAncGVuYWx0eScsDQogICAgICAgICAgJ2d1YXJhbnRlZUZlZScsICdpbnRlcm1lZGlhcnlGZWUnLCAnY29tcGVuc2l0eScsICdvdGhlckFtb3VudHNPd2VkJywgJ2luc3VyYW5jZScNCiAgICAgICAgXSkNCg0KICAgICAgICB0aGlzLiRhbGVydChjb250ZW50LCB0aXRsZSwgew0KICAgICAgICAgIGRhbmdlcm91c2x5VXNlSFRNTFN0cmluZzogdHJ1ZSwNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOagvOW8j+WMluazleiviei0ueeUqOivpuaDhSAqLw0KICAgIGZvcm1hdExpdGlnYXRpb25GZWVEZXRhaWwoY2FzZUlkLCBmZWVUeXBlcykgew0KICAgICAgY29uc3Qgc3VtbWFyeSA9IHRoaXMubGl0aWdhdGlvbkNvc3RTdW1tYXJ5W2Nhc2VJZF0NCiAgICAgIGlmICghc3VtbWFyeSkgcmV0dXJuICc8cD7mmoLml6DotLnnlKjmlbDmja48L3A+Jw0KDQogICAgICBjb25zdCBmZWVMYWJlbHMgPSB7DQogICAgICAgIGp1ZGdtZW50QW1vdW50OiAn5Yik5Yaz6YeR6aKdJywNCiAgICAgICAgaW50ZXJlc3Q6ICfliKnmga8nLA0KICAgICAgICBsYXd5ZXJGZWU6ICflvovluIjotLknLA0KICAgICAgICBsaXRpZ2F0aW9uRmVlOiAn5rOV6K+J6LS5JywNCiAgICAgICAgcHJlc2VydmF0aW9uRmVlOiAn5L+d5YWo6LS5JywNCiAgICAgICAgc3VydmVpbGxhbmNlRmVlOiAn5biD5o6n6LS5JywNCiAgICAgICAgYW5ub3VuY2VtZW50RmVlOiAn5YWs5ZGK6LS5JywNCiAgICAgICAgYXBwcmFpc2FsRmVlOiAn6K+E5Lyw6LS5JywNCiAgICAgICAgZXhlY3V0aW9uRmVlOiAn5omn6KGM6LS5JywNCiAgICAgICAgcGVuYWx0eTogJ+i/nee6pumHkScsDQogICAgICAgIGd1YXJhbnRlZUZlZTogJ+aLheS/nei0uScsDQogICAgICAgIGludGVybWVkaWFyeUZlZTogJ+WxhemXtOi0uScsDQogICAgICAgIGNvbXBlbnNpdHk6ICfku6Plgb/ph5EnLA0KICAgICAgICBvdGhlckFtb3VudHNPd2VkOiAn5YW25LuW5qyg5qy+JywNCiAgICAgICAgaW5zdXJhbmNlOiAn5L+d6Zmp6LS5Jw0KICAgICAgfQ0KDQogICAgICBsZXQgaHRtbCA9ICc8ZGl2IHN0eWxlPSJ0ZXh0LWFsaWduOiBsZWZ0OyI+Jw0KICAgICAgbGV0IHRvdGFsID0gMA0KDQogICAgICBmZWVUeXBlcy5mb3JFYWNoKGZlZVR5cGUgPT4gew0KICAgICAgICBjb25zdCBhbW91bnQgPSBOdW1iZXIoc3VtbWFyeVtmZWVUeXBlXSB8fCAwKQ0KICAgICAgICBpZiAoYW1vdW50ID4gMCkgew0KICAgICAgICAgIGh0bWwgKz0gYDxwPiR7ZmVlTGFiZWxzW2ZlZVR5cGVdfTog77+lJHt0aGlzLmZvcm1hdE1vbmV5KGFtb3VudCl9PC9wPmANCiAgICAgICAgICB0b3RhbCArPSBhbW91bnQNCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgICAgaWYgKGZlZVR5cGVzLmxlbmd0aCA+IDEgJiYgdG90YWwgPiAwKSB7DQogICAgICAgIGh0bWwgKz0gYDxocj48cD48c3Ryb25nPuWQiOiuoTog77+lJHt0aGlzLmZvcm1hdE1vbmV5KHRvdGFsKX08L3N0cm9uZz48L3A+YA0KICAgICAgfQ0KDQogICAgICBodG1sICs9ICc8L2Rpdj4nDQogICAgICByZXR1cm4gaHRtbCB8fCAnPHA+5pqC5peg6LS555So5pWw5o2uPC9wPicNCiAgICB9LA0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgLy8g5ZCM5q2l5pS+5qy+6ZO26KGM562b6YCJ5p2h5Lu25Yiw5ZCO56uv5pyf5pyb55qE5a2X5q615ZCNDQogICAgICBpZiAodGhpcy5xdWVyeVBhcmFtcy5sZW5kaW5nQmFuaykgew0KICAgICAgICAvLyDmoLnmja7pgInkuK3nmoTpk7booYxJROafpeaJvuWvueW6lOeahOmTtuihjOWQjeensA0KICAgICAgICBjb25zdCBzZWxlY3RlZEJhbmsgPSB0aGlzLmxlbmRpbmdCYW5rTGlzdC5maW5kKGJhbmsgPT4gYmFuay52YWx1ZSA9PT0gdGhpcy5xdWVyeVBhcmFtcy5sZW5kaW5nQmFuaykNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy7mlL7mrL7pk7booYwgPSBzZWxlY3RlZEJhbmsgPyBzZWxlY3RlZEJhbmsubGFiZWwgOiAnJw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy7mlL7mrL7pk7booYwgPSAnJw0KICAgICAgfQ0KDQogICAgICAvLyDlpITnkIbms5Xor4nml7bpl7TljLrpl7TnrZvpgIkNCiAgICAgIGlmICh0aGlzLnF1ZXJ5UGFyYW1zLmxpdGlnYXRpb25UaW1lICYmIHRoaXMucXVlcnlQYXJhbXMubGl0aWdhdGlvblRpbWUubGVuZ3RoID09PSAyKSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMubGl0aWdhdGlvblN0YXJ0RGF0ZSA9IHRoaXMucXVlcnlQYXJhbXMubGl0aWdhdGlvblRpbWVbMF0NCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5saXRpZ2F0aW9uRW5kRGF0ZSA9IHRoaXMucXVlcnlQYXJhbXMubGl0aWdhdGlvblRpbWVbMV0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMubGl0aWdhdGlvblN0YXJ0RGF0ZSA9ICcnDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMubGl0aWdhdGlvbkVuZERhdGUgPSAnJw0KICAgICAgfQ0KDQogICAgICAvLyDlpITnkIbml6Xlv5fnsbvlnovnrZvpgIkgLSDlsIbliY3nq6/nmoRsb2dUeXBl5Lyg6YCS57uZ5ZCO56uvDQogICAgICAvLyBsb2dUeXBl5a2X5q615Lya55u05o6l5Lyg6YCS57uZ5ZCO56uv77yM5LiN6ZyA6KaB6aKd5aSW5aSE55CGDQoNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDENCiAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgfSwNCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICDotLfmrL7kuro6ICcnLA0KICAgICAgICDouqvku73or4E6ICcnLA0KICAgICAgICDovabovobniYzlj7c6ICcnLA0KICAgICAgICDovabovobnirbmgIE6ICcnLA0KICAgICAgICDlh7rljZXmuKDpgZM6ICcnLA0KICAgICAgICBsZW5kaW5nQmFuazogJycsDQogICAgICAgIOaUvuasvumTtuihjDogJycsDQogICAgICAgIGxpdGlnYXRpb25UaW1lOiAnJywNCiAgICAgICAgbGl0aWdhdGlvblN0YXJ0RGF0ZTogJycsDQogICAgICAgIGxpdGlnYXRpb25FbmREYXRlOiAnJywNCiAgICAgICAg5rOV6K+J5paH5ZGYOiAnJywNCiAgICAgICAg6Lef5YKs5ZGYOiAnJywNCiAgICAgICAg5qGI5Lu26LSf6LSj5Lq6OiAnJywNCiAgICAgICAg5rOV6K+J5a2Q54q25oCBOiAnJywNCiAgICAgICAgbG9nVHlwZTogJycsDQogICAgICAgIOivieiuvOazlemZojogJycsDQogICAgICB9DQogICAgICB0aGlzLmdldExpc3QoKQ0KICAgIH0sDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCkNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvLyDlkK/liqjms5Xor4nlvLnnqpcNCiAgICBvcGVuTGl0aWdhdGlvbkZvcm0ocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRSb3cgPSByb3cgfHwge30NCiAgICAgIHRoaXMuJHJlZnMubGl0aWdhdGlvbkZvcm0ub3BlbiAmJiB0aGlzLiRyZWZzLmxpdGlnYXRpb25Gb3JtLm9wZW4oKQ0KICAgIH0sDQogICAgb25MaXRpZ2F0aW9uRm9ybUNvbmZpcm0oKSB7DQogICAgICAvLyDlpITnkIbnoa7orqTpgLvovpENCiAgICB9LA0KICAgIC8vIOazleiviei0ueeUqOW8ueeqlw0KICAgIG9wZW5MaXRpZ2F0aW9uRmVlRm9ybShyb3cpIHsNCiAgICAgIHRoaXMuY3VycmVudFJvdyA9IHJvdyB8fCB7fQ0KICAgICAgdGhpcy4kcmVmcy5saXRpZ2F0aW9uRmVlRm9ybS5vcGVuICYmIHRoaXMuJHJlZnMubGl0aWdhdGlvbkZlZUZvcm0ub3BlbigpDQogICAgfSwNCiAgICBvbkxpdGlnYXRpb25GZWVGb3JtQ29uZmlybSgpIHsNCiAgICAgIC8vIOWkhOeQhuehruiupOmAu+i+kQ0KICAgIH0sDQogICAgLy8g5o+Q5Lqk5pel5b+X5by556qXDQogICAgb3BlbkxpdGlnYXRpb25Mb2dGb3JtKHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50Um93ID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyb3cpKQ0KICAgICAgdGhpcy4kcmVmcy5saXRpZ2F0aW9uTG9nRm9ybS5vcGVuRGlhbG9nICYmIHRoaXMuJHJlZnMubGl0aWdhdGlvbkxvZ0Zvcm0ub3BlbkRpYWxvZygpDQogICAgfSwNCiAgICBvbkxpdGlnYXRpb25Mb2dGb3JtQ29uZmlybSgpIHsNCiAgICAgIC8vIOWkhOeQhuehruiupOmAu+i+kQ0KICAgIH0sDQogICAgLy8g5pel5b+X5p+l55yL5by556qXDQogICAgb3BlbkxpdGlnYXRpb25Mb2dWaWV3KHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50Um93ID0gcm93IHx8IHt9DQogICAgICB0aGlzLiRyZWZzLmxpdGlnYXRpb25Mb2dWaWV3Lm9wZW5EaWFsb2cgJiYgdGhpcy4kcmVmcy5saXRpZ2F0aW9uTG9nVmlldy5vcGVuRGlhbG9nKCkNCiAgICB9LA0KICAgIC8vIOaJvui9puaMiemSruW8ueeqlw0KICAgIG9wZW5EaXNwYXRjaFZlaGljbGVGb3JtKHJvdykgew0KICAgICAgdGhpcy5kaXNwYXRjaExvYW5JZCA9IHJvdy7mtYHnqIvluo/lj7cNCiAgICAgIHRoaXMuJHJlZnMuZGlzcGF0Y2hWZWhpY2xlRm9ybS5vcGVuRGlhbG9nKCkNCiAgICB9LA0KICAgIC8qKiDorqHnrpflvoXov73lgb/mrKDmrL7mgLvpop0gKi8NCiAgICBjYWxjdWxhdGVUb3RhbERlYnQocm93KSB7DQogICAgICBjb25zdCBjYXNlSWQgPSByb3cu5bqP5Y+3DQoNCiAgICAgIC8vIOiOt+WPluWQhOmhuei0ueeUqA0KICAgICAgY29uc3QganVkZ21lbnRBbW91bnQgPSB0aGlzLmdldExpdGlnYXRpb25GZWVBbW91bnQoY2FzZUlkLCAnanVkZ21lbnRBbW91bnQnKSAvLyDliKTlhrPph5Hpop0NCiAgICAgIGNvbnN0IGludGVyZXN0ID0gdGhpcy5nZXRMaXRpZ2F0aW9uRmVlQW1vdW50KGNhc2VJZCwgJ2ludGVyZXN0JykgLy8g5Yip5oGvDQogICAgICBjb25zdCBsaXRpZ2F0aW9uQ29zdHMgPSB0aGlzLmdldExpdGlnYXRpb25GZWVBbW91bnQoY2FzZUlkLCAnbGl0aWdhdGlvbicpIC8vIOazleiviei0ueeUqA0KICAgICAgY29uc3QgdW5zdWVkQW1vdW50ID0gdGhpcy5jYWxjdWxhdGVVbnN1ZWRBbW91bnQocm93KSAvLyDmnKrotbfor4nph5Hpop0NCg0KICAgICAgLy8g6K6h566X5oC76aKd77ya5Yik5Yaz6YeR6aKdICsg5Yip5oGvICsg5rOV6K+J6LS555SoICsg5pyq6LW36K+J6YeR6aKdDQogICAgICBjb25zdCB0b3RhbCA9IE51bWJlcihqdWRnbWVudEFtb3VudCkgKyBOdW1iZXIoaW50ZXJlc3QpICsgTnVtYmVyKGxpdGlnYXRpb25Db3N0cykgKyBOdW1iZXIodW5zdWVkQW1vdW50KQ0KDQogICAgICByZXR1cm4gdG90YWwNCiAgICB9LA0KDQogICAgLyoqIOafpeeci+W+hei/veWBv+asoOasvuivpuaDhSAqLw0KICAgIHZpZXdUb3RhbERlYnREZXRhaWxzKHJvdykgew0KICAgICAgY29uc3QgY2FzZUlkID0gcm93LuW6j+WPtw0KDQogICAgICAvLyDojrflj5blkITpobnotLnnlKgNCiAgICAgIGNvbnN0IGp1ZGdtZW50QW1vdW50ID0gdGhpcy5nZXRMaXRpZ2F0aW9uRmVlQW1vdW50KGNhc2VJZCwgJ2p1ZGdtZW50QW1vdW50JykNCiAgICAgIGNvbnN0IGludGVyZXN0ID0gdGhpcy5nZXRMaXRpZ2F0aW9uRmVlQW1vdW50KGNhc2VJZCwgJ2ludGVyZXN0JykNCiAgICAgIGNvbnN0IGxpdGlnYXRpb25Db3N0cyA9IHRoaXMuZ2V0TGl0aWdhdGlvbkZlZUFtb3VudChjYXNlSWQsICdsaXRpZ2F0aW9uJykNCiAgICAgIGNvbnN0IHVuc3VlZEFtb3VudCA9IHRoaXMuY2FsY3VsYXRlVW5zdWVkQW1vdW50KHJvdykNCiAgICAgIGNvbnN0IHRvdGFsID0gdGhpcy5jYWxjdWxhdGVUb3RhbERlYnQocm93KQ0KDQogICAgICBsZXQgaHRtbCA9ICc8ZGl2IHN0eWxlPSJ0ZXh0LWFsaWduOiBsZWZ0OyI+Jw0KICAgICAgaHRtbCArPSBgPHA+5Yik5Yaz6YeR6aKdOiDvv6Uke3RoaXMuZm9ybWF0TW9uZXkoanVkZ21lbnRBbW91bnQpfTwvcD5gDQogICAgICBodG1sICs9IGA8cD7liKnmga86IO+/pSR7dGhpcy5mb3JtYXRNb25leShpbnRlcmVzdCl9PC9wPmANCiAgICAgIGh0bWwgKz0gYDxwPuazleiviei0ueeUqDog77+lJHt0aGlzLmZvcm1hdE1vbmV5KGxpdGlnYXRpb25Db3N0cyl9PC9wPmANCiAgICAgIGh0bWwgKz0gYDxwPuacqui1t+iviemHkeminTog77+lJHt0aGlzLmZvcm1hdE1vbmV5KHVuc3VlZEFtb3VudCl9PC9wPmANCiAgICAgIGh0bWwgKz0gYDxocj48cD48c3Ryb25nPuW+hei/veWBv+asoOasvuaAu+iuoTog77+lJHt0aGlzLmZvcm1hdE1vbmV5KHRvdGFsKX08L3N0cm9uZz48L3A+YA0KICAgICAgaHRtbCArPSAnPC9kaXY+Jw0KDQogICAgICB0aGlzLiRhbGVydChodG1sLCAn5b6F6L+95YG/5qyg5qy+6K+m5oOFJywgew0KICAgICAgICBkYW5nZXJvdXNseVVzZUhUTUxTdHJpbmc6IHRydWUsDQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJw0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5omT5byA5pel5bi46LS555So55Sz6K+35by556qXDQogICAgb3BlbkRhaWx5RXhwZW5zZURpYWxvZyhyb3cpIHsNCiAgICAgIC8vIOmAmui/h3JlZuiwg+eUqOazleiviei0ueeUqOihqOWNlee7hOS7tueahOaWueazle+8jOS8oOWFpeahiOS7tklEDQogICAgICB0aGlzLiRyZWZzLmxpdGlnYXRpb25GZWVGb3JtLm9wZW5EYWlseUV4cGVuc2VEaWFsb2cocm93LuW6j+WPtykNCiAgICB9LA0KICAgIC8vIOafpeeci+i0t+asvuS6uuS/oeaBrw0KICAgIG9wZW5Vc2VySW5mbyhjdXN0b21lckluZm8pIHsNCiAgICAgIHRoaXMuY3VzdG9tZXJJbmZvID0gY3VzdG9tZXJJbmZvDQogICAgICB0aGlzLnVzZXJJbmZvVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIC8vIOafpeeci+i9pui+huS/oeaBrw0KICAgIGNoZWNrQ2FyKHBsYXRlTm8pIHsNCiAgICAgIHRoaXMucGxhdGVObyA9IHBsYXRlTm8NCiAgICAgIHRoaXMuY2FyU2hvdyA9IHRydWUNCiAgICB9LA0KICAgIC8vIOiOt+WPlui1t+ivieexu+Wei+aWh+Wtlw0KICAgIGdldExhd3N1aXRUeXBlVGV4dCh2YWx1ZSkgew0KICAgICAgY29uc3QgaXRlbSA9IHRoaXMubGF3c3VpdFR5cGVMaXN0LmZpbmQoaXRlbSA9PiBpdGVtLnZhbHVlID09PSB2YWx1ZSkNCiAgICAgIHJldHVybiBpdGVtID8gaXRlbS5sYWJlbCA6IHZhbHVlDQogICAgfSwNCiAgICAvLyDojrflj5botbfor4nlhoXlrrnmloflrZcNCiAgICBnZXRMYXdzdWl0Q29udGVudFRleHQodmFsdWUpIHsNCiAgICAgIGlmICghdmFsdWUpIHJldHVybiAnLScNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5bCd6K+V6Kej5p6QSlNPTuaVsOe7hO+8iOWkmumAieagvOW8j++8iQ0KICAgICAgICBjb25zdCBjb250ZW50QXJyYXkgPSBKU09OLnBhcnNlKHZhbHVlKQ0KICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShjb250ZW50QXJyYXkpKSB7DQogICAgICAgICAgcmV0dXJuIGNvbnRlbnRBcnJheS5tYXAodmFsID0+IHsNCiAgICAgICAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLmxhd3N1aXRDb250ZW50TGlzdC5maW5kKGl0ZW0gPT4gaXRlbS52YWx1ZSA9PT0gdmFsKQ0KICAgICAgICAgICAgcmV0dXJuIGl0ZW0gPyBpdGVtLmxhYmVsIDogdmFsDQogICAgICAgICAgfSkuam9pbign44CBJykNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCB7DQogICAgICAgIC8vIOWmguaenOS4jeaYr0pTT07moLzlvI/vvIzmjInljZXpgInlpITnkIYNCiAgICAgICAgY29uc3QgaXRlbSA9IHRoaXMubGF3c3VpdENvbnRlbnRMaXN0LmZpbmQoaXRlbSA9PiBpdGVtLnZhbHVlID09PSB2YWx1ZSkNCiAgICAgICAgcmV0dXJuIGl0ZW0gPyBpdGVtLmxhYmVsIDogdmFsdWUNCiAgICAgIH0NCg0KICAgICAgLy8g6buY6K6k6L+U5Zue5Y6f5YC8DQogICAgICByZXR1cm4gdmFsdWUNCiAgICB9LA0KICAgIC8vIOiOt+WPluWCrOiusOexu+Wei+aWh+acrA0KICAgIGdldFVyZ2VTdGF0dXNUZXh0KHZhbHVlKSB7DQogICAgICBjb25zdCB1cmdlU3RhdHVzTWFwID0gew0KICAgICAgICAxOiAn57un57ut6Lef6LiqJywNCiAgICAgICAgMjogJ+e6puWumui/mOasvicsDQogICAgICAgIDM6ICfml6Dms5Xot5/ov5snDQogICAgICB9DQogICAgICByZXR1cm4gdXJnZVN0YXR1c01hcFt2YWx1ZV0gfHwgdmFsdWUNCiAgICB9LA0KICAgIC8vIOiOt+WPlui9pui+hueKtuaAgeaWh+acrA0KICAgIGdldENhclN0YXR1c1RleHQodmFsdWUpIHsNCiAgICAgIC8vIOS9v+eUqOW3suacieeahCBjYXJTdGF0dXNMaXN0IOaVsOaNrg0KICAgICAgY29uc3Qgc3RhdHVzSXRlbSA9IHRoaXMuY2FyU3RhdHVzTGlzdC5maW5kKGl0ZW0gPT4gaXRlbS52YWx1ZSA9PSB2YWx1ZSkNCiAgICAgIHJldHVybiBzdGF0dXNJdGVtID8gc3RhdHVzSXRlbS5sYWJlbCA6IHZhbHVlDQogICAgfSwNCiAgICAvLyDmmL7npLrmrKDmrL7or6bmg4XlvLnnqpcNCiAgICBzaG93RGVidERldGFpbChyb3cpIHsNCiAgICAgIHRoaXMuY3VycmVudERlYnRSb3cgPSByb3cNCiAgICAgIHRoaXMuZGVidERldGFpbFZpc2libGUgPSB0cnVlDQogICAgfSwNCiAgICAvLyDmoLzlvI/ljJbph5Hpop3mmL7npLoNCiAgICBmb3JtYXRNb25leShhbW91bnQpIHsNCiAgICAgIGlmIChhbW91bnQgPT09IG51bGwgfHwgYW1vdW50ID09PSB1bmRlZmluZWQgfHwgYW1vdW50ID09PSAnJykgew0KICAgICAgICByZXR1cm4gJzAuMDAnDQogICAgICB9DQogICAgICByZXR1cm4gTnVtYmVyKGFtb3VudCkudG9Mb2NhbGVTdHJpbmcoJ3poLUNOJywgew0KICAgICAgICBtaW5pbXVtRnJhY3Rpb25EaWdpdHM6IDIsDQogICAgICAgIG1heGltdW1GcmFjdGlvbkRpZ2l0czogMg0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOiuoeeul+acqui1t+iviemHkeminQ0KICAgIGNhbGN1bGF0ZVVuc3VlZEFtb3VudChyb3cpIHsNCiAgICAgIGNvbnN0IHJlbWFpbmluZ0Ftb3VudCA9IE51bWJlcihyb3cu5Ymp5L2Z6YeR6aKdKSB8fCAwDQogICAgICBjb25zdCBzdWVkQW1vdW50ID0gTnVtYmVyKHJvdy7otbfor4nph5Hpop0pIHx8IDANCiAgICAgIGNvbnN0IHVuc3VlZEFtb3VudCA9IHJlbWFpbmluZ0Ftb3VudCAtIHN1ZWRBbW91bnQNCiAgICAgIHJldHVybiB1bnN1ZWRBbW91bnQgPiAwID8gdW5zdWVkQW1vdW50IDogMA0KICAgIH0sDQoNCiAgICAvLyDmmL7npLrmnKrotbfor4nlhoXlrrnor6bmg4UNCiAgICBhc3luYyBzaG93VW5zdWVkQ29udGVudERldGFpbChyb3cpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOiOt+WPluivpei0t+asvueahOaJgOacieazleivieahiOS7tg0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldExpdGlnYXRpb25CeUxvYW5JZChyb3cu5rWB56iL5bqP5Y+3KQ0KDQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIC8vIOiOt+WPluaJgOacieW3suWQr+WKqOeahOazleivieahiOS7tueahOi1t+ivieWGheWuuQ0KICAgICAgICAgIGNvbnN0IHVzZWRQcm9zZWN1dGlvbkNvbnRlbnRzID0gW10NCg0KICAgICAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaChjYXNlSXRlbSA9PiB7DQogICAgICAgICAgICAvLyDliKTmlq3ms5Xor4nmmK/lkKblt7LlkK/liqgNCiAgICAgICAgICAgIGlmICh0aGlzLmlzTGl0aWdhdGlvblN0YXJ0ZWQoY2FzZUl0ZW0pKSB7DQogICAgICAgICAgICAgIGNvbnN0IGNvbnRlbnQgPSBjYXNlSXRlbS5wcm9zZWN1dGlvbkNvbnRlbnQNCiAgICAgICAgICAgICAgaWYgKGNvbnRlbnQpIHsNCiAgICAgICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICAgICAgLy8g5bCd6K+V6Kej5p6Q5Li65pWw57uE77yI5aSa6YCJ77yJDQogICAgICAgICAgICAgICAgICBjb25zdCBjb250ZW50QXJyYXkgPSBKU09OLnBhcnNlKGNvbnRlbnQpDQogICAgICAgICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShjb250ZW50QXJyYXkpKSB7DQogICAgICAgICAgICAgICAgICAgIHVzZWRQcm9zZWN1dGlvbkNvbnRlbnRzLnB1c2goLi4uY29udGVudEFycmF5KQ0KICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgdXNlZFByb3NlY3V0aW9uQ29udGVudHMucHVzaChjb250ZW50KQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0gY2F0Y2ggew0KICAgICAgICAgICAgICAgICAgLy8g5aaC5p6c5LiN5pivSlNPTuagvOW8j++8jOaMieWNlemAieWkhOeQhg0KICAgICAgICAgICAgICAgICAgdXNlZFByb3NlY3V0aW9uQ29udGVudHMucHVzaChjb250ZW50KQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICAvLyDljrvph43lt7Lkvb/nlKjnmoTotbfor4nlhoXlrrkNCiAgICAgICAgICBjb25zdCB1bmlxdWVVc2VkQ29udGVudHMgPSBbLi4ubmV3IFNldCh1c2VkUHJvc2VjdXRpb25Db250ZW50cyldDQoNCiAgICAgICAgICAvLyDmib7lh7rmnKrkvb/nlKjnmoTotbfor4nlhoXlrrkNCiAgICAgICAgICBjb25zdCBhbGxQcm9zZWN1dGlvbkNvbnRlbnRzID0gdGhpcy5sYXdzdWl0Q29udGVudExpc3QNCiAgICAgICAgICBjb25zdCB1bnVzZWRDb250ZW50cyA9IGFsbFByb3NlY3V0aW9uQ29udGVudHMuZmlsdGVyKGl0ZW0gPT4NCiAgICAgICAgICAgICF1bmlxdWVVc2VkQ29udGVudHMuaW5jbHVkZXMoaXRlbS52YWx1ZSkNCiAgICAgICAgICApDQoNCiAgICAgICAgICAvLyDorqHnrpfmnKrotbfor4nlhoXlrrnlr7nlupTnmoTph5Hpop0NCiAgICAgICAgICBsZXQgaHRtbCA9ICc8ZGl2IHN0eWxlPSJ0ZXh0LWFsaWduOiBsZWZ0OyI+Jw0KICAgICAgICAgIGxldCB0b3RhbFVuc3VlZEFtb3VudCA9IDANCg0KICAgICAgICAgIGlmICh1bnVzZWRDb250ZW50cy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICBodG1sICs9ICc8aDQgc3R5bGU9Im1hcmdpbi1ib3R0b206IDE1cHg7IGNvbG9yOiAjMzAzMTMzOyI+5pyq6LW36K+J5YaF5a6577yaPC9oND4nDQoNCiAgICAgICAgICAgIHVudXNlZENvbnRlbnRzLmZvckVhY2goY29udGVudCA9PiB7DQogICAgICAgICAgICAgIGxldCBhbW91bnQgPSAwDQogICAgICAgICAgICAgIC8vIOagueaNrui1t+ivieWGheWuueexu+Wei+iuoeeul+WvueW6lOmHkeminQ0KICAgICAgICAgICAgICBzd2l0Y2ggKGNvbnRlbnQudmFsdWUpIHsNCiAgICAgICAgICAgICAgICBjYXNlICcxJzogLy8g6ZO26KGM5Luj5YG/6YeR6aKdDQogICAgICAgICAgICAgICAgICBhbW91bnQgPSBOdW1iZXIocm93LumTtuihjOWJqeS9meacqui/mOS7o+WBv+mHkSkgfHwgMA0KICAgICAgICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICAgICAgICBjYXNlICcyJzogLy8g5Luj5omj6YeR6aKdDQogICAgICAgICAgICAgICAgICBhbW91bnQgPSBOdW1iZXIocm93LuS7o+aJo+WJqeS9meacqui/mOS7o+WBv+mHkSkgfHwgMA0KICAgICAgICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICAgICAgICBjYXNlICczJzogLy8g6L+d57qm6YeRDQogICAgICAgICAgICAgICAgICBhbW91bnQgPSBOdW1iZXIocm93LuWJqeS9meacqui/mOi/nee6pumHkemHkeminSkgfHwgMA0KICAgICAgICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICAgICAgICBjYXNlICc0JzogLy8g5YW25LuW5qyg5qy+DQogICAgICAgICAgICAgICAgICBhbW91bnQgPSBOdW1iZXIocm93LuWJqeS9meacqui/mOWFtuS7luasoOasvikgfHwgMA0KICAgICAgICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8vIOaYvuekuuaJgOacieacqui1t+ivieWGheWuue+8jOS4jeeuoemHkemineaYr+WQpuS4ujANCiAgICAgICAgICAgICAgaHRtbCArPSBgPHA+JHtjb250ZW50LmxhYmVsfTog77+lJHt0aGlzLmZvcm1hdE1vbmV5KGFtb3VudCl9PC9wPmANCiAgICAgICAgICAgICAgdG90YWxVbnN1ZWRBbW91bnQgKz0gYW1vdW50DQogICAgICAgICAgICB9KQ0KDQogICAgICAgICAgICBodG1sICs9IGA8aHI+PHA+PHN0cm9uZz7mnKrotbfor4nph5Hpop3lkIjorqE6IO+/pSR7dGhpcy5mb3JtYXRNb25leSh0b3RhbFVuc3VlZEFtb3VudCl9PC9zdHJvbmc+PC9wPmANCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgaHRtbCArPSAnPHA+5omA5pyJ6LW36K+J5YaF5a656YO95bey6KKr6YCJ5oupPC9wPicNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBodG1sICs9ICc8L2Rpdj4nDQoNCiAgICAgICAgICB0aGlzLiRhbGVydChodG1sLCAn5pyq6LW36K+J5YaF5a656K+m5oOFJywgew0KICAgICAgICAgICAgZGFuZ2Vyb3VzbHlVc2VIVE1MU3RyaW5nOiB0cnVlLA0KICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponDQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bms5Xor4nmoYjku7bmlbDmja7lpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bmnKrotbfor4nlhoXlrrnor6bmg4XlpLHotKUnKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDliKTmlq3ms5Xor4nmmK/lkKblt7LlkK/liqgNCiAgICBpc0xpdGlnYXRpb25TdGFydGVkKGNhc2VEYXRhKSB7DQogICAgICAvLyDmo4Dmn6XlhbPplK7lrZfmrrXmmK/lkKbpg73mnInlgLwNCiAgICAgIGNvbnN0IGhhc0xpdGlnYXRpb25UeXBlID0gY2FzZURhdGEubGl0aWdhdGlvblR5cGUgIT0gbnVsbCAmJiBjYXNlRGF0YS5saXRpZ2F0aW9uVHlwZSAhPT0gJycNCiAgICAgIGNvbnN0IGhhc1Byb3NlY3V0aW9uVHlwZSA9IGNhc2VEYXRhLnByb3NlY3V0aW9uVHlwZSAhPSBudWxsICYmIGNhc2VEYXRhLnByb3NlY3V0aW9uVHlwZSAhPT0gJycNCiAgICAgIGNvbnN0IGhhc1Byb3NlY3V0aW9uQ29udGVudCA9IGNhc2VEYXRhLnByb3NlY3V0aW9uQ29udGVudCAhPSBudWxsICYmIGNhc2VEYXRhLnByb3NlY3V0aW9uQ29udGVudCAhPT0gJycNCiAgICAgIGNvbnN0IGhhc0xpdGlnYXRpb25TdGFydERheSA9IGNhc2VEYXRhLmxpdGlnYXRpb25TdGFydERheSAhPSBudWxsICYmIGNhc2VEYXRhLmxpdGlnYXRpb25TdGFydERheSAhPT0gJycNCg0KICAgICAgcmV0dXJuIGhhc0xpdGlnYXRpb25UeXBlICYmIGhhc1Byb3NlY3V0aW9uVHlwZSAmJiBoYXNQcm9zZWN1dGlvbkNvbnRlbnQgJiYgaGFzTGl0aWdhdGlvblN0YXJ0RGF5DQogICAgfSwNCiAgfSwNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0YA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/litigation/litigation", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 启动法诉弹窗 -->\r\n    <litigation-form ref=\"litigationForm\" :data=\"currentRow\" />\r\n\r\n    <!-- 法诉费用弹窗 -->\r\n    <litigation-fee-form ref=\"litigationFeeForm\" :data=\"currentRow\" />\r\n\r\n    <!-- 提交日志弹窗 -->\r\n    <litigation-log-form ref=\"litigationLogForm\" :data=\"currentRow\" />\r\n\r\n    <!-- 日志查看弹窗 -->\r\n    <litigation-log-view ref=\"litigationLogView\" :data=\"currentRow\" />\r\n\r\n    <!-- 派单找车组件 -->\r\n    <dispatch-vehicle-form ref=\"dispatchVehicleForm\" :loanId=\"dispatchLoanId\" />\r\n\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"\" prop=\"贷款人\">\r\n        <el-input v-model=\"queryParams.贷款人\" placeholder=\"贷款人账户、姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"身份证\">\r\n        <el-input v-model=\"queryParams.身份证\" placeholder=\"贷款人身份证号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"车辆牌号\">\r\n        <el-input v-model=\"queryParams.车辆牌号\" placeholder=\"车牌号码\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"车辆状态\">\r\n        <el-select v-model=\"queryParams.车辆状态\" placeholder=\"车辆状态\" clearable>\r\n          <el-option v-for=\"dict in carStatusList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"出单渠道\">\r\n        <el-input v-model=\"queryParams.出单渠道\" placeholder=\"录单渠道名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"lendingBank\">\r\n        <el-select v-model=\"queryParams.lendingBank\" placeholder=\"放款银行\" clearable>\r\n          <el-option v-for=\"dict in lendingBankList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"法诉时间\">\r\n        <el-date-picker\r\n          v-model=\"queryParams.litigationTime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <template v-if=\"showMore\">\r\n        <el-form-item label=\"\" prop=\"法诉文员\">\r\n          <el-input v-model=\"queryParams.法诉文员\" placeholder=\"法诉文员\" clearable @keyup.enter.native=\"handleQuery\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"跟催员\">\r\n          <el-input v-model=\"queryParams.跟催员\" placeholder=\"跟催员\" clearable @keyup.enter.native=\"handleQuery\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"案件负责人\">\r\n          <el-input v-model=\"queryParams.案件负责人\" placeholder=\"案件负责人\" clearable @keyup.enter.native=\"handleQuery\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"法诉子状态\">\r\n          <litigation-status v-model=\"queryParams.法诉子状态\" placeholder=\"法诉状态\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"logType\">\r\n          <el-select v-model=\"queryParams.logType\" placeholder=\"日志类型\" clearable>\r\n            <el-option v-for=\"dict in logTypeList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"诉讼法院\">\r\n          <el-select v-model=\"queryParams.诉讼法院\" placeholder=\"诉讼法院\" clearable>\r\n            <el-option v-for=\"dict in lawsuitCourtList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </template>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        <el-button type=\"text\" size=\"mini\" @click=\"showMore = !showMore\">\r\n          {{ showMore ? '收起' : '更多' }}\r\n          <i :class=\"showMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\r\n        </el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"litigation_caseList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"55\" fixed=\"left\" />\r\n      <el-table-column label=\"法诉文员\" align=\"center\" prop=\"法诉文员\" width=\"100\" />\r\n      <el-table-column label=\"发起法诉日\" align=\"center\" prop=\"发起法诉日\" width=\"110\" />\r\n      <el-table-column label=\"案件启动日\" align=\"center\" prop=\"案件启动日\" width=\"110\" />\r\n      <el-table-column label=\"日志类型\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getUrgeStatusText(scope.row.日志类型) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"日志更新日\" align=\"center\" prop=\"日志更新日\" width=\"110\" />\r\n      <el-table-column label=\"法诉状态\" align=\"center\" prop=\"法诉子状态\" width=\"100\" />\r\n      <el-table-column label=\"贷款人\" align=\"center\" prop=\"贷款人\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"openUserInfo({ customerId: scope.row.客户ID, applyId: scope.row.申请编号 })\">\r\n            {{ scope.row.贷款人 }}\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"身份证\" align=\"center\" prop=\"身份证\" width=\"150\" />\r\n      <el-table-column label=\"车辆牌号\" align=\"center\" prop=\"车辆牌号\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"checkCar(scope.row.车辆牌号)\">{{ scope.row.车辆牌号 }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"车辆状态\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getCarStatusText(scope.row.车辆状态) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"找车团队\" align=\"center\" width=\"100\">\r\n        <template #default=\"{ row }\">\r\n          {{ row.找车团队 || '未派单' }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"地区\" align=\"center\" prop=\"地区\" width=\"100\" />\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"出单渠道\" width=\"120\" />\r\n      <el-table-column label=\"放款银行\" align=\"center\" prop=\"放款银行\" width=\"120\" />\r\n      <el-table-column label=\"托管类型\" align=\"center\" prop=\"托管类型\" width=\"100\" />\r\n      <el-table-column label=\"欠款余额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(scope.row.剩余金额) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"showDebtDetail(scope.row)\"\r\n              style=\"color: #409EFF; font-size: 12px;\"\r\n            >\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"未起诉金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(calculateUnsuedAmount(scope.row)) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"showUnsuedContentDetail(scope.row)\"\r\n              style=\"color: #409EFF; font-size: 12px;\"\r\n            >\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"起诉金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(scope.row.起诉金额) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"起诉类型\" align=\"center\" prop=\"起诉类型\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getLawsuitTypeText(scope.row.起诉类型) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"起诉内容\" align=\"center\" prop=\"起诉内容\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getLawsuitContentText(scope.row.起诉内容) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"判决金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(getLitigationFeeAmount(scope.row.序号, 'judgmentAmount')) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"利息\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(getLitigationFeeAmount(scope.row.序号, 'interest')) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"诉讼费\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(getLitigationFeeAmount(scope.row.序号, 'litigation')) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"viewLitigationFeeDetails(scope.row.序号, 'litigation')\"\r\n              style=\"font-size: 12px; padding: 0;\">\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"待追偿欠款\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div>{{ formatMoney(calculateTotalDebt(scope.row)) }}</div>\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"viewTotalDebtDetails(scope.row)\"\r\n              style=\"font-size: 12px; padding: 0;\">\r\n              详情\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"代偿证明发出日\" align=\"center\" prop=\"代偿证明发出日\" width=\"140\" />\r\n      <el-table-column label=\"法院地\" align=\"center\" prop=\"法院地\" width=\"100\" />\r\n      <el-table-column label=\"诉讼法院\" align=\"center\" prop=\"诉讼法院\" width=\"120\" />\r\n      <el-table-column label=\"案件负责人\" align=\"center\" prop=\"案件负责人\" width=\"100\" />\r\n      <el-table-column label=\"诉前调号出具时间\" align=\"center\" prop=\"诉前调号出具时间\" width=\"150\" />\r\n      <el-table-column label=\"诉前调号\" align=\"center\" prop=\"诉前调号\" width=\"120\" />\r\n      <el-table-column label=\"民初号出具时间\" align=\"center\" prop=\"民初号出具时间\" width=\"140\" />\r\n      <el-table-column label=\"民初号\" align=\"center\" prop=\"民初号\" width=\"120\" />\r\n      <el-table-column label=\"开庭时间\" align=\"center\" prop=\"开庭时间\" width=\"110\" />\r\n      <el-table-column label=\"申请执行时间\" align=\"center\" prop=\"申请执行时间\" width=\"130\" />\r\n      <el-table-column label=\"执行号/执保号\" align=\"center\" prop=\"执行号/执保号\" width=\"140\" />\r\n      <el-table-column label=\"车辆出库时间\" align=\"center\" prop=\"车辆出库时间\" width=\"130\" />\r\n      <el-table-column label=\"法拍时间\" align=\"center\" prop=\"法拍时间\" width=\"110\" />\r\n      <el-table-column label=\"车辆评估价\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(scope.row.车辆评估价) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"拍卖金额\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatMoney(scope.row.拍卖金额) }}\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"操作\" align=\"center\" fixed=\"right\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-popover placement=\"left\" trigger=\"click\" popper-class=\"custom-popover\">\r\n            <div class=\"operation-buttons\">\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openLitigationLogView(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                查看日志\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:remove']\"\r\n                @click=\"openLitigationForm(scope.row)\">\r\n                启动法诉\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openLitigationLogForm(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                提交日志\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openLitigationFeeForm(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                法诉费用\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openDispatchVehicleForm(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                派单找车\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"openDailyExpenseDialog(scope.row)\"\r\n                v-hasPermi=\"['vm_car_order:vm_car_order:edit']\">\r\n                日常费用\r\n              </el-button>\r\n            </div>\r\n            <el-button slot=\"reference\" size=\"mini\" type=\"text\">更多</el-button>\r\n          </el-popover>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n\r\n    <!-- 贷款人信息对话框 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n    <!-- 车辆信息组件 -->\r\n    <car-info ref=\"carInfo\" :visible.sync=\"carShow\" title=\"车辆信息\" :plateNo=\"plateNo\" permission=\"2\" />\r\n\r\n    <!-- 欠款详情弹窗 -->\r\n    <el-dialog title=\"欠款详情\" :visible.sync=\"debtDetailVisible\" width=\"800px\" append-to-body>\r\n      <div v-if=\"currentDebtRow\" style=\"padding: 10px;\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">银行代偿</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">银行代偿金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.银行代偿金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">银行催回金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.银行催回金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">银行剩余未还:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.银行剩余未还代偿金) }}</span>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">代扣金额</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">代扣金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.代扣金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">代扣催回金额:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.代扣催回金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">代扣剩余未还:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.代扣剩余未还代偿金) }}</span>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">违约金</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">违约金:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.违约金) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">催回违约金:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.催回违约金金额) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">剩余未还违约金:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.剩余未还违约金金额) }}</span>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <h4 style=\"margin-bottom: 15px; color: #303133; border-bottom: 1px solid #e4e7ed; padding-bottom: 10px;\">其他欠款</h4>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">其他欠款:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.其他欠款) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">催回其他欠款:</span>\r\n              <span class=\"debt-value\">{{ formatMoney(currentDebtRow.催回其他欠款) }}</span>\r\n            </div>\r\n            <div class=\"debt-item\">\r\n              <span class=\"debt-label\">剩余未还其他欠款:</span>\r\n              <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.剩余未还其他欠款) }}</span>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <div style=\"margin-top: 20px; padding-top: 15px; border-top: 2px solid #409EFF; background-color: #f5f7fa; padding: 15px; border-radius: 4px;\">\r\n          <h4 style=\"margin-bottom: 15px; color: #409EFF;\">汇总信息</h4>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <div class=\"debt-item total-debt\">\r\n                <span class=\"debt-label\">总欠款金额:</span>\r\n                <span class=\"debt-value\">{{ formatMoney(currentDebtRow.总欠款金额) }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"debt-item total-debt\">\r\n                <span class=\"debt-label\">已还金额:</span>\r\n                <span class=\"debt-value\">{{ formatMoney(currentDebtRow.已还金额) }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"debt-item total-debt\">\r\n                <span class=\"debt-label\">剩余金额:</span>\r\n                <span class=\"debt-value debt-remaining\">{{ formatMoney(currentDebtRow.剩余金额) }}</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listLitigation, getLitigationCostSummary, getLitigationByLoanId } from '@/api/litigation/litigation'\r\nimport { listPartner_info_simple } from '@/api/partner_info/partner_info'\r\nimport litigationStatus from '@/layout/components/Dialog/litigationStatus.vue'\r\nimport litigationForm from './modules/litigationForm'\r\nimport litigationFeeForm from './modules/litigationFeeForm'\r\nimport litigationLogForm from './modules/litigationLogForm'\r\nimport litigationLogView from './modules/litigationLogView'\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport carInfo from '@/layout/components/Dialog/carInfo.vue'\r\nimport dispatchVehicleForm from '@/layout/components/Dialog/dispatchVehicleForm.vue'\r\n\r\nexport default {\r\n  name: 'Litigation',\r\n  components: {\r\n    litigationStatus,\r\n    litigationForm,\r\n    litigationFeeForm,\r\n    litigationLogForm,\r\n    litigationLogView,\r\n    userInfo,\r\n    carInfo,\r\n    dispatchVehicleForm,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 法诉案件表格数据\r\n      litigation_caseList: [],\r\n      // 法诉费用汇总数据 - 按案件ID存储\r\n      litigationCostSummary: {},\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        贷款人: '',\r\n        身份证: '',\r\n        车辆牌号: '',\r\n        车辆状态: '',\r\n        出单渠道: '',\r\n        lendingBank: '',\r\n        放款银行: '',\r\n        litigationTime: '',\r\n        litigationStartDate: '',\r\n        litigationEndDate: '',\r\n        法诉文员: '',\r\n        跟催员: '',\r\n        案件负责人: '',\r\n        法诉子状态: '',\r\n        logType: '',\r\n        诉讼法院: '',\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {},\r\n      // 当前行数据\r\n      currentRow: {},\r\n      showMore: false,\r\n      // 贷款人信息\r\n      userInfoVisible: false,\r\n      customerInfo: { customerId: '', applyId: '' },\r\n      // 车辆信息\r\n      carShow: false,\r\n      plateNo: '',\r\n      // 欠款详情\r\n      debtDetailVisible: false,\r\n      currentDebtRow: null,\r\n      carStatusList: [\r\n        { label: '省内正常行驶', value: '1' },\r\n        { label: '省外正常行驶', value: '2' },\r\n        { label: '抵押', value: '3' },\r\n        { label: '疑似抵押', value: '4' },\r\n        { label: '疑似黑车', value: '5' },\r\n        { label: '已入库', value: '6' },\r\n        { label: '车在法院', value: '7' },\r\n        { label: '已法拍', value: '8' },\r\n        { label: '协商卖车', value: '9' },\r\n      ],\r\n      lendingBankList: [],\r\n      litigationStatusList: [\r\n        { label: '待立案', value: '1' },\r\n        { label: '已立案', value: '2' },\r\n        { label: '开庭', value: '3' },\r\n        { label: '判决', value: '4' },\r\n        { label: '结案', value: '5' },\r\n      ],\r\n      logTypeList: [\r\n        { label: '继续跟踪', value: 1 },\r\n        { label: '约定还款', value: 2 },\r\n        { label: '无法跟进', value: 3 },\r\n      ],\r\n      lawsuitCourtList: [\r\n        { label: '法院A', value: 'A' },\r\n        { label: '法院B', value: 'B' },\r\n        { label: '法院C', value: 'C' },\r\n      ],\r\n      lawsuitTypeList: [\r\n        { label: '债转', value: '1' },\r\n        { label: '债加', value: '2' },\r\n        { label: '担保物权', value: '3' },\r\n        { label: '仲裁', value: '4' },\r\n        { label: '赋强公证', value: '5' },\r\n        { label: '拍状元', value: '6' },\r\n        { label: '拍司令', value: '7' },\r\n        { label: '属地诉讼', value: '8' },\r\n        { label: '余值起诉', value: '9' },\r\n        { label: '债权出售', value: '10' },\r\n        { label: '签约地诉讼', value: '11' },\r\n        { label: '特殊诉讼通道', value: '12' },\r\n      ],\r\n      lawsuitContentList: [\r\n        { label: '银行代偿金额', value: '1' },\r\n        { label: '代扣金额', value: '2' },\r\n        { label: '违约金', value: '3' },\r\n        { label: '其他欠款', value: '4' },\r\n      ],\r\n      dispatchLoanId: '',\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getLendingBankList()\r\n  },\r\n  methods: {\r\n    /** 查询法诉案件列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listLitigation(this.queryParams).then(response => {\r\n        this.litigation_caseList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n        // 获取法诉费用汇总数据\r\n        this.loadLitigationCostSummary()\r\n      })\r\n    },\r\n\r\n    /** 加载法诉费用汇总数据 */\r\n    loadLitigationCostSummary() {\r\n      // 获取所有案件ID，确保转换为数字类型\r\n      const caseIds = this.litigation_caseList\r\n        .map(item => {\r\n          const id = item.序号\r\n          // 确保ID是数字类型\r\n          return typeof id === 'string' ? parseInt(id) : Number(id)\r\n        })\r\n        .filter(id => id && !isNaN(id))\r\n\r\n      if (caseIds.length === 0) return\r\n\r\n      console.log('发送的案件ID列表:', caseIds)\r\n\r\n      // 调用API获取费用汇总\r\n      getLitigationCostSummary(caseIds).then(response => {\r\n        if (response.code === 200) {\r\n          this.litigationCostSummary = response.data || {}\r\n          console.log('获取到的费用汇总数据:', this.litigationCostSummary)\r\n        } else {\r\n          console.error('获取法诉费用汇总失败:', response.msg)\r\n          this.litigationCostSummary = {}\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取法诉费用汇总失败:', error)\r\n        this.litigationCostSummary = {}\r\n      })\r\n    },\r\n\r\n    /** 获取放款银行列表 */\r\n    getLendingBankList() {\r\n      listPartner_info_simple().then(response => {\r\n        // 将资金方数据转换为下拉框需要的格式\r\n        this.lendingBankList = (response.data || []).map(item => ({\r\n          label: item.name || item.orgName,\r\n          value: item.id\r\n        }))\r\n      }).catch(error => {\r\n        console.error('获取放款银行列表失败:', error)\r\n        this.lendingBankList = []\r\n      })\r\n    },\r\n\r\n    /** 获取法诉费用金额 */\r\n    getLitigationFeeAmount(caseId, feeType) {\r\n      // 确保caseId是正确的类型\r\n      const normalizedCaseId = typeof caseId === 'string' ? parseInt(caseId) : Number(caseId)\r\n      const summary = this.litigationCostSummary[normalizedCaseId]\r\n\r\n      if (!summary) {\r\n        return 0\r\n      }\r\n\r\n      switch (feeType) {\r\n        case 'judgmentAmount':\r\n          return Number(summary.judgmentAmount || 0)\r\n        case 'interest':\r\n          return Number(summary.interest || 0)\r\n        case 'litigation':\r\n          // 诉讼费包含多种费用类型的总和\r\n          return Number(summary.lawyerFee || 0) +\r\n                 Number(summary.litigationFee || 0) +\r\n                 Number(summary.preservationFee || 0) +\r\n                 Number(summary.surveillanceFee || 0) +\r\n                 Number(summary.announcementFee || 0) +\r\n                 Number(summary.appraisalFee || 0) +\r\n                 Number(summary.executionFee || 0) +\r\n                 Number(summary.penalty || 0) +\r\n                 Number(summary.guaranteeFee || 0) +\r\n                 Number(summary.intermediaryFee || 0) +\r\n                 Number(summary.compensity || 0) +\r\n                 Number(summary.otherAmountsOwed || 0) +\r\n                 Number(summary.insurance || 0)\r\n        default:\r\n          return 0\r\n      }\r\n    },\r\n\r\n    /** 查看诉讼费详情 */\r\n    viewLitigationFeeDetails(caseId, feeType) {\r\n      // 确保caseId是正确的类型\r\n      const normalizedCaseId = typeof caseId === 'string' ? parseInt(caseId) : Number(caseId)\r\n\r\n      // 只处理诉讼费详情\r\n      if (feeType === 'litigation') {\r\n        const title = '诉讼费详情'\r\n        const content = this.formatLitigationFeeDetail(normalizedCaseId, [\r\n          'lawyerFee', 'litigationFee', 'preservationFee', 'surveillanceFee',\r\n          'announcementFee', 'appraisalFee', 'executionFee', 'penalty',\r\n          'guaranteeFee', 'intermediaryFee', 'compensity', 'otherAmountsOwed', 'insurance'\r\n        ])\r\n\r\n        this.$alert(content, title, {\r\n          dangerouslyUseHTMLString: true,\r\n          confirmButtonText: '确定'\r\n        })\r\n      }\r\n    },\r\n\r\n    /** 格式化法诉费用详情 */\r\n    formatLitigationFeeDetail(caseId, feeTypes) {\r\n      const summary = this.litigationCostSummary[caseId]\r\n      if (!summary) return '<p>暂无费用数据</p>'\r\n\r\n      const feeLabels = {\r\n        judgmentAmount: '判决金额',\r\n        interest: '利息',\r\n        lawyerFee: '律师费',\r\n        litigationFee: '法诉费',\r\n        preservationFee: '保全费',\r\n        surveillanceFee: '布控费',\r\n        announcementFee: '公告费',\r\n        appraisalFee: '评估费',\r\n        executionFee: '执行费',\r\n        penalty: '违约金',\r\n        guaranteeFee: '担保费',\r\n        intermediaryFee: '居间费',\r\n        compensity: '代偿金',\r\n        otherAmountsOwed: '其他欠款',\r\n        insurance: '保险费'\r\n      }\r\n\r\n      let html = '<div style=\"text-align: left;\">'\r\n      let total = 0\r\n\r\n      feeTypes.forEach(feeType => {\r\n        const amount = Number(summary[feeType] || 0)\r\n        if (amount > 0) {\r\n          html += `<p>${feeLabels[feeType]}: ￥${this.formatMoney(amount)}</p>`\r\n          total += amount\r\n        }\r\n      })\r\n\r\n      if (feeTypes.length > 1 && total > 0) {\r\n        html += `<hr><p><strong>合计: ￥${this.formatMoney(total)}</strong></p>`\r\n      }\r\n\r\n      html += '</div>'\r\n      return html || '<p>暂无费用数据</p>'\r\n    },\r\n    handleQuery() {\r\n      // 同步放款银行筛选条件到后端期望的字段名\r\n      if (this.queryParams.lendingBank) {\r\n        // 根据选中的银行ID查找对应的银行名称\r\n        const selectedBank = this.lendingBankList.find(bank => bank.value === this.queryParams.lendingBank)\r\n        this.queryParams.放款银行 = selectedBank ? selectedBank.label : ''\r\n      } else {\r\n        this.queryParams.放款银行 = ''\r\n      }\r\n\r\n      // 处理法诉时间区间筛选\r\n      if (this.queryParams.litigationTime && this.queryParams.litigationTime.length === 2) {\r\n        this.queryParams.litigationStartDate = this.queryParams.litigationTime[0]\r\n        this.queryParams.litigationEndDate = this.queryParams.litigationTime[1]\r\n      } else {\r\n        this.queryParams.litigationStartDate = ''\r\n        this.queryParams.litigationEndDate = ''\r\n      }\r\n\r\n      // 处理日志类型筛选 - 将前端的logType传递给后端\r\n      // logType字段会直接传递给后端，不需要额外处理\r\n\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    resetQuery() {\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        贷款人: '',\r\n        身份证: '',\r\n        车辆牌号: '',\r\n        车辆状态: '',\r\n        出单渠道: '',\r\n        lendingBank: '',\r\n        放款银行: '',\r\n        litigationTime: '',\r\n        litigationStartDate: '',\r\n        litigationEndDate: '',\r\n        法诉文员: '',\r\n        跟催员: '',\r\n        案件负责人: '',\r\n        法诉子状态: '',\r\n        logType: '',\r\n        诉讼法院: '',\r\n      }\r\n      this.getList()\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    // 启动法诉弹窗\r\n    openLitigationForm(row) {\r\n      this.currentRow = row || {}\r\n      this.$refs.litigationForm.open && this.$refs.litigationForm.open()\r\n    },\r\n    onLitigationFormConfirm() {\r\n      // 处理确认逻辑\r\n    },\r\n    // 法诉费用弹窗\r\n    openLitigationFeeForm(row) {\r\n      this.currentRow = row || {}\r\n      this.$refs.litigationFeeForm.open && this.$refs.litigationFeeForm.open()\r\n    },\r\n    onLitigationFeeFormConfirm() {\r\n      // 处理确认逻辑\r\n    },\r\n    // 提交日志弹窗\r\n    openLitigationLogForm(row) {\r\n      this.currentRow = JSON.parse(JSON.stringify(row))\r\n      this.$refs.litigationLogForm.openDialog && this.$refs.litigationLogForm.openDialog()\r\n    },\r\n    onLitigationLogFormConfirm() {\r\n      // 处理确认逻辑\r\n    },\r\n    // 日志查看弹窗\r\n    openLitigationLogView(row) {\r\n      this.currentRow = row || {}\r\n      this.$refs.litigationLogView.openDialog && this.$refs.litigationLogView.openDialog()\r\n    },\r\n    // 找车按钮弹窗\r\n    openDispatchVehicleForm(row) {\r\n      this.dispatchLoanId = row.流程序号\r\n      this.$refs.dispatchVehicleForm.openDialog()\r\n    },\r\n    /** 计算待追偿欠款总额 */\r\n    calculateTotalDebt(row) {\r\n      const caseId = row.序号\r\n\r\n      // 获取各项费用\r\n      const judgmentAmount = this.getLitigationFeeAmount(caseId, 'judgmentAmount') // 判决金额\r\n      const interest = this.getLitigationFeeAmount(caseId, 'interest') // 利息\r\n      const litigationCosts = this.getLitigationFeeAmount(caseId, 'litigation') // 法诉费用\r\n      const unsuedAmount = this.calculateUnsuedAmount(row) // 未起诉金额\r\n\r\n      // 计算总额：判决金额 + 利息 + 法诉费用 + 未起诉金额\r\n      const total = Number(judgmentAmount) + Number(interest) + Number(litigationCosts) + Number(unsuedAmount)\r\n\r\n      return total\r\n    },\r\n\r\n    /** 查看待追偿欠款详情 */\r\n    viewTotalDebtDetails(row) {\r\n      const caseId = row.序号\r\n\r\n      // 获取各项费用\r\n      const judgmentAmount = this.getLitigationFeeAmount(caseId, 'judgmentAmount')\r\n      const interest = this.getLitigationFeeAmount(caseId, 'interest')\r\n      const litigationCosts = this.getLitigationFeeAmount(caseId, 'litigation')\r\n      const unsuedAmount = this.calculateUnsuedAmount(row)\r\n      const total = this.calculateTotalDebt(row)\r\n\r\n      let html = '<div style=\"text-align: left;\">'\r\n      html += `<p>判决金额: ￥${this.formatMoney(judgmentAmount)}</p>`\r\n      html += `<p>利息: ￥${this.formatMoney(interest)}</p>`\r\n      html += `<p>法诉费用: ￥${this.formatMoney(litigationCosts)}</p>`\r\n      html += `<p>未起诉金额: ￥${this.formatMoney(unsuedAmount)}</p>`\r\n      html += `<hr><p><strong>待追偿欠款总计: ￥${this.formatMoney(total)}</strong></p>`\r\n      html += '</div>'\r\n\r\n      this.$alert(html, '待追偿欠款详情', {\r\n        dangerouslyUseHTMLString: true,\r\n        confirmButtonText: '确定'\r\n      })\r\n    },\r\n\r\n    // 打开日常费用申请弹窗\r\n    openDailyExpenseDialog(row) {\r\n      // 通过ref调用法诉费用表单组件的方法，传入案件ID\r\n      this.$refs.litigationFeeForm.openDailyExpenseDialog(row.序号)\r\n    },\r\n    // 查看贷款人信息\r\n    openUserInfo(customerInfo) {\r\n      this.customerInfo = customerInfo\r\n      this.userInfoVisible = true\r\n    },\r\n    // 查看车辆信息\r\n    checkCar(plateNo) {\r\n      this.plateNo = plateNo\r\n      this.carShow = true\r\n    },\r\n    // 获取起诉类型文字\r\n    getLawsuitTypeText(value) {\r\n      const item = this.lawsuitTypeList.find(item => item.value === value)\r\n      return item ? item.label : value\r\n    },\r\n    // 获取起诉内容文字\r\n    getLawsuitContentText(value) {\r\n      if (!value) return '-'\r\n\r\n      try {\r\n        // 尝试解析JSON数组（多选格式）\r\n        const contentArray = JSON.parse(value)\r\n        if (Array.isArray(contentArray)) {\r\n          return contentArray.map(val => {\r\n            const item = this.lawsuitContentList.find(item => item.value === val)\r\n            return item ? item.label : val\r\n          }).join('、')\r\n        }\r\n      } catch {\r\n        // 如果不是JSON格式，按单选处理\r\n        const item = this.lawsuitContentList.find(item => item.value === value)\r\n        return item ? item.label : value\r\n      }\r\n\r\n      // 默认返回原值\r\n      return value\r\n    },\r\n    // 获取催记类型文本\r\n    getUrgeStatusText(value) {\r\n      const urgeStatusMap = {\r\n        1: '继续跟踪',\r\n        2: '约定还款',\r\n        3: '无法跟进'\r\n      }\r\n      return urgeStatusMap[value] || value\r\n    },\r\n    // 获取车辆状态文本\r\n    getCarStatusText(value) {\r\n      // 使用已有的 carStatusList 数据\r\n      const statusItem = this.carStatusList.find(item => item.value == value)\r\n      return statusItem ? statusItem.label : value\r\n    },\r\n    // 显示欠款详情弹窗\r\n    showDebtDetail(row) {\r\n      this.currentDebtRow = row\r\n      this.debtDetailVisible = true\r\n    },\r\n    // 格式化金额显示\r\n    formatMoney(amount) {\r\n      if (amount === null || amount === undefined || amount === '') {\r\n        return '0.00'\r\n      }\r\n      return Number(amount).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      })\r\n    },\r\n    // 计算未起诉金额\r\n    calculateUnsuedAmount(row) {\r\n      const remainingAmount = Number(row.剩余金额) || 0\r\n      const suedAmount = Number(row.起诉金额) || 0\r\n      const unsuedAmount = remainingAmount - suedAmount\r\n      return unsuedAmount > 0 ? unsuedAmount : 0\r\n    },\r\n\r\n    // 显示未起诉内容详情\r\n    async showUnsuedContentDetail(row) {\r\n      try {\r\n        // 获取该贷款的所有法诉案件\r\n        const response = await getLitigationByLoanId(row.流程序号)\r\n\r\n        if (response.code === 200 && response.data) {\r\n          // 获取所有已启动的法诉案件的起诉内容\r\n          const usedProsecutionContents = []\r\n\r\n          response.data.forEach(caseItem => {\r\n            // 判断法诉是否已启动\r\n            if (this.isLitigationStarted(caseItem)) {\r\n              const content = caseItem.prosecutionContent\r\n              if (content) {\r\n                try {\r\n                  // 尝试解析为数组（多选）\r\n                  const contentArray = JSON.parse(content)\r\n                  if (Array.isArray(contentArray)) {\r\n                    usedProsecutionContents.push(...contentArray)\r\n                  } else {\r\n                    usedProsecutionContents.push(content)\r\n                  }\r\n                } catch {\r\n                  // 如果不是JSON格式，按单选处理\r\n                  usedProsecutionContents.push(content)\r\n                }\r\n              }\r\n            }\r\n          })\r\n\r\n          // 去重已使用的起诉内容\r\n          const uniqueUsedContents = [...new Set(usedProsecutionContents)]\r\n\r\n          // 找出未使用的起诉内容\r\n          const allProsecutionContents = this.lawsuitContentList\r\n          const unusedContents = allProsecutionContents.filter(item =>\r\n            !uniqueUsedContents.includes(item.value)\r\n          )\r\n\r\n          // 计算未起诉内容对应的金额\r\n          let html = '<div style=\"text-align: left;\">'\r\n          let totalUnsuedAmount = 0\r\n\r\n          if (unusedContents.length > 0) {\r\n            html += '<h4 style=\"margin-bottom: 15px; color: #303133;\">未起诉内容：</h4>'\r\n\r\n            unusedContents.forEach(content => {\r\n              let amount = 0\r\n              // 根据起诉内容类型计算对应金额\r\n              switch (content.value) {\r\n                case '1': // 银行代偿金额\r\n                  amount = Number(row.银行剩余未还代偿金) || 0\r\n                  break\r\n                case '2': // 代扣金额\r\n                  amount = Number(row.代扣剩余未还代偿金) || 0\r\n                  break\r\n                case '3': // 违约金\r\n                  amount = Number(row.剩余未还违约金金额) || 0\r\n                  break\r\n                case '4': // 其他欠款\r\n                  amount = Number(row.剩余未还其他欠款) || 0\r\n                  break\r\n              }\r\n\r\n              // 显示所有未起诉内容，不管金额是否为0\r\n              html += `<p>${content.label}: ￥${this.formatMoney(amount)}</p>`\r\n              totalUnsuedAmount += amount\r\n            })\r\n\r\n            html += `<hr><p><strong>未起诉金额合计: ￥${this.formatMoney(totalUnsuedAmount)}</strong></p>`\r\n          } else {\r\n            html += '<p>所有起诉内容都已被选择</p>'\r\n          }\r\n\r\n          html += '</div>'\r\n\r\n          this.$alert(html, '未起诉内容详情', {\r\n            dangerouslyUseHTMLString: true,\r\n            confirmButtonText: '确定'\r\n          })\r\n        } else {\r\n          this.$message.error('获取法诉案件数据失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('获取未起诉内容详情失败')\r\n      }\r\n    },\r\n\r\n    // 判断法诉是否已启动\r\n    isLitigationStarted(caseData) {\r\n      // 检查关键字段是否都有值\r\n      const hasLitigationType = caseData.litigationType != null && caseData.litigationType !== ''\r\n      const hasProsecutionType = caseData.prosecutionType != null && caseData.prosecutionType !== ''\r\n      const hasProsecutionContent = caseData.prosecutionContent != null && caseData.prosecutionContent !== ''\r\n      const hasLitigationStartDay = caseData.litigationStartDay != null && caseData.litigationStartDay !== ''\r\n\r\n      return hasLitigationType && hasProsecutionType && hasProsecutionContent && hasLitigationStartDay\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 操作按钮容器 */\r\n.operation-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8px;\r\n  width: 90px;\r\n  padding: 4px 8px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.operation-btn {\r\n  width: 74px !important;\r\n  height: 26px !important;\r\n  margin: 0 !important;\r\n  padding: 0 6px !important;\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  text-align: center;\r\n  line-height: 26px;\r\n}\r\n\r\n/* 按钮悬停效果 */\r\n.operation-btn:hover {\r\n  background-color: #f5f7fa;\r\n  color: #409eff;\r\n}\r\n\r\n/* 欠款详情样式 */\r\n.debt-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  padding: 5px 0;\r\n}\r\n\r\n.debt-label {\r\n  font-size: 13px;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n.debt-value {\r\n  font-size: 13px;\r\n  color: #303133;\r\n  font-weight: 600;\r\n}\r\n\r\n.debt-remaining {\r\n  color: #F56C6C !important;\r\n}\r\n\r\n.total-debt {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n}\r\n\r\n.total-debt .debt-label {\r\n  color: #303133;\r\n  font-weight: 600;\r\n}\r\n\r\n.total-debt .debt-value {\r\n  font-size: 15px;\r\n  color: #409EFF;\r\n}\r\n</style>\r\n<style>\r\n.custom-popover {\r\n  width: 116px !important;\r\n  min-width: 116px !important;\r\n  max-width: 116px !important;\r\n  box-sizing: border-box !important;\r\n}\r\n</style>\r\n"]}]}