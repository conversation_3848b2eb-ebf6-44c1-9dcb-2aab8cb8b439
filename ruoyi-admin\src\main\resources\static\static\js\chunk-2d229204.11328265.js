(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d229204"],{dbb2:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"用户id",prop:"memberId"}},[r("el-input",{attrs:{placeholder:"请输入用户id",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.memberId,callback:function(t){e.$set(e.queryParams,"memberId",t)},expression:"queryParams.memberId"}})],1),r("el-form-item",{attrs:{label:"报销金额",prop:"money"}},[r("el-input",{attrs:{placeholder:"请输入报销金额",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.money,callback:function(t){e.$set(e.queryParams,"money",t)},expression:"queryParams.money"}})],1),r("el-form-item",{attrs:{label:"报销凭据",prop:"img"}},[r("el-input",{attrs:{placeholder:"请输入报销凭据",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.img,callback:function(t){e.$set(e.queryParams,"img",t)},expression:"queryParams.img"}})],1),r("el-form-item",{attrs:{label:"审核状态",prop:"status"}},[r("el-select",{attrs:{placeholder:"请选择审核状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_reim_status,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"开始地址",prop:"startAddress"}},[r("el-input",{attrs:{placeholder:"请输入开始地址",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.startAddress,callback:function(t){e.$set(e.queryParams,"startAddress",t)},expression:"queryParams.startAddress"}})],1),r("el-form-item",{attrs:{label:"结束地址",prop:"endAddress"}},[r("el-input",{attrs:{placeholder:"请输入结束地址",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.endAddress,callback:function(t){e.$set(e.queryParams,"endAddress",t)},expression:"queryParams.endAddress"}})],1),r("el-form-item",{attrs:{label:"创建时间",prop:"createDate"}},[r("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择创建时间"},model:{value:e.queryParams.createDate,callback:function(t){e.$set(e.queryParams,"createDate",t)},expression:"queryParams.createDate"}})],1),r("el-form-item",{attrs:{label:"更新时间",prop:"updateDate"}},[r("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择更新时间"},model:{value:e.queryParams.updateDate,callback:function(t){e.$set(e.queryParams,"updateDate",t)},expression:"queryParams.updateDate"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["reimburse:reimburse:add"],expression:"['reimburse:reimburse:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["reimburse:reimburse:edit"],expression:"['reimburse:reimburse:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["reimburse:reimburse:remove"],expression:"['reimburse:reimburse:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["reimburse:reimburse:export"],expression:"['reimburse:reimburse:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.reimburseList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:"${comment}",align:"center",prop:"id"}}),r("el-table-column",{attrs:{label:"用户id",align:"center",prop:"memberId"}}),r("el-table-column",{attrs:{label:"报销内容",align:"center",prop:"content"}}),r("el-table-column",{attrs:{label:"报销金额",align:"center",prop:"money"}}),r("el-table-column",{attrs:{label:"报销凭据",align:"center",prop:"img"}}),r("el-table-column",{attrs:{label:"审核状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.sys_reim_status,value:t.row.status}})]}}])}),r("el-table-column",{attrs:{label:"1-油费，2-路费，3-其他",align:"center",prop:"state"}}),r("el-table-column",{attrs:{label:"1-有钥匙，2-无钥匙",align:"center",prop:"keyStatus"}}),r("el-table-column",{attrs:{label:"拒绝原因",align:"center",prop:"remark"}}),r("el-table-column",{attrs:{label:"开始地址",align:"center",prop:"startAddress"}}),r("el-table-column",{attrs:{label:"结束地址",align:"center",prop:"endAddress"}}),r("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createDate",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.createDate,"{y}-{m}-{d}")))])]}}])}),r("el-table-column",{attrs:{label:"更新时间",align:"center",prop:"updateDate",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.updateDate,"{y}-{m}-{d}")))])]}}])}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["reimburse:reimburse:edit"],expression:"['reimburse:reimburse:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["reimburse:reimburse:remove"],expression:"['reimburse:reimburse:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"用户id",prop:"memberId"}},[r("el-input",{attrs:{placeholder:"请输入用户id"},model:{value:e.form.memberId,callback:function(t){e.$set(e.form,"memberId",t)},expression:"form.memberId"}})],1),r("el-form-item",{attrs:{label:"报销内容"}},[r("editor",{attrs:{"min-height":192},model:{value:e.form.content,callback:function(t){e.$set(e.form,"content",t)},expression:"form.content"}})],1),r("el-form-item",{attrs:{label:"报销金额",prop:"money"}},[r("el-input",{attrs:{placeholder:"请输入报销金额"},model:{value:e.form.money,callback:function(t){e.$set(e.form,"money",t)},expression:"form.money"}})],1),r("el-form-item",{attrs:{label:"报销凭据",prop:"img"}},[r("el-input",{attrs:{placeholder:"请输入报销凭据"},model:{value:e.form.img,callback:function(t){e.$set(e.form,"img",t)},expression:"form.img"}})],1),r("el-form-item",{attrs:{label:"审核状态",prop:"status"}},[r("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_reim_status,(function(t){return r("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1),r("el-form-item",{attrs:{label:"拒绝原因",prop:"remark"}},[r("el-input",{attrs:{placeholder:"请输入拒绝原因"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),r("el-form-item",{attrs:{label:"开始地址",prop:"startAddress"}},[r("el-input",{attrs:{placeholder:"请输入开始地址"},model:{value:e.form.startAddress,callback:function(t){e.$set(e.form,"startAddress",t)},expression:"form.startAddress"}})],1),r("el-form-item",{attrs:{label:"结束地址",prop:"endAddress"}},[r("el-input",{attrs:{placeholder:"请输入结束地址"},model:{value:e.form.endAddress,callback:function(t){e.$set(e.form,"endAddress",t)},expression:"form.endAddress"}})],1),r("el-form-item",{attrs:{label:"创建时间",prop:"createDate"}},[r("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择创建时间"},model:{value:e.form.createDate,callback:function(t){e.$set(e.form,"createDate",t)},expression:"form.createDate"}})],1),r("el-form-item",{attrs:{label:"更新时间",prop:"updateDate"}},[r("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择更新时间"},model:{value:e.form.updateDate,callback:function(t){e.$set(e.form,"updateDate",t)},expression:"form.updateDate"}})],1),r("el-form-item",{attrs:{label:"是否删除(0-否，2是)",prop:"delFlag"}},[r("el-input",{attrs:{placeholder:"请输入是否删除(0-否，2是)"},model:{value:e.form.delFlag,callback:function(t){e.$set(e.form,"delFlag",t)},expression:"form.delFlag"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],s=r("5530"),n=(r("d81d"),r("d3b7"),r("0643"),r("a573"),r("b775"));function i(e){return Object(n["a"])({url:"/reimburse/reimburse/list",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/reimburse/reimburse/"+e,method:"get"})}function u(e){return Object(n["a"])({url:"/reimburse/reimburse",method:"post",data:e})}function m(e){return Object(n["a"])({url:"/reimburse/reimburse",method:"put",data:e})}function c(e){return Object(n["a"])({url:"/reimburse/reimburse/"+e,method:"delete"})}var d={name:"Reimburse",dicts:["sys_reim_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,reimburseList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,memberId:null,content:null,money:null,img:null,status:null,state:null,keyStatus:null,startAddress:null,endAddress:null,createDate:null,updateDate:null},form:{},rules:{}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,i(this.queryParams).then((function(t){e.reimburseList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,memberId:null,content:null,money:null,img:null,status:null,state:null,keyStatus:null,remark:null,startAddress:null,endAddress:null,createBy:null,createDate:null,updateBy:null,updateDate:null,delFlag:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加报销记录"},handleUpdate:function(e){var t=this;this.reset();var r=e.id||this.ids;o(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改报销记录"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?m(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.id||this.ids;this.$modal.confirm('是否确认删除报销记录编号为"'+r+'"的数据项？').then((function(){return c(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("reimburse/reimburse/export",Object(s["a"])({},this.queryParams),"reimburse_".concat((new Date).getTime(),".xlsx"))}}},p=d,b=r("2877"),f=Object(b["a"])(p,a,l,!1,null,null,null);t["default"]=f.exports}}]);