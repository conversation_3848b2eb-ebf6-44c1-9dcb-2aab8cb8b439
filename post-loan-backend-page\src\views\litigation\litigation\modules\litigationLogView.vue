<template>
  <div>
    <el-dialog :visible.sync="visible" title="日志查看" width="800px" @close="handleCancel">
      <!-- 进度条或撤诉状态 -->
      <div v-if="isWithdrawn" style="text-align: center; margin-bottom: 24px; padding: 20px; background-color: #f5f5f5; border-radius: 4px;">
        <span style="font-size: 16px; color: #909399; font-weight: bold;">已撤案</span>
      </div>
      <el-steps v-else :active="activeStep" finish-status="success" process-status="process" align-center style="margin-bottom: 24px" class="custom-steps">
        <el-step v-for="(item, idx) in statusSteps" :key="idx" :title="item" />
      </el-steps>

      <!-- 日志表格 -->
      <el-table :data="logList" border style="width: 100%; margin-bottom: 24px">
        <el-table-column prop="createTime" label="时间" width="160" />
        <el-table-column prop="createBy" label="跟踪人" width="120" />
        <el-table-column prop="status" label="跟踪动作" width="120" />
        <el-table-column prop="urgeDescribe" label="描述" />
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleUrgeLog">催记日志</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </span>
    </el-dialog>

    <!-- 催记日志组件 -->
    <loan-reminder-log ref="loanReminderLog" :loan-id="reminderLogLoanId" />
  </div>
</template>

<script>
import { listLitigation_log } from '@/api/litigation/litigation'
import LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'

export default {
  name: 'LitigationLogView',
  components: {
    LoanReminderLog,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    data: {
      handler(newVal) {
        console.log('newVal:', newVal)
        listLitigation_log({ litigationId: newVal.序号 }).then(res => {
          this.logList = res.rows
          // 获取最后一个日志的状态，并设置对应的进度条步骤
          if (res.rows && res.rows.length > 0) {
            const lastLogStatus = res.rows[res.rows.length - 1].status
            this.currentStatus = lastLogStatus
            this.setActiveStepByStatus(lastLogStatus)
          } else {
            // 没有日志数据时，清空当前状态
            this.currentStatus = ''
          }
        })
      },
    },
  },
  data() {
    return {
      visible: false,
      // 诉讼状态树结构
      reminderLogLoanId: '',
      litigationStatusTree: [
        // 独立状态（不需要分类）
        { label: '暂不起诉', value: '暂不起诉' },
        { label: '撤案', value: '撤案' },
        {
          label: '立案前',
          value: '立案前',
          children: [
            { label: '准备资料', value: '准备资料' },
            { label: '已邮寄', value: '已邮寄' },
            { label: '待立案', value: '待立案' },
          ],
        },
        {
          label: '立案-判决',
          value: '立案-判决',
          children: [
            { label: '获取案件号', value: '获取案件号' },
            { label: '待出民初号', value: '待出民初号' },
            { label: '待开庭', value: '待开庭' },
            { label: '待出法院文书', value: '待出法院文书' },
          ],
        },
        {
          label: '判决-执行',
          value: '判决-执行',
          children: [
            { label: '待执行', value: '待执行' },
            { label: '待出申请书', value: '待出申请书' },
            { label: '已提交执行书', value: '已提交执行书' },
          ],
        },
        {
          label: '执行后',
          value: '执行后',
          children: [
            { label: '执行中', value: '执行中' },
            { label: '执行终本', value: '执行终本' },
            { label: '继续执行', value: '继续执行' },
            { label: '待送车', value: '待送车' },
            { label: '待法拍', value: '待法拍' },
          ],
        },
        {
          label: '结案',
          value: '结案',
          children: [
            { label: '法诉减免结清', value: '法诉减免结清' },
            { label: '法诉全额结清', value: '法诉全额结清' },
          ],
        },
      ],
      // 当前激活的步骤索引
      activeStep: 0,
      logList: [],
      // 当前状态
      currentStatus: '',
    }
  },
  computed: {
    // 从状态树中提取父节点的label作为进度条步骤（排除独立状态）
    statusSteps() {
      return this.litigationStatusTree
        .filter(item => item.children && item.children.length > 0)
        .map(item => item.label)
    },
    // 判断是否为撤案状态（只有在有日志数据且状态为撤案时才显示）
    isWithdrawn() {
      return this.logList && this.logList.length > 0 && this.currentStatus === '撤案'
    },
  },
  methods: {
    // 根据状态设置激活的步骤
    setActiveStepByStatus(status) {
      if (!status) {
        this.activeStep = 0
        return
      }

      // 如果是撤案状态，不设置进度条
      if (status === '撤案') {
        return
      }

      // 查找状态对应的父节点索引
      const parentIndex = this.findParentIndexByStatus(status)
      this.activeStep = parentIndex >= 0 ? parentIndex : 0
    },

    // 根据状态找到父节点的索引
    findParentIndexByStatus(status) {
      for (let i = 0; i < this.litigationStatusTree.length; i++) {
        const item = this.litigationStatusTree[i]

        // 如果是父节点本身
        if (item.label === status || item.value === status) {
          return i
        }

        // 如果有子节点，在子节点中查找
        if (item.children && item.children.length > 0) {
          const childFound = item.children.some(child => child.label === status || child.value === status)
          if (childFound) {
            return i
          }
        }
      }
      return -1
    },

    openDialog() {
      this.visible = true
    },
    handleUrgeLog() {
      // 打开催记日志对话框，传入当前的 data
      this.reminderLogLoanId = String(this.data.流程序号)
      this.$refs.loanReminderLog.openLogDialog()
    },
    handleConfirm() {
      return
    },
    handleCancel() {
      this.visible = false
    },
  },
}
</script>

<style scoped>
/* 自定义步骤条样式 - 激活节点为蓝色 */
::v-deep .custom-steps .el-step__head.is-process {
  color: #409eff;
  border-color: #409eff;
}

::v-deep .custom-steps .el-step__head.is-process .el-step__icon {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
}

::v-deep .custom-steps .el-step__title.is-process {
  color: #409eff;
  font-weight: bold;
}
</style>
