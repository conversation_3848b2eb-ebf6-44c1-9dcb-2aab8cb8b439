(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4939a30c"],{"0f5f":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.carInfo.plateNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车架号"}},[e._v(e._s(e.carInfo.identityNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"发动机号"}},[e._v(e._s(e.carInfo.engineNumber||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(e._s(e.carStatusLabel))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆位置"}},[e._v(e._s(e.carInfo.carAddress||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(e._s(e.gpsStatusLabel))]):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看车牌照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"行驶证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看行驶证照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"登记证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看登记证照片")])],1):e._e()],1)],1)},n=[],r=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),a("bd52")),o={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var e=this,t=this.carStatusList.find((function(t){return t.value===e.carInfo.carStatus}));return t?t.label:"未知状态"},gpsStatusLabel:function(){var e=this,t=this.gpsStatusList.find((function(t){return t.value===e.carInfo.gpsStatus}));return t?t.label:"未知状态"}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,e&&this.plateNo&&Object(r["e"])(this.plateNo).then((function(e){t.carInfo=e.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},s=o,i=a("2877"),u=Object(i["a"])(s,l,n,!1,null,null,null);t["a"]=u.exports},1791:function(e,t,a){},"2eca":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[e._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},n=[],r=a("bd52"),o={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(r["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},s=o,i=(a("d6fd"),a("2877")),u=Object(i["a"])(s,l,n,!1,null,"8a3d4978",null);t["a"]=u.exports},"9f56":function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"d",(function(){return r})),a.d(t,"a",(function(){return o})),a.d(t,"h",(function(){return s})),a.d(t,"c",(function(){return i})),a.d(t,"b",(function(){return u})),a.d(t,"f",(function(){return c})),a.d(t,"g",(function(){return p}));var l=a("b775");function n(e){return Object(l["a"])({url:"/sys_office/sys_office/orgList",method:"get",params:e})}function r(e){return Object(l["a"])({url:"/sys_office/sys_office/"+e,method:"get"})}function o(e){return Object(l["a"])({url:"/sys_office/sys_office",method:"post",data:e})}function s(e){return Object(l["a"])({url:"/sys_office/sys_office",method:"put",data:e})}function i(e){return Object(l["a"])({url:"/sys_office/sys_office/"+e,method:"delete"})}function u(e){return Object(l["a"])({url:"/vw_user/vw_user/list?roleId="+e,method:"get"})}function c(e){return Object(l["a"])({url:"/sys_office/sys_office/dh_assign",method:"put",data:e})}function p(e){return Object(l["a"])({url:"/sys_office/sys_office/fs_assign",method:"put",data:e})}},a4ce:function(e,t,a){"use strict";a.d(t,"d",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return o})),a.d(t,"f",(function(){return s})),a.d(t,"b",(function(){return i})),a.d(t,"e",(function(){return u}));var l=a("b775");function n(e){return Object(l["a"])({url:"/partner_info/partner_info/list",method:"get",params:e})}function r(e){return Object(l["a"])({url:"/partner_info/partner_info/"+e,method:"get"})}function o(e){return Object(l["a"])({url:"/partner_info/partner_info",method:"post",data:e})}function s(e){return Object(l["a"])({url:"/partner_info/partner_info",method:"put",data:e})}function i(e){return Object(l["a"])({url:"/partner_info/partner_info/"+e,method:"delete"})}function u(e){return Object(l["a"])({url:"/partner_info/partner_info/id-name-list",method:"get",params:e})}},c0ae:function(e,t,a){},d6fd:function(e,t,a){"use strict";a("1791")},db7d:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"certId"}},[a("el-input",{attrs:{placeholder:"贷款人身份证号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.certId,callback:function(t){e.$set(e.queryParams,"certId",t)},expression:"queryParams.certId"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"nickName"}},[a("el-input",{attrs:{placeholder:"业务员姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.nickName,callback:function(t){e.$set(e.queryParams,"nickName",t)},expression:"queryParams.nickName"}})],1),a("el-form-item",{attrs:{label:"",prop:"jgName"}},[a("el-input",{attrs:{placeholder:"录单渠道名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jgName,callback:function(t){e.$set(e.queryParams,"jgName",t)},expression:"queryParams.jgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"orgName"}},[a("el-select",{attrs:{placeholder:"放款银行",clearable:""},model:{value:e.queryParams.orgName,callback:function(t){e.$set(e.queryParams,"orgName",t)},expression:"queryParams.orgName"}},e._l(e.bankList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.orgName,value:e.orgName}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"petitionAssignmentType"}},[a("el-select",{attrs:{placeholder:"指派状态",clearable:""},model:{value:e.queryParams.petitionAssignmentType,callback:function(t){e.$set(e.queryParams,"petitionAssignmentType",t)},expression:"queryParams.petitionAssignmentType"}},e._l(e.assignList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"petitionUser"}},[a("el-input",{attrs:{placeholder:"上访员",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.petitionUser,callback:function(t){e.$set(e.queryParams,"petitionUser",t)},expression:"queryParams.petitionUser"}})],1),a("el-form-item",{attrs:{label:"扣款时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.queryParams.allocationTime,callback:function(t){e.$set(e.queryParams,"allocationTime",t)},expression:"queryParams.allocationTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_account_loan:vw_account_loan:remove"],expression:"['vw_account_loan:vw_account_loan:remove']"}],attrs:{type:"primary",plain:"",size:"mini",disabled:e.multiple},on:{click:e.openAssignDialog}},[e._v(" 分配 ")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vw_account_loanList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"指派状态",align:"center",prop:"petitionAssignmentType"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(1===t.row.petitionAssignmentType?"未指派":2===t.row.petitionAssignmentType?"已撤销":3===t.row.petitionAssignmentType?"已指派":"未知")+" ")]}}])}),a("el-table-column",{attrs:{label:"逾期状态",align:"center",prop:"slippageStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getSlippageStatusText(t.row.slippageStatus))+" ")]}}])}),a("el-table-column",{attrs:{label:"上访员",align:"center",prop:"petitionUser"}}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"customerName"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openUserInfo({customerId:t.row.customerId,applyId:t.row.applyId})}}},[e._v(" "+e._s(t.row.customerName)+" ")])]}}])}),a("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"jgName"}}),a("el-table-column",{attrs:{label:"业务员",align:"center",prop:"nickName"}}),a("el-table-column",{attrs:{label:"车牌号码",align:"center",prop:"plateNo"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openCarInfo(t.row.plateNo)}}},[e._v(e._s(t.row.plateNo))])]}}])}),a("el-table-column",{attrs:{label:"车牌状态",align:"center",prop:"carStatus"}}),a("el-table-column",{attrs:{label:"车牌位置",align:"center",prop:"carDetailAddress"}}),a("el-table-column",{attrs:{label:"GPS状态",align:"center",prop:"gpsStatus"}}),a("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"orgName"}}),a("el-table-column",{attrs:{label:"逾期天数",align:"center",prop:"overdueDays",width:"130"}}),a("el-table-column",{attrs:{label:"首期逾期金额",align:"center",prop:"foverdueAmount",width:"130"}}),a("el-table-column",{attrs:{label:"银行逾期金额",align:"center",prop:"boverdueAmount",width:"130"}}),a("el-table-column",{attrs:{label:"代扣逾期金额",align:"center",prop:"doverdueAmount",width:"130"}}),a("el-table-column",{attrs:{label:"代扣结清金额",align:"center",prop:"drepaymentAmounts",width:"130"}}),a("el-table-column",{attrs:{label:"银行结清金额",align:"center",prop:"brepaymentAmounts",width:"130"}}),a("el-table-column",{attrs:{label:"催记类型",align:"center",prop:"loanReminder.urgeStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getUrgeStatusText(t.row.loanReminder&&t.row.loanReminder.urgeStatus))+" ")]}}])}),a("el-table-column",{attrs:{label:"催记日期",align:"center",prop:"loanReminder.createTime"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.parseTime(t.row.loanReminder&&t.row.loanReminder.createTime,"{y}-{m}-{d}"))+" ")]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-popover",{attrs:{placement:"left",trigger:"click","popper-class":"custom-popover"}},[a("div",{staticClass:"operation-buttons"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_account_loan:vw_account_loan:edit"],expression:"['vw_account_loan:vw_account_loan:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.logView(t.row)}}},[e._v(" 查看催记 ")]),3===t.row.petitionAssignmentType?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_account_loan:vw_account_loan:remove"],expression:"['vw_account_loan:vw_account_loan:remove']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(" 撤销上访 ")]):e._e()],1),a("el-button",{attrs:{slot:"reference",size:"mini",type:"text"},slot:"reference"},[e._v("更多")])],1)]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"贷款人",prop:"applyId"}},[a("el-input",{attrs:{placeholder:"请输入申请编号",disabled:!0},model:{value:e.form.applyId,callback:function(t){e.$set(e.form,"applyId",t)},expression:"form.applyId"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"出单渠道",prop:"applyId"}},[a("el-input",{attrs:{placeholder:"请输入申请编号",disabled:!0},model:{value:e.form.applyId,callback:function(t){e.$set(e.form,"applyId",t)},expression:"form.applyId"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"放款银行",prop:"putoutId"}},[a("el-input",{attrs:{placeholder:"请输入出账编号",disabled:!0},model:{value:e.form.putoutId,callback:function(t){e.$set(e.form,"putoutId",t)},expression:"form.putoutId"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"银行逾期金额",prop:"applyId"}},[a("el-input",{attrs:{placeholder:"请输入申请编号",disabled:!0},model:{value:e.form.applyId,callback:function(t){e.$set(e.form,"applyId",t)},expression:"form.applyId"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"代扣逾期金额",prop:"applyId"}},[a("el-input",{attrs:{placeholder:"请输入申请编号",disabled:!0},model:{value:e.form.applyId,callback:function(t){e.$set(e.form,"applyId",t)},expression:"form.applyId"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"车辆状态",prop:"loanBank"}},[a("el-select",{attrs:{placeholder:"车辆状态",clearable:""},model:{value:e.form.loanBank,callback:function(t){e.$set(e.form,"loanBank",t)},expression:"form.loanBank"}},e._l(e.bankList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"还款类型",prop:"loanBank"}},[a("el-select",{attrs:{placeholder:"还款类型",clearable:""},model:{value:e.form.loanBank,callback:function(t){e.$set(e.form,"loanBank",t)},expression:"form.loanBank"}},e._l(e.bankList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"代扣金额",prop:"applyId"}},[a("el-input",{attrs:{placeholder:"代扣金额"},model:{value:e.form.applyId,callback:function(t){e.$set(e.form,"applyId",t)},expression:"form.applyId"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"loanBank"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:""},model:{value:e.form.loanBank,callback:function(t){e.$set(e.form,"loanBank",t)},expression:"form.loanBank"}},e._l(e.bankList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"还款凭证",prop:"loanBank"}},[a("el-upload",{attrs:{action:"https://jsonplaceholder.typicode.com/posts/","list-type":"picture-card","on-preview":e.handlePictureCardPreview,"on-remove":e.handleRemove}},[a("i",{staticClass:"el-icon-plus"})]),a("el-dialog",{attrs:{visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"银行还款金额",prop:"applyId"}},[a("el-input",{attrs:{placeholder:"银行还款金额"},model:{value:e.form.applyId,callback:function(t){e.$set(e.form,"applyId",t)},expression:"form.applyId"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"loanBank"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:""},model:{value:e.form.loanBank,callback:function(t){e.$set(e.form,"loanBank",t)},expression:"form.loanBank"}},e._l(e.bankList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"还款凭证",prop:"loanBank"}},[a("el-upload",{attrs:{action:"https://jsonplaceholder.typicode.com/posts/","list-type":"picture-card","on-preview":e.handlePictureCardPreview,"on-remove":e.handleRemove}},[a("i",{staticClass:"el-icon-plus"})]),a("el-dialog",{attrs:{visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"预计还款时间",prop:"putoutDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"预计还款时间"},model:{value:e.form.putoutDate,callback:function(t){e.$set(e.form,"putoutDate",t)},expression:"form.putoutDate"}})],1)],1)],1),a("el-form-item",{attrs:{label:"催记类型",prop:"loanBank"}},[a("el-select",{attrs:{placeholder:"催记类型",clearable:""},model:{value:e.form.loanBank,callback:function(t){e.$set(e.form,"loanBank",t)},expression:"form.loanBank"}},e._l(e.bankList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"催记描述",prop:"loanBank"}},[a("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入催记描述"},model:{value:e.textarea,callback:function(t){e.textarea=t},expression:"textarea"}})],1),a("el-form-item",{attrs:{label:"下次跟进时间",prop:"putoutDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"下次跟进时间"},model:{value:e.form.putoutDate,callback:function(t){e.$set(e.form,"putoutDate",t)},expression:"form.putoutDate"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"工作指派",visible:e.assignOpen,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.assignOpen=t}}},[a("el-form",{ref:"assignForm",attrs:{model:e.assignFormData,rules:e.assignRules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"上访人员",prop:"petitionUser"}},[a("el-select",{attrs:{placeholder:"上访人员",clearable:""},model:{value:e.assignFormData.petitionUser,callback:function(t){e.$set(e.assignFormData,"petitionUser",t)},expression:"assignFormData.petitionUser"}},e._l(e.dcNameList,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitAssignForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancelassign}},[e._v("取 消")])],1)],1),a("userInfo",{ref:"userInfo",attrs:{visible:e.userInfoVisible,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.userInfoVisible=t}}}),a("carInfo",{ref:"carInfo",attrs:{visible:e.carInfoVisible,title:"车辆信息",plateNo:e.plateNo,permission:"2"},on:{"update:visible":function(t){e.carInfoVisible=t}}}),a("loan-reminder-log",{ref:"loanReminderLog",attrs:{"loan-id":e.currentRow.loanId}})],1)},n=[],r=a("2909"),o=a("5530"),s=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("bd52")),i=a("2eca"),u=a("0f5f"),c=a("7954"),p=(a("9f56"),a("a4ce")),m={name:"Vw_account_loan",components:{userInfo:i["a"],carInfo:u["a"],LoanReminderLog:c["a"]},data:function(){return{assignRules:{petitionUser:[{required:!0,message:"请选择上访人员",trigger:"change"}]},assignFormData:{petitionUser:"",ids:[]},assignOpen:!1,dcNameList:[],dialogImageUrl:"",dialogVisible:!1,textarea:"",loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,vw_account_loanList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,nickName:null,certId:null,carNo:null,loanBank:null,jgName:null,assignStatus:null,petitioner:null,followUpType:null,allocationTime:null,salesman:null},bankList:[],assignList:[{label:"未指派",value:1},{label:"已撤销",value:2},{label:"已指派",value:3}],followUpList:[{label:"无法跟进",value:1},{label:"约定还款",value:2},{label:"继续联系",value:3}],form:{},rules:{},applyId:"",lenderShow:!1,plateNo:"",carInfoVisible:!1,currentRow:{},customerInfo:{customerId:"",applyId:""},userInfoVisible:!1}},created:function(){this.getTeam(),this.getList(),this.getPetition()},methods:{handleDelete:function(e){var t=this;this.$confirm("确定要撤销该上访指派吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(s["u"])(e.loanId).then((function(e){200===e.code?(t.$message.success("撤销成功"),t.getList()):t.$message.error(e.msg||"撤销失败")}))}))},submitAssignForm:function(){var e=this;this.$refs.assignForm.validate((function(t){if(t){if(!e.ids||0===e.ids.length)return void e.$message.warning("请选择需要分配的记录");var a=Object(o["a"])(Object(o["a"])({},e.assignFormData),{},{ids:e.ids});Object(s["d"])(a).then((function(t){200===t.code?(e.assignOpen=!1,e.$message.success("工作指派成功"),e.assignFormData.urgeUser=null,e.getList()):e.$message.error(t.msg||"工作指派失败")}))}}))},cancelassign:function(){this.assignOpen=!1},openAssignDialog:function(){this.ids.length?(this.assignFormData={petitionUser:"",ids:Object(r["a"])(this.ids)},this.assignOpen=!0):this.$modal.msgWarning("请先选择要分配的数据")},getPetition:function(){var e=this;Object(s["j"])(105).then((function(t){200===t.code&&Array.isArray(t.rows)?e.dcNameList=t.rows.map((function(e){return e.userName||e.nickName})):(console.dcNameList("获取DCC列表失败:",t.msg),e.dcNameList=[])})).catch((function(t){console.error("请求发生错误:",t),e.dcNameList=[]}))},getUrgeStatusText:function(e){switch(e){case 1:case"1":return"继续跟踪";case 2:case"2":return"约定还款";case 3:case"3":return"无法跟进";case 4:case"4":return"暂时无需跟进";default:return""}},getSlippageStatusText:function(e){switch(e){case"1":return"提醒";case"2":return"电催";case"3":return"上访";default:return"未知"}},handlePictureCardPreview:function(){},handleRemove:function(){},getTeam:function(){var e=this;Object(p["e"])(this.queryParams).then((function(t){e.bankList=t.data}))},getList:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.queryParams;this.loading=!0,Object(s["n"])(t).then((function(t){e.vw_account_loanList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,applyId:null,putoutId:null,contractId:null,customerId:null,customerName:null,loanStatus:null,occurType:null,productId:null,partnerId:null,currency:null,businessSum:null,contractAmt:null,putoutDate:null,billDate:null,firstRepayDate:null,lastDueDate:null,nextDueDate:null,maturityDate:null,term:null,termUnit:null,settleDate:null,finishDate:null,originalMaturityDate:null,rateTermId:null,rptTermId:null,finTermId:null,feeTermId:null,normalBalance:null,nextInstalmentAmt:null,currentPeriod:null,currentBalance:null,currenctinteBalance:null,overdueBalance:null,odinteBalance:null,fineinteBalance:null,compdinteBalance:null,overdueAmt:null,overdueDays:null,lcaTimes:null,totalPeriod:null,graceinteBalance:null,accrueinteBalance:null,repriceType:null,repriceFlag:null,repriceCycle:null,repriceDate:null,lastRepriceDate:null,nextRepriceDate:null,graceDays:null,loanOverDateFlag:null,holidayPaymentFlag:null,autoPayFlag:null,interestTypeFlag:null,classifyResult:null,batchNo:null,lockFlag:null,businessDate:null,putoutRate:null,putoutPaymentMethod:null,loanRate:null,managementFeeRate:null,paymentFlag:null,performanceStatus:null,orgId:null,officeId:null,userId:null,mangerId:null,allotStatus:null,allotAssignee:null,litigationStatus:null,subType:null,numberStatus:null,lagFlag:null,createBy:null,createDate:null,updateBy:null,updateDate:null,mobilePhone:null,nickName:null,jgName:null,jgStatus:null,orgName:null,plateNo:null},this.resetForm("form")},handleQuery:function(){this.queryParams.allocationTime&&(this.queryParams.startTime=this.queryParams.allocationTime[0],this.queryParams.endTime=this.queryParams.allocationTime[1]),this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.customerName=null,this.queryParams.certId=null,this.queryParams.plateNo=null,this.queryParams.nickName=null,this.queryParams.jgName=null,this.queryParams.orgName=null,this.queryParams.petitionAssignmentType=null,this.queryParams.petitionUser=null,this.queryParams.allocationTime=null,this.queryParams.startTime=null,this.queryParams.endTime=null,this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return String(e.loanId)})),this.single=1!==e.length,this.multiple=!e.length,this.assignFormData.ids=this.ids},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?Object(s["A"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(s["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},openUserInfo:function(e){this.customerInfo=e,this.userInfoVisible=!0},openCarInfo:function(e){this.plateNo=e,this.carInfoVisible=!0},logView:function(e){this.currentRow=e,this.$refs.loanReminderLog.openLogDialog()}}},d=m,f=(a("f50f"),a("ed8a"),a("2877")),b=Object(f["a"])(d,l,n,!1,null,"234812aa",null);t["default"]=b.exports},ed8a:function(e,t,a){"use strict";a("f678")},f50f:function(e,t,a){"use strict";a("c0ae")},f678:function(e,t,a){}}]);