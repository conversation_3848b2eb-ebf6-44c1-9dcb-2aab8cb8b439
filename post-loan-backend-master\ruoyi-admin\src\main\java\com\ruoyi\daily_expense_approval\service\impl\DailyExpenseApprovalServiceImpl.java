package com.ruoyi.daily_expense_approval.service.impl;

import java.util.ArrayList;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.daily_expense_approval.mapper.DailyExpenseApprovalMapper;
import com.ruoyi.daily_expense_approval.domain.DailyExpenseApproval;
import com.ruoyi.daily_expense_approval.service.IDailyExpenseApprovalService;
import com.ruoyi.system.service.ISysUserService;

/**
 * 日常花费审批Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class DailyExpenseApprovalServiceImpl implements IDailyExpenseApprovalService 
{
    @Autowired
    private DailyExpenseApprovalMapper dailyExpenseApprovalMapper;

    @Autowired
    private ISysUserService sysUserService;

    /**
     * 查询日常花费审批
     * 
     * @param id 日常花费审批主键
     * @return 日常花费审批
     */
    @Override
    public DailyExpenseApproval selectDailyExpenseApprovalById(Long id)
    {
        return dailyExpenseApprovalMapper.selectDailyExpenseApprovalById(id);
    }

    /**
     * 查询日常花费审批列表
     * 
     * @param dailyExpenseApproval 日常花费审批
     * @return 日常花费审批
     */
    @Override
    public List<DailyExpenseApproval> selectDailyExpenseApprovalList(DailyExpenseApproval dailyExpenseApproval)
    {
        return dailyExpenseApprovalMapper.selectDailyExpenseApprovalList(dailyExpenseApproval);
    }

    /**
     * 新增日常花费审批
     *
     * @param dailyExpenseApproval 日常花费审批
     * @return 结果
     */
    @Override
    public int insertDailyExpenseApproval(DailyExpenseApproval dailyExpenseApproval)
    {
        // 自动填充申请人信息
        String username = SecurityUtils.getUsername();
        if (dailyExpenseApproval.getApplicantId() == null || dailyExpenseApproval.getApplicantId().isEmpty()) {
            dailyExpenseApproval.setApplicantId(username);
        }
        if (dailyExpenseApproval.getApplicantName() == null || dailyExpenseApproval.getApplicantName().isEmpty()) {
            String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
            dailyExpenseApproval.setApplicantName(nickName);
        }

        // 设置默认审批状态为待审批
        if (dailyExpenseApproval.getApprovalStatus() == null) {
            dailyExpenseApproval.setApprovalStatus("0"); // 0-待审批
        }

        // 设置申请时间
        if (dailyExpenseApproval.getApplicationTime() == null) {
            dailyExpenseApproval.setApplicationTime(DateUtils.getNowDate());
        }

        dailyExpenseApproval.setCreateTime(DateUtils.getNowDate());
        dailyExpenseApproval.setCreateBy(username);
        return dailyExpenseApprovalMapper.insertDailyExpenseApproval(dailyExpenseApproval);
    }

    /**
     * 修改日常花费审批
     * 
     * @param dailyExpenseApproval 日常花费审批
     * @return 结果
     */
    @Override
    public int updateDailyExpenseApproval(DailyExpenseApproval dailyExpenseApproval)
    {
        dailyExpenseApproval.setUpdateTime(DateUtils.getNowDate());
        return dailyExpenseApprovalMapper.updateDailyExpenseApproval(dailyExpenseApproval);
    }

    /**
     * 批量删除日常花费审批
     * 
     * @param ids 需要删除的日常花费审批主键
     * @return 结果
     */
    @Override
    public int deleteDailyExpenseApprovalByIds(Long[] ids)
    {
        return dailyExpenseApprovalMapper.deleteDailyExpenseApprovalByIds(ids);
    }

    /**
     * 删除日常花费审批信息
     *
     * @param id 日常花费审批主键
     * @return 结果
     */
    @Override
    public int deleteDailyExpenseApprovalById(Long id)
    {
        return dailyExpenseApprovalMapper.deleteDailyExpenseApprovalById(id);
    }

    /**
     * 审批日常花费申请
     *
     * @param id 日常花费审批主键
     * @param approvalAction 审批动作（approve-通过，reject-拒绝）
     * @param approvalRemark 审批备注
     * @return 结果
     */
    @Override
    public int approveDailyExpense(Long id, String approvalAction, String approvalRemark)
    {
        // 先查询当前记录
        DailyExpenseApproval currentApproval = dailyExpenseApprovalMapper.selectDailyExpenseApprovalById(id);
        if (currentApproval == null) {
            throw new RuntimeException("审批记录不存在");
        }

        // 获取当前用户角色
        String userRole = SecurityUtils.getLoginUser().getUser().getRoles().get(0).getRoleName();

        // 检查是否有权限审批
        if (!currentApproval.canApprove(userRole)) {
            throw new RuntimeException("您没有权限进行当前阶段的审批，当前状态为【" + currentApproval.getStatusDescription() + "】");
        }

        // 设置审批人信息
        String username = SecurityUtils.getUsername();
        currentApproval.setApproverId(username);
        currentApproval.setApproverName(SecurityUtils.getLoginUser().getUser().getNickName());
        currentApproval.setApprovalTime(DateUtils.getNowDate());
        currentApproval.setApprovalRemark(approvalRemark);
        currentApproval.setUpdateTime(DateUtils.getNowDate());
        currentApproval.setUpdateBy(username);

        // 根据审批动作设置状态
        if ("reject".equals(approvalAction)) {
            // 拒绝：直接设置为已拒绝状态
            currentApproval.setApprovalStatus(DailyExpenseApproval.STATUS_REJECTED);
        } else if ("approve".equals(approvalAction)) {
            // 通过：进入下一个审批状态
            String nextStatus = currentApproval.getNextApprovalStatus();
            currentApproval.setApprovalStatus(nextStatus);
        } else {
            throw new RuntimeException("无效的审批动作");
        }

        return dailyExpenseApprovalMapper.updateDailyExpenseApproval(currentApproval);
    }

    /**
     * 根据用户角色查询待审批的日常费用申请
     *
     * @param dailyExpenseApproval 查询条件
     * @return 待审批列表
     */
    @Override
    public List<DailyExpenseApproval> selectPendingApprovalList(DailyExpenseApproval dailyExpenseApproval)
    {
        // 获取当前用户角色
        String userRole = SecurityUtils.getLoginUser().getUser().getRoles().get(0).getRoleName();

        // 根据角色设置查询状态
        List<String> statusList = new ArrayList<>();
        if ("贷后主管".equals(userRole)) {
            // 贷后主管：可以审批待审批和贷后主管审批状态
            statusList.add(DailyExpenseApproval.STATUS_PENDING);
            statusList.add(DailyExpenseApproval.STATUS_SUPERVISOR);
        } else if ("director".equals(userRole)) {
            // 总监：可以审批总监审批状态
            statusList.add(DailyExpenseApproval.STATUS_DIRECTOR);
        } else if ("manager".equals(userRole)) {
            // 总经理：可以审批财务主管审批和总经理审批状态
            statusList.add(DailyExpenseApproval.STATUS_FINANCE_SUPERVISOR);
            statusList.add(DailyExpenseApproval.STATUS_GENERAL_MANAGER);
        }

        if (statusList.isEmpty()) {
            return new ArrayList<>();
        }

        // 查询符合条件的记录
        List<DailyExpenseApproval> allList = dailyExpenseApprovalMapper.selectDailyExpenseApprovalList(dailyExpenseApproval);
        return allList.stream()
                .filter(item -> item.getApprovalStatus() != null && statusList.contains(item.getApprovalStatus()))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 初始化空的审批状态
     *
     * @return 更新的记录数
     */
    @Override
    public int initializeNullApprovalStatus()
    {
        return dailyExpenseApprovalMapper.updateNullApprovalStatus();
    }
}
