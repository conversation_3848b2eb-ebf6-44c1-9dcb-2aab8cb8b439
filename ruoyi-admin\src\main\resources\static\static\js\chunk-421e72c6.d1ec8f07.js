(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-421e72c6"],{"0f5f":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.carInfo.plateNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车架号"}},[e._v(e._s(e.carInfo.identityNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"发动机号"}},[e._v(e._s(e.carInfo.engineNumber||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(e._s(e.carStatusLabel))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆位置"}},[e._v(e._s(e.carInfo.carAddress||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(e._s(e.gpsStatusLabel))]):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看车牌照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"行驶证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看行驶证照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"登记证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看登记证照片")])],1):e._e()],1)],1)},s=[],r=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),a("bd52")),o={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var e=this,t=this.carStatusList.find((function(t){return t.value===e.carInfo.carStatus}));return t?t.label:"未知状态"},gpsStatusLabel:function(){var e=this,t=this.gpsStatusList.find((function(t){return t.value===e.carInfo.gpsStatus}));return t?t.label:"未知状态"}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,e&&this.plateNo&&Object(r["e"])(this.plateNo).then((function(e){t.carInfo=e.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},n=o,i=a("2877"),c=Object(i["a"])(n,l,s,!1,null,null,null);t["a"]=c.exports},1791:function(e,t,a){},"2eca":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[e._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},s=[],r=a("bd52"),o={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(r["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},n=o,i=(a("d6fd"),a("2877")),c=Object(i["a"])(n,l,s,!1,null,"8a3d4978",null);t["a"]=c.exports},"3fa2":function(e,t,a){"use strict";a("4e87")},"4e87":function(e,t,a){},a5e3:function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{staticClass:"dialogBox",attrs:{"close-on-click-modal":!1,title:"提交催记",visible:e.isReminderFormOpen,width:"800px"},on:{"update:visible":function(t){e.isReminderFormOpen=t}}},[a("el-descriptions",{attrs:{column:3,border:""}},[a("el-descriptions-item",{attrs:{label:"贷款人"}},[e._v(e._s(e.descForm.customerName))]),a("el-descriptions-item",{attrs:{label:"手机号"}},[e._v(e._s(e.descForm.mobilePhone))]),a("el-descriptions-item",{attrs:{label:"出单渠道"}},[e._v(e._s(e.descForm.jgName))]),a("el-descriptions-item",{attrs:{label:"放款银行"}},[e._v(e._s(e.descForm.orgName))]),a("el-descriptions-item",{attrs:{label:"银行逾期金额"}},[e._v(e._s(e.descForm.boverdueAmount))]),a("el-descriptions-item",{attrs:{label:"代扣逾期金额"}},[e._v(e._s(e.descForm.doverdueAmount))])],1),a("el-form",{ref:"form",staticStyle:{"margin-top":"30px"},attrs:{model:e.form,"label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"车辆状态",prop:"carStatus"}},[a("el-select",{attrs:{placeholder:"车辆状态"},model:{value:e.form.carStatus,callback:function(t){e.$set(e.form,"carStatus",t)},expression:"form.carStatus"}},e._l(e.carStatusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"还款类型",prop:"repaymentStatus"}},[a("el-select",{attrs:{placeholder:"还款类型",clearable:""},model:{value:e.form.repaymentStatus,callback:function(t){e.$set(e.form,"repaymentStatus",t)},expression:"form.repaymentStatus"}},e._l(e.repaymentList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"催记类型",prop:"urgeStatus"}},[a("el-select",{attrs:{placeholder:"催记类型",clearable:""},model:{value:e.form.urgeStatus,callback:function(t){e.$set(e.form,"urgeStatus",t)},expression:"form.urgeStatus"}},e._l(e.followUpList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),2==e.form.urgeStatus?a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"还款时间",prop:"appointedTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"还款时间"},model:{value:e.form.appointedTime,callback:function(t){e.$set(e.form,"appointedTime",t)},expression:"form.appointedTime"}})],1)],1)],1):e._e(),1==e.form.urgeStatus?a("el-form-item",{attrs:{label:"下次跟进时间",prop:"trackingTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"下次跟进时间"},model:{value:e.form.trackingTime,callback:function(t){e.$set(e.form,"trackingTime",t)},expression:"form.trackingTime"}})],1):e._e(),a("el-form-item",{attrs:{label:"催记描述",prop:"urgeDescribe"}},[a("el-input",{staticStyle:{width:"90%"},attrs:{type:"textarea",rows:2,placeholder:"请输入催记描述"},model:{value:e.form.urgeDescribe,callback:function(t){e.$set(e.form,"urgeDescribe",t)},expression:"form.urgeDescribe"}})],1),e.form.appointedTime?a("el-collapse",{attrs:{accordion:""}},[a("el-collapse-item",[a("template",{slot:"title"},[a("div",{staticClass:"collapse-title"},[a("span",{staticClass:"title-text"},[e._v("银行还款")]),a("div",{staticClass:"status-badges"},[a("span",{staticClass:"field-label"},[e._v("金额")]),a("el-badge",{attrs:{size:"small",value:e.form.bmoney?"√":"!",type:e.form.bmoney?"success":"warning"}}),a("span",{staticClass:"field-label"},[e._v("账号")]),a("el-badge",{attrs:{size:"small",value:e.form.baccount?"√":"!",type:e.form.baccount?"success":"warning"}}),a("span",{staticClass:"field-label"},[e._v("凭证")]),a("el-badge",{attrs:{size:"small",value:e.form.brepaymentImg.length>0?"√":"!",type:e.form.brepaymentImg.length>0?"success":"warning"}})],1)])]),a("el-row",{staticStyle:{"margin-top":"20px"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"银行还款金额",prop:"bmoney"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"银行还款金额"},model:{value:e.form.bmoney,callback:function(t){e.$set(e.form,"bmoney",t)},expression:"form.bmoney"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"baccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:"","allow-create":"",filterable:""},model:{value:e.form.baccount,callback:function(t){e.$set(e.form,"baccount",t)},expression:"form.baccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"还款凭证",prop:"brepaymentImg"}},[a("el-upload",{attrs:{action:e.uploadImgUrl,"list-type":"picture-card","file-list":e.form.brepaymentImg,headers:e.headers,"on-success":function(t,a,l){return e.handleUploadSuccess(t,a,l,"form.brepaymentImg")},"on-remove":function(t,a){return e.handleRemove(t,a,"form.brepaymentImg")},"on-error":e.handleUploadError}},[a("i",{staticClass:"el-icon-plus"})])],1)],2),a("el-collapse-item",{attrs:{name:"2"}},[a("template",{slot:"title"},[a("div",{staticClass:"collapse-title"},[a("span",{staticClass:"title-text"},[e._v("代扣还款")]),a("div",{staticClass:"status-badges"},[a("span",{staticClass:"field-label"},[e._v("金额")]),a("el-badge",{attrs:{size:"small",value:e.form.dmoney?"√":"!",type:e.form.dmoney?"success":"warning"}}),a("span",{staticClass:"field-label"},[e._v("账号")]),a("el-badge",{attrs:{size:"small",value:e.form.daccount?"√":"!",type:e.form.daccount?"success":"warning"}}),a("span",{staticClass:"field-label"},[e._v("凭证")]),a("el-badge",{attrs:{size:"small",value:e.form.drepaymentImg.length>0?"√":"!",type:e.form.drepaymentImg.length>0?"success":"warning"}})],1)])]),a("el-row",{staticStyle:{"margin-top":"20px"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"代扣还款金额",prop:"dmoney"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"代扣金额"},model:{value:e.form.dmoney,callback:function(t){e.$set(e.form,"dmoney",t)},expression:"form.dmoney"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"daccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:"","allow-create":"",filterable:""},model:{value:e.form.daccount,callback:function(t){e.$set(e.form,"daccount",t)},expression:"form.daccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"还款凭证",prop:"drepaymentImg"}},[a("el-upload",{attrs:{action:e.uploadImgUrl,"list-type":"picture-card","file-list":e.form.drepaymentImg,headers:e.headers,"on-success":function(t,a,l){return e.handleUploadSuccess(t,a,l,"form.drepaymentImg")},"on-remove":function(t,a){return e.handleRemove(t,a,"form.drepaymentImg")},"on-error":e.handleUploadError}},[a("i",{staticClass:"el-icon-plus"})])],1)],2),a("el-collapse-item",{attrs:{name:"3"}},[a("template",{slot:"title"},[a("div",{staticClass:"collapse-title"},[a("span",{staticClass:"title-text"},[e._v("其他还款")]),a("div",{staticClass:"status-badges"},[a("span",{staticClass:"field-label"},[e._v("金额")]),a("el-badge",{attrs:{size:"small",value:e.form.omoney?"√":"!",type:e.form.omoney?"success":"warning"}}),a("span",{staticClass:"field-label"},[e._v("账号")]),a("el-badge",{attrs:{size:"small",value:e.form.oaccount?"√":"!",type:e.form.oaccount?"success":"warning"}}),a("span",{staticClass:"field-label"},[e._v("凭证")]),a("el-badge",{attrs:{size:"small",value:e.form.orepaymentImg.length>0?"√":"!",type:e.form.orepaymentImg.length>0?"success":"warning"}})],1)])]),a("el-row",{staticStyle:{"margin-top":"20px"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"其他还款金额",prop:"omoney"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"其他还款金额"},model:{value:e.form.omoney,callback:function(t){e.$set(e.form,"omoney",t)},expression:"form.omoney"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"oaccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:"","allow-create":"",filterable:""},model:{value:e.form.oaccount,callback:function(t){e.$set(e.form,"oaccount",t)},expression:"form.oaccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"还款凭证",prop:"orepaymentImg"}},[a("el-upload",{attrs:{action:e.uploadImgUrl,"list-type":"picture-card","file-list":e.form.orepaymentImg,headers:e.headers,"on-success":function(t,a,l){return e.handleUploadSuccess(t,a,l,"form.orepaymentImg")},"on-remove":function(t,a){return e.handleRemove(t,a,"form.orepaymentImg")},"on-error":e.handleUploadError}},[a("i",{staticClass:"el-icon-plus"})])],1)],2),a("el-collapse-item",{attrs:{name:"4"}},[a("template",{slot:"title"},[a("div",{staticClass:"collapse-title"},[a("span",{staticClass:"title-text"},[e._v("代偿还款")]),a("div",{staticClass:"status-badges"},[a("span",{staticClass:"field-label"},[e._v("金额")]),a("el-badge",{attrs:{size:"small",value:e.form.cmoney?"√":"!",type:e.form.cmoney?"success":"warning"}}),a("span",{staticClass:"field-label"},[e._v("账号")]),a("el-badge",{attrs:{size:"small",value:e.form.caccount?"√":"!",type:e.form.caccount?"success":"warning"}}),a("span",{staticClass:"field-label"},[e._v("凭证")]),a("el-badge",{attrs:{size:"small",value:e.form.crepaymentImg.length>0?"√":"!",type:e.form.crepaymentImg.length>0?"success":"warning"}})],1)])]),a("el-row",{staticStyle:{"margin-top":"20px"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"代偿还款金额",prop:"cmoney"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"代偿还款金额"},model:{value:e.form.cmoney,callback:function(t){e.$set(e.form,"cmoney",t)},expression:"form.cmoney"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"caccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:"","allow-create":"",filterable:""},model:{value:e.form.caccount,callback:function(t){e.$set(e.form,"caccount",t)},expression:"form.caccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"还款凭证",prop:"crepaymentImg"}},[a("el-upload",{attrs:{action:e.uploadImgUrl,"list-type":"picture-card","file-list":e.form.crepaymentImg,headers:e.headers,"on-success":function(t,a,l){return e.handleUploadSuccess(t,a,l,"form.crepaymentImg")},"on-remove":function(t,a){return e.handleRemove(t,a,"form.crepaymentImg")},"on-error":e.handleUploadError}},[a("i",{staticClass:"el-icon-plus"})])],1)],2),a("el-collapse-item",{attrs:{name:"5"}},[a("template",{slot:"title"},[a("div",{staticClass:"collapse-title"},[a("span",{staticClass:"title-text"},[e._v("违约金还款")]),a("div",{staticClass:"status-badges"},[a("span",{staticClass:"field-label"},[e._v("金额")]),a("el-badge",{attrs:{size:"small",value:e.form.pmoney?"√":"!",type:e.form.pmoney?"success":"warning"}}),a("span",{staticClass:"field-label"},[e._v("账号")]),a("el-badge",{attrs:{size:"small",value:e.form.paccount?"√":"!",type:e.form.paccount?"success":"warning"}}),a("span",{staticClass:"field-label"},[e._v("凭证")]),a("el-badge",{attrs:{size:"small",value:e.form.prepaymentImg.length>0?"√":"!",type:e.form.prepaymentImg.length>0?"success":"warning"}})],1)])]),a("el-row",{staticStyle:{"margin-top":"20px"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"违约金还款金额",prop:"pmoney"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"违约金还款金额"},model:{value:e.form.pmoney,callback:function(t){e.$set(e.form,"pmoney",t)},expression:"form.pmoney"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号类型",prop:"paccount"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:"","allow-create":"",filterable:""},model:{value:e.form.paccount,callback:function(t){e.$set(e.form,"paccount",t)},expression:"form.paccount"}},e._l(e.accountList,(function(e){return a("el-option",{key:e.card,attrs:{label:e.name,value:e.card}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"还款凭证",prop:"prepaymentImg"}},[a("el-upload",{attrs:{action:e.uploadImgUrl,"list-type":"picture-card","file-list":e.form.prepaymentImg,headers:e.headers,"on-success":function(t,a,l){return e.handleUploadSuccess(t,a,l,"form.prepaymentImg")},"on-remove":function(t,a){return e.handleRemove(t,a,"form.prepaymentImg")},"on-error":e.handleUploadError}},[a("i",{staticClass:"el-icon-plus"})])],1)],2)],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitReminder}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},s=[],r=a("3835"),o=(a("a15b"),a("d81d"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("0643"),a("a573"),a("bd52")),n=a("5f87"),i={name:"LoanReminderLogSubmit",props:{loanId:{type:String,default:""},status:{type:[String,Number],default:1}},data:function(){return{isReminderFormOpen:!1,form:{},descForm:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],repaymentList:[{label:"未还款",value:0},{label:"还款",value:1},{label:"部分还款",value:2},{label:"分期还款",value:3},{label:"协商卖车",value:4},{label:"法诉结清",value:5},{label:"法诉减免结清",value:6},{label:"拍卖回款",value:7},{label:"法院划扣",value:8},{label:"参与分配回款",value:9}],followUpList:[{label:"继续联系",value:1},{label:"约定还款",value:2},{label:"无法跟进",value:3}],accountList:[],uploadImgUrl:"/prod-api/common/ossupload",headers:{Authorization:"Bearer "+Object(n["a"])()}}},watch:{isReminderFormOpen:{handler:function(e){var t=this;e&&Object(o["h"])(this.loanId).then((function(e){t.descForm=e.data}))}}},created:function(){this.getBankList()},methods:{getBankList:function(){var e=this;Object(o["i"])({isOff:1}).then((function(t){e.accountList=t.rows}))},openDialog:function(){var e=this;this.$nextTick((function(){e.form={carStatus:"",repaymentStatus:"",urgeStatus:"",trackingTime:"",appointedTime:"",bmoney:"",baccount:"",brepaymentImg:[],dmoney:"",daccount:"",drepaymentImg:[],omoney:"",oaccount:"",orepaymentImg:[],cmoney:"",caccount:"",crepaymentImg:[],pmoney:"",paccount:"",prepaymentImg:[],urgeDescribe:"",loanId:e.loanId},console.log("loanId:",e.loanId),console.log("form:",e.form)})),this.isReminderFormOpen=!0},submitReminder:function(){var e=this,t=JSON.parse(JSON.stringify(this.form));t.brepaymentImg=t.brepaymentImg.length>0?t.brepaymentImg.map((function(e){return e.response})).join(","):"",t.drepaymentImg=t.drepaymentImg.length>0?t.drepaymentImg.map((function(e){return e.response})).join(","):"",t.orepaymentImg=t.orepaymentImg.length>0?t.orepaymentImg.map((function(e){return e.response})).join(","):"",t.crepaymentImg=t.crepaymentImg.length>0?t.crepaymentImg.map((function(e){return e.response})).join(","):"",t.prepaymentImg=t.prepaymentImg.length>0?t.prepaymentImg.map((function(e){return e.response})).join(","):"",t.bOverdueAmount=this.descForm.boverdueAmount?Number(this.descForm.boverdueAmount):0,t.dOverdueAmount=this.descForm.doverdueAmount?Number(this.descForm.doverdueAmount):0,t.status=Number(this.status),console.log(t),Object(o["t"])(t).then((function(t){e.$message.success("催记提交成功"),e.cancel(),e.$emit("success")}))},cancel:function(){this.isReminderFormOpen=!1,this.resetForm()},resetForm:function(){this.form={}},handleUploadSuccess:function(e,t,a,l){var s=l.split("."),o=Object(r["a"])(s,2),n=o[0],i=o[1];this[n][i]=a,console.log(this.form.brepaymentImg)},handleRemove:function(e,t,a){var l=a.split("."),s=Object(r["a"])(l,2),o=s[0],n=s[1];this[o][n]=t,console.log(this.form.brepaymentImg)},handleUploadError:function(){this.$modal.msgError("上传图片失败，请重试"),this.$modal.closeLoading()}}},c=i,m=(a("3fa2"),a("2877")),p=Object(m["a"])(c,l,s,!1,null,"4792b739",null);t["a"]=p.exports},d6fd:function(e,t,a){"use strict";a("1791")}}]);