<template>
  <el-dialog :title="title" :visible.sync="visible" width="800px" append-to-body @close="resetForm">
    <el-form ref="form" :model="loanReminder" label-width="120px">
      <!-- 第一部分：文书相关 -->
      <el-divider content-position="left">
        <i class="el-icon-document"></i>
        贷款信息
      </el-divider>
      <!-- 非填入字段 -->
      <el-descriptions title="" :column="3" border>
        <el-descriptions-item label="贷款人">
          {{ loanReminder.customerName }}
        </el-descriptions-item>
        <el-descriptions-item label="出单渠道">
          {{ loanReminder.channel }}
        </el-descriptions-item>
        <el-descriptions-item label="放款银行">
          {{ loanReminder.bank }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 第一部分：文书相关 -->
      <el-divider content-position="left">
        <i class="el-icon-document"></i>
        文书信息
      </el-divider>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="变更法诉状态">
            <litigation-status v-model="litigationLog.status" placeholder="请选择法诉状态" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="文书名称">
            <el-select v-model="litigationLog.docName" placeholder="请选择文书名称" style="width: 100%">
              <el-option label="诉前调号" value="诉前调号" />
              <el-option label="民初号" value="民初号" />
              <el-option label="执行号" value="执行号" />
              <el-option label="执保号" value="执保号" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="文书号">
            <el-input v-model="litigationLog.docNumber" placeholder="请输入文书号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="文书生效">
            <el-date-picker v-model="litigationLog.docEffectiveDate" type="date" placeholder="选择日期" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="litigationLog.status === '待出法院文书'">
          <el-form-item label="登记开庭时间">
            <el-date-picker v-model="litigationLog.openDate" type="datetime" placeholder="选择开庭时间" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上传文书">
            <el-upload
              :data="data"
              :action="uploadUrl"
              :headers="headers"
              :limit="1"
              :file-list="litigationLog.docUploadUrl"
              :on-success="(res, file, fileList) => handleUploadSuccess(res, file, fileList, 'litigationLog.docUploadUrl')"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 'litigationLog.docUploadUrl')"
              :on-error="handleUploadError">
              <el-button size="small" type="primary" :disabled="litigationLog.docUploadUrl.length >= 1">点击上传</el-button>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二部分：还款相关 -->
      <el-divider content-position="left">
        <i class="el-icon-money"></i>
        还款信息
      </el-divider>

      <!-- 还款类型选择 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="还款类型">
            <el-select v-model="loanReminder.repaymentStatus" placeholder="请选择还款类型" style="width: 100%" @change="handleRepaymentTypeChange">
              <el-option label="部分还款" value="2" />
              <el-option label="分期还款" value="3" />
              <el-option label="协商买车" value="4" />
              <el-option label="法诉结清" value="5" />
              <el-option label="法诉减免结清" value="6" />
              <el-option label="拍卖回款" value="7" />
              <el-option label="法院划扣" value="8" />
              <el-option label="其他分配回款" value="9" />
            </el-select>
            <div v-if="loanReminder.repaymentStatus === '3'" class="form-tip" style="color: #409EFF;">
              <i class="el-icon-info"></i> 已选择分期还款，请填写下方分期申请信息
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 分期还款表单 -->
      <div v-if="loanReminder.repaymentStatus === '3'" class="installment-form-container">
        <div class="installment-form-header">
          <i class="el-icon-s-order"></i>
          <span>分期申请详情</span>
        </div>
        <el-form ref="installmentFormRef" :model="installmentForm" :rules="installmentRules" label-width="120px" class="installment-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="申请分期金额" prop="applyAmount" required>
                <el-input-number
                  v-model="installmentForm.applyAmount"
                  :min="0.01"
                  :max="999999999"
                  :precision="2"
                  :step="100"
                  :controls-position="'right'"
                  placeholder="请输入申请分期金额"
                  @input="handleInstallmentFormChange"
                  style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="分期期数" prop="periodCount" required>
                <el-input-number
                  v-model="installmentForm.periodCount"
                  :min="1"
                  :max="60"
                  :precision="0"
                  :step="1"
                  :controls-position="'right'"
                  placeholder="请输入分期期数"
                  @input="handleInstallmentFormChange"
                  style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="每期账单金额">
                <el-input v-model="installmentForm.billAmount" placeholder="自动计算" disabled />
                <div class="form-tip">
                  根据申请金额和期数自动计算
                  <span v-if="installmentForm.applyAmount > 0 && installmentForm.periodCount > 0">
                    <br>计算公式：({{ installmentForm.applyAmount }} - {{ installmentForm.tailAmount || 0 }}) ÷ {{ installmentForm.periodCount }} = {{ installmentForm.billAmount }}元/期
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="每期还款日" prop="repayDay" required>
                <el-select v-model="installmentForm.repayDay" placeholder="请选择每期还款日" style="width: 100%">
                  <el-option v-for="day in 28" :key="day" :label="`每月${day}号`" :value="day" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="尾款金额">
                <el-input-number
                  v-model="installmentForm.tailAmount"
                  :min="0"
                  :precision="2"
                  :step="100"
                  :controls-position="'right'"
                  placeholder="请输入尾款金额（可选）"
                  @input="handleInstallmentFormChange"
                  style="width: 100%" />
                <div class="form-tip">可选，如有尾款请填写</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="尾款支付时间" :prop="installmentForm.tailAmount > 0 ? 'tailPayTime' : ''">
                <el-date-picker
                  v-model="installmentForm.tailPayTime"
                  type="date"
                  placeholder="选择尾款支付时间"
                  :disabled="!installmentForm.tailAmount || installmentForm.tailAmount <= 0"
                  style="width: 100%" />
                <div class="form-tip">有尾款时必填</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="账号类型" prop="accountType" required>
                <el-select v-model="installmentForm.accountType" placeholder="请选择账号类型" style="width: 100%">
                  <el-option v-for="dict in accountList" :key="dict.card" :label="dict.name" :value="dict.name" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 其他还款类型的表单 -->
      <div v-else-if="loanReminder.repaymentStatus && loanReminder.repaymentStatus !== '3'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="款项明细类型">
              <el-select v-model="loanReminder.fundsRepayment" placeholder="请选择款项明细类型" style="width: 100%">
                <el-option label="律师费" value="律师费" />
                <el-option label="法诉费" value="法诉费" />
                <el-option label="保全费" value="保全费" />
                <el-option label="布控费" value="布控费" />
                <el-option label="公告费" value="公告费" />
                <el-option label="评估费" value="评估费" />
                <el-option label="执行费" value="执行费" />
                <el-option label="违约金" value="违约金" />
                <el-option label="担保费" value="担保费" />
                <el-option label="居间费" value="居间费" />
                <el-option label="代偿金" value="代偿金" />
                <el-option label="判决金额" value="判决金额" />
                <el-option label="利息" value="利息" />
                <el-option label="其他欠款" value="其他欠款" />
                <el-option label="保险费" value="保险费" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="金额">
              <el-input-number v-model="loanReminder.fundsAmount" :min="0" :precision="2" style="width: 100%" placeholder="请输入金额" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="账号类型">
              <el-select v-model="loanReminder.fundsAccountType" placeholder="请选择账号类型" style="width: 100%">
                <el-option v-for="dict in accountList" :key="dict.card" :label="dict.name" :value="dict.name" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号">
              <el-input v-model="loanReminder.accountNumber" :disabled="loanReminder.fundsAccountType !== '其他'" placeholder="请输入账号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="还款凭据">
              <el-upload
                :data="data"
                :action="uploadUrl"
                :headers="headers"
                list-type="picture-card"
                :file-list="loanReminder.fundsImage"
                :on-preview="handlePictureCardPreview"
                :on-success="(res, file, fileList) => handleUploadSuccess(res, file, fileList, 'loanReminder.fundsImage')"
                :on-remove="(file, fileList) => handleRemove(file, fileList, 'loanReminder.fundsImage')"
                :on-error="handleUploadError">
                <i class="el-icon-plus"></i>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </div>



      <!-- 第三部分：日志相关 -->
      <el-divider content-position="left">
        <i class="el-icon-notebook-2"></i>
        日志信息
      </el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="日志类型">
            <el-select v-model="loanReminder.urgeStatus" placeholder="请选择日志类型" style="width: 100%">
              <el-option label="继续跟踪" value="1" />
              <el-option label="约定还款" value="2" />
              <el-option label="无法跟进" value="3" />
              <el-option label="暂时无需跟进" value="4" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="下次跟进时间">
            <el-date-picker v-model="loanReminder.trackingTime" type="datetime" placeholder="选择跟进时间" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="日志描述">
            <el-input v-model="loanReminder.urgeDescribe" type="textarea" :rows="4" placeholder="请输入日志描述" maxlength="500" show-word-limit />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="submitForm">确 定</el-button>
    </div>
    <!-- 图片预览 -->
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </el-dialog>
</template>

<script>
import litigationStatus from '@/layout/components/Dialog/litigationStatus.vue'
import { getToken } from '@/utils/auth'
import { submitLitigationLog } from '@/api/litigation/litigation'
import { addInstallment_application_audit } from '@/api/installment_application_audit/installment_application_audit'
import { get_bank_account } from '@/api/vw_account_loan/vw_account_loan'

export default {
  name: 'LitigationLogForm',
  components: {
    litigationStatus,
  },
  props: {
    action: {
      type: String,
      default: '/common/ossupload',
    },
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      title: '提交法诉日志',
      visible: false,
      loanReminder: {},
      litigationLog: {},
      accountList: [], // 银行账户列表
      installmentForm: {
        loanId: null,
        applyAmount: 0,
        periodCount: 1,
        billAmount: '0.00',
        tailAmount: 0,
        repayDay: 1,
        tailPayTime: null,
        accountType: '',
        installmentStatus: 2 // 2-法诉分期
      },
      // 分期表单验证规则
      installmentRules: {
        applyAmount: [
          { required: true, message: '请输入申请分期金额', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '申请分期金额必须大于0', trigger: 'blur' }
        ],
        periodCount: [
          { required: true, message: '请输入分期期数', trigger: 'blur' },
          { type: 'number', min: 1, max: 60, message: '分期期数必须在1-60期之间', trigger: 'blur' }
        ],
        repayDay: [
          { required: true, message: '请选择每期还款日', trigger: 'change' }
        ],
        accountType: [
          { required: true, message: '请选择账号类型', trigger: 'change' }
        ],
        tailPayTime: [
          {
            validator: (rule, value, callback) => {
              if (this.installmentForm.tailAmount > 0 && !value) {
                callback(new Error('有尾款时必须选择尾款支付时间'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ]
      },
      uploadUrl: process.env.VUE_APP_BASE_API + this.action,
      headers: {
        Authorization: 'Bearer ' + getToken(),
      },
      dialogImageUrl: '',
      dialogVisible: false,
    }
  },
  watch: {
    data: {
      handler(newVal) {
        if (newVal) {
          console.log('newVal', newVal)
          this.loanReminder = {
            loanId: newVal.流程序号,
            customerName: newVal.贷款人,
            channel: newVal.出单渠道,
            bank: newVal.放款银行,
            identity: this.$store.state.user.roles[0],
            repaymentStatus: '',
            fundsRepayment: '',
            fundsAmount: '',
            fundsImage: [],
            fundsAccountType: '',
            accountNumber: '',
            urgeStatus: '',
            trackingTime: '',
            urgeDescribe: '',
            status: 2,
          }
          this.litigationLog = {
            loanId: newVal.流程序号,
            litigationId: newVal.序号,
            docName: '',
            docNumber: '',
            docUploadUrl: [],
            docEffectiveDate: '',
            openDate: '',
            status: '',
          }
          // 重置分期表单
          this.installmentForm = {
            loanId: newVal && newVal.流程序号 ? newVal.流程序号 : null,
            applyAmount: 0,
            periodCount: 1,
            billAmount: '0.00',
            tailAmount: 0,
            repayDay: 1,
            tailPayTime: null,
            accountType: '',
            installmentStatus: 2 // 2-法诉分期
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.getAccountList()
  },
  methods: {
    // 获取银行账户列表
    getAccountList() {
      get_bank_account().then(response => {
        this.accountList = response.rows
      })
    },
    // 处理还款类型变化
    handleRepaymentTypeChange(value) {
      if (value === '3') {
        // 选择分期还款时，清空其他还款信息
        this.loanReminder.fundsRepayment = ''
        this.loanReminder.fundsAmount = ''
        this.loanReminder.fundsAccountType = ''
        this.loanReminder.accountNumber = ''
        this.loanReminder.fundsImage = []
      } else {
        // 选择其他还款类型时，清空分期表单验证状态
        this.$nextTick(() => {
          if (this.$refs.installmentFormRef) {
            this.$refs.installmentFormRef.clearValidate()
          }
        })
      }
    },

    // 分期表单计算方法
    handleInstallmentFormChange() {
      const applyAmount = Number(this.installmentForm.applyAmount) || 0
      const periodCount = Number(this.installmentForm.periodCount) || 1
      const tailAmount = Number(this.installmentForm.tailAmount) || 0
      if (applyAmount >= 0 && periodCount >= 1) {
        this.installmentForm.billAmount = ((applyAmount - tailAmount) / periodCount).toFixed(2)
      } else {
        this.installmentForm.billAmount = '0.00'
      }
    },
    // 通用的上传成功处理函数
    handleUploadSuccess(res, file, fileList, formField) {
      const [obj, prop] = formField.split('.')
      this[obj][prop] = fileList
    },
    // 通用的删除处理函数
    handleRemove(file, fileList, formField) {
      const [obj, prop] = formField.split('.')
      this[obj][prop] = fileList
    },
    // 上传失败
    handleUploadError() {
      this.$modal.msgError('上传失败，请重试')
      this.$modal.closeLoading()
    },
    // 图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    /** 提交表单 */
    submitForm() {
      const loanReminderCopy = JSON.parse(JSON.stringify(this.loanReminder))
      const litigationLogCopy = JSON.parse(JSON.stringify(this.litigationLog))
      loanReminderCopy.fundsImage = loanReminderCopy.fundsImage.map(item => item.response).join(',')
      litigationLogCopy.docUploadUrl = litigationLogCopy.docUploadUrl.map(item => item.response).join(',')
      loanReminderCopy.fundsAccountType =
        loanReminderCopy.fundsAccountType === '其他' ? loanReminderCopy.accountNumber : loanReminderCopy.fundsAccountType
      // 将日志描述从 loanReminder 复制到 litigationLog
      litigationLogCopy.urgeDescribe = loanReminderCopy.urgeDescribe

      // 如果选择了分期还款，先提交分期申请
      if (loanReminderCopy.repaymentStatus === '3') {
        // 显示确认对话框
        const confirmMessage = `确认提交分期申请？\n申请金额：${this.installmentForm.applyAmount}元\n分期期数：${this.installmentForm.periodCount}期\n每期金额：${this.installmentForm.billAmount}元`
        this.$confirm(confirmMessage, '确认分期申请', {
          confirmButtonText: '确定提交',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.submitInstallmentApplication().then(() => {
            // 分期申请提交成功后，再提交日志
            this.submitLitigationLogData(loanReminderCopy, litigationLogCopy)
          }).catch(() => {
            this.$modal.msgError('分期申请提交失败')
          })
        }).catch(() => {
          // 用户取消了操作
        })
      } else {
        // 直接提交日志
        this.submitLitigationLogData(loanReminderCopy, litigationLogCopy)
      }
    },

    /** 提交分期申请 */
    submitInstallmentApplication() {
      return new Promise((resolve, reject) => {
        // 使用Element UI表单验证
        this.$refs.installmentFormRef.validate((valid) => {
          if (!valid) {
            this.$message.error('请完善分期申请信息')
            reject()
            return
          }

          // 额外的业务验证
          if (!this.installmentForm.loanId) {
            this.$message.error('贷款ID不能为空，请重新打开表单')
            reject()
            return
          }

          // 验证每期账单金额是否合理
          const billAmount = Number(this.installmentForm.billAmount)
          if (billAmount <= 0) {
            this.$message.error('每期账单金额必须大于0，请检查申请金额和期数')
            reject()
            return
          }

          // 验证尾款逻辑
          if (this.installmentForm.tailAmount > 0 && !this.installmentForm.tailPayTime) {
            this.$message.error('设置了尾款金额时，必须选择尾款支付时间')
            reject()
            return
          }

          console.log('提交分期申请数据：', this.installmentForm)

          // 调用分期申请API（与代偿分期使用相同的API）
          addInstallment_application_audit(this.installmentForm).then(response => {
            if (response.code === 200) {
              this.$modal.msgSuccess('分期申请提交成功')
              resolve()
            } else {
              this.$modal.msgError('分期申请提交失败：' + (response.msg || '未知错误'))
              reject()
            }
          }).catch(error => {
            console.error('分期申请提交失败:', error)
            this.$modal.msgError('分期申请提交失败，请稍后重试')
            reject()
          })
        })
      })
    },

    /** 提交法诉日志数据 */
    submitLitigationLogData(loanReminderCopy, litigationLogCopy) {
      console.log('提交表单数据：', loanReminderCopy)
      console.log('提交表单数据：', litigationLogCopy)
      submitLitigationLog({ loanReminder: loanReminderCopy, litigationLog: litigationLogCopy }).then(res => {
        this.$modal.msgSuccess('提交成功')
        this.visible = false
        this.resetForm()
      })
    },
    /** 取消操作 */
    cancel() {
      this.visible = false
      this.resetForm()
      return
    },
    /** 重置表单 */
    resetForm() {
      this.loanReminder = {
        fundsImage: [],
      }
      this.litigationLog = {
        docUploadUrl: [],
      }
      this.installmentForm = {
        loanId: null,
        applyAmount: 0,
        periodCount: 1,
        billAmount: '0.00',
        tailAmount: 0,
        repayDay: 1,
        tailPayTime: null,
        accountType: '',
        installmentStatus: 2 // 2-法诉分期
      }
      // 重置分期表单验证状态
      this.$nextTick(() => {
        if (this.$refs.installmentFormRef) {
          this.$refs.installmentFormRef.clearValidate()
        }
      })
    },
    /** 统一打开弹窗的方法 */
    openDialog() {
      this.visible = true
      return
    },
    /** 处理文件超出限制 */
    handleExceed(files, fileList) {
      this.$message.warning('只能上传一个文件')
      return
    },
  },
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.el-divider__text {
  padding: 0 15px;
  font-size: 14px;
  color: #606266;
}

.upload-demo {
  width: 100%;
}

.el-upload {
  width: 100%;
}

/* 分期表单容器样式 */
.installment-form-container {
  margin-top: 20px;
  border: 2px solid #409EFF;
  border-radius: 8px;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  overflow: hidden;
}

.installment-form-header {
  background: #409EFF;
  color: white;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.installment-form-header i {
  font-size: 18px;
}

.installment-form {
  padding: 20px;
  background: white;
  margin: 0;
  border: none;
}

/* 分期表单样式 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.2;
}

/* 必填项标识 */
.el-form-item.is-required .el-form-item__label::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

/* 分期表单区域样式 */
.installment-form .el-row {
  margin-bottom: 15px;
}

/* 禁用状态的输入框样式 */
.el-input.is-disabled .el-input__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

/* 其他还款表单样式 */
.el-form:not(.installment-form) .el-row {
  margin-bottom: 10px;
}
</style>
