(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c7b43"],{"526f":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[n("el-form-item",{attrs:{label:"客户编号",prop:"id",fixed:"left"}},[n("el-input",{attrs:{placeholder:"请输入客户编号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.id,callback:function(t){e.$set(e.queryParams,"id",t)},expression:"queryParams.id"}})],1),n("el-form-item",{attrs:{label:"姓名",prop:"customerName"}},[n("el-input",{attrs:{placeholder:"请输入姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),n("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),n("el-row",{staticClass:"mb8",attrs:{gutter:10}},[n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["customer_info:customer_info:export"],expression:"['customer_info:customer_info:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),n("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.customer_infoList},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),n("el-table-column",{attrs:{label:"客户编号",align:"center",prop:"id",fixed:"left"}}),n("el-table-column",{attrs:{label:"姓名",align:"center",prop:"customerName"}}),n("el-table-column",{attrs:{label:"性别",align:"center",prop:"sex"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s("FEMALE"==t.row.sex?"女":"男"))])]}}])}),n("el-table-column",{attrs:{label:"年龄",align:"center",prop:"age"}}),n("el-table-column",{attrs:{label:"出生日期",align:"center",prop:"birthday"}}),n("el-table-column",{attrs:{label:"民族",align:"center",prop:"nation"}}),n("el-table-column",{attrs:{label:"证件类型",align:"center",prop:"certType"}}),n("el-table-column",{attrs:{label:"签发机关",align:"center",prop:"creditissueOrg"}}),n("el-table-column",{attrs:{label:"身份证地址",align:"center",prop:"address"}}),n("el-table-column",{attrs:{label:"有效开始日期",align:"center",prop:"effecteddate"}}),n("el-table-column",{attrs:{label:"有效结束日期",align:"center",prop:"expireddate"}})],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),n("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}}),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),n("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],l=n("5530"),o=(n("d81d"),n("d3b7"),n("0643"),n("a573"),n("b775"));function i(e){return Object(o["a"])({url:"/customer_info/customer_info/list",method:"get",params:e})}function s(e){return Object(o["a"])({url:"/customer_info/customer_info/"+e,method:"get"})}function u(e){return Object(o["a"])({url:"/customer_info/customer_info",method:"post",data:e})}function c(e){return Object(o["a"])({url:"/customer_info/customer_info",method:"put",data:e})}function m(e){return Object(o["a"])({url:"/customer_info/customer_info/"+e,method:"delete"})}var d={name:"Customer_info",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,customer_infoList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,id:null,customerName:null},form:{},rules:{}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,i(this.queryParams).then((function(t){e.customer_infoList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,customerName:null,sex:null,age:null,birthday:null,nation:null,certId:null,certType:null,creditissueOrg:null,address:null,effecteddate:null,expireddate:null,customerType:null,personalType:null,customerStatus:null,validStatus:null,createBy:null,createDate:null,updateBy:null,updateDate:null,delFlag:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加客户信息"},handleUpdate:function(e){var t=this;this.reset();var n=e.id||this.ids;s(n).then((function(e){t.form=e.data,t.open=!0,t.title="修改客户信息"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,n=e.id||this.ids;this.$modal.confirm('是否确认删除客户信息编号为"'+n+'"的数据项？').then((function(){return m(n)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("customer_info/customer_info/export",Object(l["a"])({},this.queryParams),"customer_info_".concat((new Date).getTime(),".xlsx"))}}},p=d,f=n("2877"),h=Object(f["a"])(p,a,r,!1,null,null,null);t["default"]=h.exports}}]);