(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cdf4cd74"],{"1d62":function(e,t,a){"use strict";a.d(t,"e",(function(){return l})),a.d(t,"d",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"b",(function(){return o})),a.d(t,"a",(function(){return s}));var r=a("b775");function l(e){return Object(r["a"])({url:"/trial_balance/trial_balance/list",method:"get",params:e})}function n(){return Object(r["a"])({url:"/vm_car_order/vm_car_order/cate",method:"get"})}function i(e){return Object(r["a"])({url:"/vm_car_order/vm_car_order/"+e,method:"get"})}function o(e){return Object(r["a"])({url:"/vm_car_order/vm_car_order/"+e,method:"delete"})}function s(e){return Object(r["a"])({url:"/car_order/car_order",method:"put",data:e})}},bee4:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"cardID"}},[a("el-input",{attrs:{placeholder:"贷款人身份证号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.cardID,callback:function(t){e.$set(e.queryParams,"cardID",t)},expression:"queryParams.cardID"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"请输入车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"clerk"}},[a("el-input",{attrs:{placeholder:"业务员",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.clerk,callback:function(t){e.$set(e.queryParams,"clerk",t)},expression:"queryParams.clerk"}})],1),a("el-form-item",{attrs:{label:"",prop:"jgName"}},[a("el-cascader",{attrs:{placeholder:"录单渠道名称",options:e.jgNameList,props:{expandTrigger:"hover",label:"name",value:"id"}},on:{change:e.handleChange},model:{value:e.queryParams.jgName,callback:function(t){e.$set(e.queryParams,"jgName",t)},expression:"queryParams.jgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"lendingBank"}},[a("el-select",{attrs:{placeholder:"放款银行",clearable:""},model:{value:e.queryParams.lendingBank,callback:function(t){e.$set(e.queryParams,"lendingBank",t)},expression:"queryParams.lendingBank"}},e._l(e.lendingBankList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"applicant"}},[a("el-input",{attrs:{placeholder:"申请人",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.applicant,callback:function(t){e.$set(e.queryParams,"applicant",t)},expression:"queryParams.applicant"}})],1),a("el-form-item",{attrs:{label:"",prop:"examineStatus"}},[a("el-select",{attrs:{placeholder:"审批状态",clearable:""},model:{value:e.queryParams.examineStatus,callback:function(t){e.$set(e.queryParams,"examineStatus",t)},expression:"queryParams.examineStatus"}},e._l(e.examineList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"申请时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.queryParams.allocationTime,callback:function(t){e.$set(e.queryParams,"allocationTime",t)},expression:"queryParams.allocationTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vm_car_orderList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"customerName"}}),a("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"orgName",width:"130"}}),a("el-table-column",{attrs:{label:"业务员",align:"center",prop:"nickName"}}),a("el-table-column",{attrs:{label:"车牌号",align:"center",prop:"plateNo",width:"130"}}),a("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"bank",width:"130"}}),a("el-table-column",{attrs:{label:"申请人",align:"center",prop:"lendingBank",width:"130"}}),a("el-table-column",{attrs:{label:"逾期天数",align:"center",prop:"boverdueDays",width:"100"}}),a("el-table-column",{attrs:{label:"银行结清金额",align:"center",prop:"btotalMoney",width:"100"}}),a("el-table-column",{attrs:{label:"其他欠款",align:"center",prop:"otherDebt"}}),a("el-table-column",{attrs:{label:"违约金",align:"center",prop:"liquidatedDamages"}}),a("el-table-column",{attrs:{label:"代扣结清金额",align:"center",prop:"dtotalMoney",width:"100"}}),a("el-table-column",{attrs:{label:"总欠金额",align:"center",prop:"totalMoney"}}),a("el-table-column",{attrs:{label:"减免金额",align:"center",prop:"reductionAmount"}}),a("el-table-column",{attrs:{label:"申请日期",align:"center",prop:"keyTime",width:"130"}}),a("el-table-column",{attrs:{label:"审批状态",align:"center",prop:"lendingBank"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"130",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("催记查看")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["trial_balance:trial_balance:examine"],expression:"['trial_balance:trial_balance:examine']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("减免审批")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"邮寄地址"}},[a("el-select",{attrs:{"value-key":"children",placeholder:"请选择省"},on:{change:e.provinceChange},model:{value:e.form.keyProvince,callback:function(t){e.$set(e.form,"keyProvince",t)},expression:"form.keyProvince"}},e._l(e.provinceList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e}})})),1),a("el-select",{staticStyle:{"margin-top":"10px"},attrs:{"value-key":"children",placeholder:"请选择市"},on:{change:e.cityChange},model:{value:e.form.keyCity,callback:function(t){e.$set(e.form,"keyCity",t)},expression:"form.keyCity"}},e._l(e.cityList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e}})})),1),a("el-select",{staticStyle:{"margin-top":"10px"},attrs:{"value-key":"children",placeholder:"请选择区"},on:{change:e.districtChange},model:{value:e.form.keyBorough,callback:function(t){e.$set(e.form,"keyBorough",t)},expression:"form.keyBorough"}},e._l(e.districtList,(function(e){return a("el-option",{key:e.name,attrs:{label:e.name,value:e}})})),1)],1),a("el-form-item",{attrs:{label:"详细地址"}},[a("el-input",{attrs:{placeholder:"请填写详细地址",clearable:""},model:{value:e.form.keyAddress,callback:function(t){e.$set(e.form,"keyAddress",t)},expression:"form.keyAddress"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],n=a("5530"),i=(a("d81d"),a("b0c0"),a("d3b7"),a("0643"),a("a573"),a("1d62")),o=a("cf0d"),s={name:"Vm_car_order",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,vm_car_orderList:[],title:"",open:!1,queryParams:{pageSize:15,customerName:"",plateNo:"",jgName:"",garageId:"",lendingBank:"",examineStatus:"",pageNum:1,cardID:"",clerk:"",applicant:"",allocationTime:"",status:2},jgNameList:[{label:"A公司",value:1},{label:"B公司",value:2}],lendingBankList:[{label:"A银行",value:1},{label:"B银行",value:2}],followUp:[{label:"无法跟进",value:1},{label:"约定还款",value:2},{label:"继续联系",value:3}],examineList:[{label:"待审批",value:1},{label:"贷后审批通过",value:2},{label:"贷后审批驳回",value:3},{label:"运营审批通过",value:4},{label:"运营审批驳回",value:5}],form:{id:"",keyProvince:"",keyCity:"",keyBorough:"",keyAddress:""},rules:{keyProvince:"",keyCity:"",keyBorough:"",keyAddress:""},provinceList:o,cityList:[],districtList:[],revokeList:{id:"",status:4}}},created:function(){this.getTeam(),this.getList()},methods:{handleChange:function(e){this.queryParams.jgName=e},provinceChange:function(e){this.form.keyProvince=e.name,this.cityList=e.children},cityChange:function(e){this.form.keyCity=e.name,this.districtList=e.children},districtChange:function(e){this.form.keyBorough=e.name},getTeam:function(){var e=this;Object(i["d"])().then((function(t){e.teamList=t.team,e.jgNameList=t.office}))},getList:function(){var e=this;this.loading=!0,Object(i["e"])(this.queryParams).then((function(t){e.vm_car_orderList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:"",keyProvince:"",keyCity:"",keyBorough:"",keyAddress:""},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.restSearch(),this.handleQuery()},restSearch:function(){this.queryParams={pageSize:10,customerName:"",plateNo:"",cardID:"",jgName:"",garageId:"",lendingBank:"",examineStatus:"",pageNum:1,clerk:"",applicant:"",allocationTime:""}},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加VIEW"},handleUpdate:function(e){var t=this;this.reset(),this.form.id=e.id,this.form.keyProvince=e.keyProvince,this.form.keyCity=e.keyCity,this.form.keyBorough=e.keyBorough,this.form.keyAddress=e.keyAddress;var a=e.id||this.ids;Object(i["c"])(a).then((function(e){t.open=!0,t.title="邮寄钥匙"}))},submitForm:function(){var e=this;Object(i["a"])(this.form).then((function(t){e.$modal.msgSuccess("提交成功"),e.open=!1,e.getList()}))},handleRevoke:function(e){var t=this;console.log("1111"),this.revokeList.id=e.id;var a={id:e.id,status:4};this.$modal.confirm("是否确认撤销？").then((function(){return Object(i["a"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("撤销成功")})).catch((function(e){console.log(e)}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认撤销编号为"'+a+'"的数据项？').then((function(){return Object(i["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("撤销成功")})).catch((function(){}))},handleExport:function(){this.download("vm_car_order/vm_car_order/export",Object(n["a"])({},this.queryParams),"vm_car_order_".concat((new Date).getTime(),".xlsx"))}}},c=s,u=a("2877"),m=Object(u["a"])(c,r,l,!1,null,null,null);t["default"]=m.exports}}]);