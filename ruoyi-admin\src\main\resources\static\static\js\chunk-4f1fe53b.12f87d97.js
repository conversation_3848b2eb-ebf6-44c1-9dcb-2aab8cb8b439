(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4f1fe53b","chunk-2d0c02c3"],{"0ccb":function(t,e,i){"use strict";var a=i("e330"),n=i("50c4"),o=i("577e"),l=i("1148"),s=i("1d80"),r=a(l),c=a("".slice),u=Math.ceil,d=function(t){return function(e,i,a){var l,d,p=o(s(e)),g=n(i),f=p.length,b=void 0===a?" ":o(a);return g<=f||""===b?p:(l=g-f,d=r(b,u(l/b.length)),d.length>l&&(d=c(d,0,l)),t?p+d:d+p)}};t.exports={start:d(!1),end:d(!0)}},4028:function(t,e,i){"use strict";i("65b5")},"413c":function(t,e,i){"use strict";i.d(e,"f",(function(){return n})),i.d(e,"a",(function(){return o})),i.d(e,"i",(function(){return l})),i.d(e,"d",(function(){return s})),i.d(e,"h",(function(){return r})),i.d(e,"b",(function(){return c})),i.d(e,"c",(function(){return u})),i.d(e,"e",(function(){return d})),i.d(e,"g",(function(){return p}));var a=i("b775");function n(t){return Object(a["a"])({url:"/vw_litigation_case_full/vw_litigation_case_full/list",method:"get",params:t})}function o(t){return Object(a["a"])({url:"/litigation_case/litigation_case",method:"post",data:t})}function l(t){return Object(a["a"])({url:"/litigation_case/litigation_case",method:"put",data:t})}function s(t){return Object(a["a"])({url:"/litigation_case/litigation_case/byLoanId/"+t,method:"get"})}function r(t){return Object(a["a"])({url:"/common/public/insertLoanReminderAndLitigationLog",method:"post",data:t})}function c(t){return Object(a["a"])({url:"/litigation_cost/litigation_cost",method:"post",data:t})}function u(t){return Object(a["a"])({url:"/litigation_cost/litigation_cost/checkLimitedFees/"+t,method:"get"})}function d(t){return Object(a["a"])({url:"/litigation_cost/litigation_cost/summary",method:"post",data:{caseIds:t}})}function p(t){return Object(a["a"])({url:"/litigation_log/litigation_log/list",method:"get",params:t})}},"4d90":function(t,e,i){"use strict";var a=i("23e7"),n=i("0ccb").start,o=i("9a0c");a({target:"String",proto:!0,forced:o},{padStart:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}})},"65b5":function(t,e,i){},"9a0c":function(t,e,i){"use strict";var a=i("342f");t.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(a)},cfa5:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-dialog",{attrs:{title:t.isViewMode?"法诉启动详情":t.dialogTitle,visible:t.dialogVisible,width:"800px"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[i("el-form",{ref:"litigationForm",attrs:{model:t.litigationCase,rules:t.rules,"label-width":"120px","label-position":"left"}},[i("el-descriptions",{attrs:{column:3,border:""}},[i("el-descriptions-item",{attrs:{label:"贷款人"}},[t._v(t._s(t.formData.贷款人||"-"))]),i("el-descriptions-item",{attrs:{label:"出单渠道"}},[t._v(t._s(t.formData.出单渠道||"-"))]),i("el-descriptions-item",{attrs:{label:"放款银行"}},[t._v(t._s(t.formData.放款银行||"-"))]),i("el-descriptions-item",{attrs:{label:"代偿总金额",span:3}},[t._v("￥"+t._s(t.formData.代偿总金额||"0.00"))]),i("el-descriptions-item",{attrs:{label:"总欠款金额"}},[t._v("￥"+t._s(t.formData.总欠款金额||"0.00"))]),i("el-descriptions-item",{attrs:{label:"已还金额"}},[t._v("￥"+t._s(t.formData.已还金额||"0.00"))]),i("el-descriptions-item",{attrs:{label:"剩余金额"}},[t._v("￥"+t._s(t.formData.剩余金额||"0.00"))]),i("el-descriptions-item",{attrs:{label:"银行代偿金额"}},[t._v("￥"+t._s(t.formData.银行代偿金额||"0.00"))]),i("el-descriptions-item",{attrs:{label:"银行催回金额"}},[t._v("￥"+t._s(t.formData.银行催回金额||"0.00"))]),i("el-descriptions-item",{attrs:{label:"银行剩余未还代偿金"}},[t._v("￥"+t._s(t.formData.银行剩余未还代偿金||"0.00"))]),i("el-descriptions-item",{attrs:{label:"代扣金额"}},[t._v("￥"+t._s(t.formData.代扣金额||"0.00"))]),i("el-descriptions-item",{attrs:{label:"代扣催回金额"}},[t._v("￥"+t._s(t.formData.代扣催回金额||"0.00"))]),i("el-descriptions-item",{attrs:{label:"代扣剩余未还代偿金"}},[t._v("￥"+t._s(t.formData.代扣剩余未还代偿金||"0.00"))]),i("el-descriptions-item",{attrs:{label:"违约金"}},[t._v("￥"+t._s(t.formData.违约金||"0.00"))]),i("el-descriptions-item",{attrs:{label:"催回违约金金额"}},[t._v("￥"+t._s(t.formData.催回违约金金额||"0.00"))]),i("el-descriptions-item",{attrs:{label:"剩余未还违约金金额"}},[t._v("￥"+t._s(t.formData.剩余未还违约金金额||"0.00"))]),i("el-descriptions-item",{attrs:{label:"其他欠款"}},[t._v("￥"+t._s(t.formData.其他欠款||"0.00"))]),i("el-descriptions-item",{attrs:{label:"催回其他欠款"}},[t._v("￥"+t._s(t.formData.催回其他欠款||"0.00"))]),i("el-descriptions-item",{attrs:{label:"剩余未还其他欠款"}},[t._v("￥"+t._s(t.formData.剩余未还其他欠款||"0.00"))])],1),i("el-divider",{attrs:{"content-position":"left"}},[t._v("法诉信息")]),t.isViewMode?t._e():i("div",[i("el-form-item",{attrs:{label:"法诉费用"}},[t._l(t.litigationFees,(function(e,a){return i("div",{key:a,staticStyle:{"margin-bottom":"10px"}},[i("el-row",{attrs:{gutter:20,type:"flex",align:"middle"}},[i("el-col",{attrs:{span:8}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择法诉费用类型"},on:{change:function(i){return t.handleLitigationFeeTypeChange(e,a)}},model:{value:e.type,callback:function(i){t.$set(e,"type",i)},expression:"item.type"}},t._l(t.litigationFeeTypeOptions,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value,disabled:t.isTypeDisabled(e.value,a)}})})),1)],1),i("el-col",{attrs:{span:8}},[i("el-input",{attrs:{placeholder:"请输入金额",disabled:""===e.type},on:{input:t.handleLitigationFeeAmountChange},model:{value:e.amount,callback:function(i){t.$set(e,"amount",i)},expression:"item.amount"}},[i("template",{slot:"prepend"},[t._v("￥")])],2)],1),i("el-col",{attrs:{span:4}},[i("el-button",{staticStyle:{width:"54px"},attrs:{type:"danger",icon:"el-icon-delete"},on:{click:function(e){return t.removeLitigationFee(a)}}})],1)],1)],1)})),i("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:t.addLitigationFee}},[t._v("新增法诉费用")])],2),i("el-row",{attrs:{gutter:20}},[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"合计法诉费用",prop:"totalMoney"}},[i("el-input",{attrs:{placeholder:"请输入合计法诉费用",disabled:""},model:{value:t.litigationFee.totalMoney,callback:function(e){t.$set(t.litigationFee,"totalMoney",e)},expression:"litigationFee.totalMoney"}},[i("template",{slot:"prepend"},[t._v("￥")])],2)],1)],1)],1)],1),i("el-row",{attrs:{gutter:20}},[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"案件类型",prop:"litigationType"}},[i("el-radio-group",{attrs:{disabled:t.isViewMode},model:{value:t.litigationCase.litigationType,callback:function(e){t.$set(t.litigationCase,"litigationType",e)},expression:"litigationCase.litigationType"}},[i("el-radio",{attrs:{label:"1"}},[t._v("普通案件")]),i("el-radio",{attrs:{label:"2"}},[t._v("重点案件")])],1)],1)],1)],1),i("el-row",{attrs:{gutter:20}},[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"起诉类型",prop:"prosecutionType"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择起诉类型",disabled:t.isViewMode},model:{value:t.litigationCase.prosecutionType,callback:function(e){t.$set(t.litigationCase,"prosecutionType",e)},expression:"litigationCase.prosecutionType"}},[i("el-option",{attrs:{label:"债转",value:"1"}}),i("el-option",{attrs:{label:"债加",value:"2"}}),i("el-option",{attrs:{label:"担保物权",value:"3"}}),i("el-option",{attrs:{label:"仲裁",value:"4"}}),i("el-option",{attrs:{label:"赋强公证",value:"5"}}),i("el-option",{attrs:{label:"拍状元",value:"6"}}),i("el-option",{attrs:{label:"拍司令",value:"7"}}),i("el-option",{attrs:{label:"属地诉讼",value:"8"}}),i("el-option",{attrs:{label:"余值起诉",value:"9"}}),i("el-option",{attrs:{label:"债权出售",value:"10"}}),i("el-option",{attrs:{label:"签约地诉讼",value:"11"}}),i("el-option",{attrs:{label:"特殊诉讼通道",value:"12"}})],1)],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"起诉内容",prop:"prosecutionContent"}},[t.isViewMode?i("div",t._l(t.selectedProsecutionContent,(function(e){return i("el-tag",{key:e,staticStyle:{"margin-right":"8px","margin-bottom":"4px"}},[t._v(" "+t._s(t.getProsecutionContentLabel(e))+" ")])})),1):i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择起诉内容",multiple:"","collapse-tags":"",clearable:""},on:{focus:t.handleSelectFocus,"visible-change":t.handleSelectVisibleChange},model:{value:t.selectedProsecutionContent,callback:function(e){t.selectedProsecutionContent=e},expression:"selectedProsecutionContent"}},t._l(t.availableProsecutionContentOptions,(function(t){return i("el-option",{key:t.value,attrs:{label:t.label,value:t.value,disabled:t.disabled}})})),1)],1)],1)],1),i("el-row",{attrs:{gutter:20}},[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"起诉金额",prop:"prosecutionAmount"}},[i("el-input",{attrs:{placeholder:t.isViewMode?"":"起诉金额将根据起诉内容自动计算",disabled:""},model:{value:t.litigationCase.prosecutionAmount,callback:function(e){t.$set(t.litigationCase,"prosecutionAmount",e)},expression:"litigationCase.prosecutionAmount"}},[i("template",{slot:"prepend"},[t._v("￥")])],2)],1)],1)],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:t.handleCancel}},[t._v(t._s(t.isViewMode?"关闭":"取消"))]),t.isViewMode?t._e():i("el-button",{attrs:{type:"primary"},on:{click:t.handleConfirm}},[t._v("确认")])],1)],1)},n=[],o=i("2909"),l=i("c14f"),s=i("1da1"),r=i("5530"),c=(i("99af"),i("4de4"),i("7db0"),i("caad"),i("d81d"),i("14d9"),i("a434"),i("e9c4"),i("a9e3"),i("b680"),i("b64b"),i("d3b7"),i("6062"),i("1e70"),i("79a4"),i("c1a1"),i("8b00"),i("a4e7"),i("1e5a"),i("72c3"),i("2532"),i("3ca3"),i("4d90"),i("0643"),i("2382"),i("fffc"),i("4e3e"),i("a573"),i("9d4a"),i("9a9a"),i("159b"),i("ddb0"),i("413c")),u=i("bd52"),d={name:"LitigationForm",props:{data:{type:Object,default:function(){return{}}}},data:function(){return{dialogTitle:"启动法诉",dialogVisible:!1,isViewMode:!1,formData:{},litigationFee:{totalMoney:0},litigationCase:{},litigationFees:[],litigationFeeTypeOptions:[{label:"律师费",value:"lawyerFee"},{label:"法诉费",value:"litigationFee"},{label:"保全费",value:"preservationFee"},{label:"布控费",value:"surveillanceFee"},{label:"公告费",value:"announcementFee"},{label:"评估费",value:"appraisalFee"},{label:"执行费",value:"executionFee"},{label:"日常报销",value:"dailyReimbursement"},{label:"违约金",value:"penalty"},{label:"担保费",value:"guaranteeFee"},{label:"居间费",value:"intermediaryFee"},{label:"代偿金",value:"compensity"},{label:"其他欠款",value:"otherAmountsOwed"},{label:"保险费",value:"insurance"},{label:"特殊通道费",value:"specialAccessFees"}],prosecutionContentOptions:[{label:"银行代偿金额",value:"1",amountFields:["银行剩余未还代偿金"]},{label:"代扣金额",value:"2",amountFields:["代扣剩余未还代偿金"]},{label:"违约金",value:"3",amountFields:["剩余未还违约金金额"]},{label:"其他欠款",value:"4",amountFields:["剩余未还其他欠款"]}],usedProsecutionContents:[],loanDetail:{},rules:{litigationType:[{required:!0,message:"请选择案件类型",trigger:"change"}],prosecutionType:[{required:!0,message:"请选择起诉类型",trigger:"change"}],prosecutionContent:[{required:!0,type:"array",min:1,message:"请选择起诉内容",trigger:"change"}]}}},computed:{availableProsecutionContentOptions:function(){var t=this;return this.prosecutionContentOptions.map((function(e){var i=!1;return t.usedProsecutionContents.forEach((function(t){i=Array.isArray(t)?i||t.includes(e.value):i||t===e.value})),Object(r["a"])(Object(r["a"])({},e),{},{disabled:i})}))},selectedProsecutionContent:{get:function(){return this.litigationCase.prosecutionContent||[]},set:function(t){var e=this;this.$set(this.litigationCase,"prosecutionContent",t),this.handleProsecutionContentChange(t),this.$nextTick((function(){e.$refs.litigationForm&&e.$refs.litigationForm.validateField("prosecutionContent")}))}}},watch:{data:{handler:function(t){t&&(this.formData=t,this.litigationFee.litigationCaseId=t.序号,this.$set(this.litigationCase,"id",t.序号),this.$set(this.litigationCase,"loanId",t.流程序号),this.$set(this.litigationCase,"prosecutionContent",[]),this.$set(this.litigationCase,"prosecutionAmount",""),this.loadExistingLitigations(),this.loadLoanDetail())},immediate:!0,deep:!0}},methods:{loadExistingLitigations:function(){var t=this;return Object(s["a"])(Object(l["a"])().m((function e(){var i,a;return Object(l["a"])().w((function(e){while(1)switch(e.p=e.n){case 0:if(t.litigationCase.loanId){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,Object(c["d"])(t.litigationCase.loanId);case 2:i=e.v,200===i.code&&i.data&&(a=i.data.find((function(e){return e.id===t.litigationCase.id})),a&&t.isLitigationStarted(a)&&(t.isViewMode=!0,t.loadCaseDataForView(a)),t.usedProsecutionContents=i.data.filter((function(e){return e.id!==t.litigationCase.id&&t.isLitigationStarted(e)})).map((function(t){var e=t.prosecutionContent;if(!e)return null;try{return JSON.parse(e)}catch(i){return e}})).filter(Boolean)),e.n=4;break;case 3:e.p=3,e.v;case 4:return e.a(2)}}),e,null,[[1,3]])})))()},isLitigationStarted:function(t){var e=null!=t.litigationType&&""!==t.litigationType,i=null!=t.prosecutionType&&""!==t.prosecutionType,a=null!=t.prosecutionContent&&""!==t.prosecutionContent,n=null!=t.litigationStartDay&&""!==t.litigationStartDay;return e&&i&&a&&n},loadCaseDataForView:function(t){this.$set(this.litigationCase,"litigationType",t.litigationType),this.$set(this.litigationCase,"prosecutionType",t.prosecutionType),this.$set(this.litigationCase,"prosecutionAmount",t.prosecutionAmount);var e=t.prosecutionContent;if("string"===typeof e)try{e=JSON.parse(e)}catch(i){e=[e]}this.$set(this.litigationCase,"prosecutionContent",e||[])},loadLoanDetail:function(){var t=this;return Object(s["a"])(Object(l["a"])().m((function e(){var i;return Object(l["a"])().w((function(e){while(1)switch(e.p=e.n){case 0:if(t.litigationCase.loanId){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,Object(u["h"])(t.litigationCase.loanId);case 2:i=e.v,200===i.code&&i.data&&(t.loanDetail=i.data),e.n=4;break;case 3:e.p=3,e.v;case 4:return e.a(2)}}),e,null,[[1,3]])})))()},handleSelectFocus:function(){},handleSelectVisibleChange:function(){},getProsecutionContentLabel:function(t){var e=this.prosecutionContentOptions.find((function(e){return e.value===t}));return e?e.label:t},handleProsecutionContentChange:function(t){var e=this;if(t&&Array.isArray(t)&&0!==t.length){var i=[];t.forEach((function(t){var a=e.prosecutionContentOptions.find((function(e){return e.value===t}));a&&(i=i.concat(a.amountFields))}));var a=Object(o["a"])(new Set(i)),n=0;a.forEach((function(t){var i=e.formData[t]||0;n+=Number(i)})),this.$set(this.litigationCase,"prosecutionAmount",n.toFixed(2))}else this.$set(this.litigationCase,"prosecutionAmount","")},addLitigationFee:function(){this.litigationFees.push({type:"",amount:""})},isTypeDisabled:function(t,e){return this.litigationFees.some((function(i,a){return i.type===t&&a!==e}))},handleLitigationFeeTypeChange:function(t,e){var i=this.litigationFees.some((function(i,a){return i.type===t.type&&a!==e}));i&&(this.$message.warning("该费用类型已选择，请勿重复！"),this.$set(this.litigationFees[e],"type",""))},handleLitigationFeeAmountChange:function(){this.litigationFee.totalMoney=this.litigationFees.reduce((function(t,e){return t+Number(e.amount)}),0)},removeLitigationFee:function(t){this.litigationFees.splice(t,1),this.handleLitigationFeeAmountChange()},open:function(){this.dialogVisible=!0,Array.isArray(this.litigationCase.prosecutionContent)||this.$set(this.litigationCase,"prosecutionContent",[])},openView:function(t){if(this.isViewMode=!0,this.dialogVisible=!0,t){this.formData=t,this.$set(this.litigationCase,"litigationType",t.litigationType),this.$set(this.litigationCase,"prosecutionType",t.prosecutionType),this.$set(this.litigationCase,"prosecutionAmount",t.prosecutionAmount);var e=t.prosecutionContent;if("string"===typeof e)try{e=JSON.parse(e)}catch(i){e=[e]}this.$set(this.litigationCase,"prosecutionContent",e||[])}},handleClose:function(){this.dialogVisible=!1,this.reset()},handleCancel:function(){this.dialogVisible=!1,this.reset()},reset:function(){this.isViewMode=!1,this.litigationFee={totalMoney:0},this.$set(this,"litigationCase",{prosecutionContent:[],prosecutionAmount:""}),this.litigationFees=[],this.usedProsecutionContents=[],this.loanDetail={}},handleConfirm:function(){var t=this;this.$refs.litigationForm.validate((function(e){if(!e)return t.$message.error("请完善必填信息"),!1;var i=Object(r["a"])({},t.litigationCase),a=t.litigationFee;Array.isArray(i.prosecutionContent)&&(i.prosecutionContent=JSON.stringify(i.prosecutionContent));var n=new Date,o=n.getFullYear(),l=String(n.getMonth()+1).padStart(2,"0"),s=String(n.getDate()).padStart(2,"0"),u="".concat(o,"-").concat(l,"-").concat(s);i.litigationStartDay=u,i.litigationStartDate=u,t.litigationFees.forEach((function(t){var e=t.type,i=t.amount;e&&(a[e]=i||0)})),i.litigationType=Number(i.litigationType),Object(c["i"])(i).then((function(e){200===e.code?Object(c["b"])(a).then((function(e){200===e.code?(t.$message.success("启动法诉提交成功"),t.isViewMode=!0):t.$message.error("启动法诉提交失败")})):t.$message.error("启动法诉提交失败")}))}))}}},p=d,g=(i("4028"),i("2877")),f=Object(g["a"])(p,a,n,!1,null,"795ee1ea",null);e["default"]=f.exports}}]);