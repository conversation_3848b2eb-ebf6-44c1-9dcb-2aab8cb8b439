(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3f22bf85"],{"0f5f":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.carInfo.plateNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车架号"}},[e._v(e._s(e.carInfo.identityNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"发动机号"}},[e._v(e._s(e.carInfo.engineNumber||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(e._s(e.carStatusLabel))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆位置"}},[e._v(e._s(e.carInfo.carAddress||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(e._s(e.gpsStatusLabel))]):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看车牌照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"行驶证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看行驶证照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"登记证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看登记证照片")])],1):e._e()],1)],1)},r=[],s=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),a("bd52")),n={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var e=this,t=this.carStatusList.find((function(t){return t.value===e.carInfo.carStatus}));return t?t.label:"未知状态"},gpsStatusLabel:function(){var e=this,t=this.gpsStatusList.find((function(t){return t.value===e.carInfo.gpsStatus}));return t?t.label:"未知状态"}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,e&&this.plateNo&&Object(s["e"])(this.plateNo).then((function(e){t.carInfo=e.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},o=n,i=a("2877"),u=Object(i["a"])(o,l,r,!1,null,null,null);t["a"]=u.exports},1791:function(e,t,a){},"2eca":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[e._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},r=[],s=a("bd52"),n={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(s["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},o=n,i=(a("d6fd"),a("2877")),u=Object(i["a"])(o,l,r,!1,null,"8a3d4978",null);t["a"]=u.exports},5752:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"certId"}},[a("el-input",{attrs:{placeholder:"贷款人身份证号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.certId,callback:function(t){e.$set(e.queryParams,"certId",t)},expression:"queryParams.certId"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"salesman"}},[a("el-input",{attrs:{placeholder:"业务员姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.salesman,callback:function(t){e.$set(e.queryParams,"salesman",t)},expression:"queryParams.salesman"}})],1),a("el-form-item",{attrs:{label:"",prop:"partnerId"}},[a("el-select",{attrs:{placeholder:"放款银行",clearable:""},model:{value:e.queryParams.partnerId,callback:function(t){e.$set(e.queryParams,"partnerId",t)},expression:"queryParams.partnerId"}},e._l(e.bankList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"followStatus"}},[a("el-select",{attrs:{placeholder:"跟催类型",clearable:""},model:{value:e.queryParams.followStatus,callback:function(t){e.$set(e.queryParams,"followStatus",t)},expression:"queryParams.followStatus"}},e._l(e.followUpList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"还款日"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.queryParams.allocationTime,callback:function(t){e.$set(e.queryParams,"allocationTime",t)},expression:"queryParams.allocationTime"}})],1),a("el-form-item",{attrs:{label:"",prop:"isFindCar"}},[a("el-select",{attrs:{placeholder:"是否派单找车",clearable:""},model:{value:e.queryParams.isFindCar,callback:function(t){e.$set(e.queryParams,"isFindCar",t)},expression:"queryParams.isFindCar"}},[a("el-option",{attrs:{label:"未派单",value:0}}),a("el-option",{attrs:{label:"已派单",value:1}})],1)],1),a("el-form-item",{attrs:{label:"",prop:"carStatus"}},[a("el-select",{attrs:{placeholder:"车辆状态",clearable:""},model:{value:e.queryParams.carStatus,callback:function(t){e.$set(e.queryParams,"carStatus",t)},expression:"queryParams.carStatus"}},e._l(e.carStatusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vw_account_loanList}},[a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"逾期状态",align:"center",prop:"slippageStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(" "+e._s(1==t.row.slippageStatus?"提醒":2==t.row.slippageStatus?"电催":3==t.row.slippageStatus?"上访":4==t.row.slippageStatus?"逾期30-60":"逾期60+")+" ")])]}}])}),a("el-table-column",{attrs:{label:"还款状态",align:"center",prop:"repaymentStatus",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(" "+e._s(1==t.row.repaymentStatus?"还款中":2==t.row.repaymentStatus?"已完结":3==t.row.repaymentStatus?"提前结清":4==t.row.repaymentStatus?"逾期催回结清":5==t.row.repaymentStatus?"逾期减免结清":6==t.row.repaymentStatus?"逾期未还款":7==t.row.repaymentStatus?"逾期还款中":8==t.row.repaymentStatus?"代偿未还款":9==t.row.repaymentStatus?"代偿还款中":10==t.row.repaymentStatus?"代偿减免结清":11==t.row.repaymentStatus?"代偿全额结清":"未知状态")+" ")])]}}])}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"customerName"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openUserInfo({customerId:t.row.customerId,applyId:t.row.applyId})}}},[e._v(" "+e._s(t.row.customerName)+" ")])]}}])}),a("el-table-column",{attrs:{label:"业务员",align:"center",prop:"nickName"}}),a("el-table-column",{attrs:{label:"车牌号码",align:"center",prop:"plateNo"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openCarInfo(t.row.plateNo)}}},[e._v(e._s(t.row.plateNo))])]}}])}),a("el-table-column",{attrs:{label:"车辆状态",align:"center",prop:"carStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.carStatus?a("span",[e._v(" "+e._s((e.carStatusList.find((function(e){return e.value===String(t.row.carStatus)}))||{}).label||"")+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"派车团队",align:"center",prop:"carTeamName"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.carTeamName?a("span",[e._v(e._s(t.row.carTeamName))]):e._e()]}}])}),a("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"orgName"}}),a("el-table-column",{attrs:{label:"银行逾期天数",align:"center",prop:"boverdueDays",width:"130"}}),a("el-table-column",{attrs:{label:"首期逾期金额",align:"center",prop:"foverdueAmount",width:"130"}}),a("el-table-column",{attrs:{label:"银行逾期金额",align:"center",prop:"boverdueAmount",width:"130"}}),a("el-table-column",{attrs:{label:"代扣逾期金额",align:"center",prop:"doverdueAmount",width:"130"}}),a("el-table-column",{attrs:{label:"催记类型",align:"center",prop:"urgeType"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.followStatus?a("span",[e._v(" "+e._s(1==t.row.followStatus?"继续联系":2==t.row.followStatus?"约定还款":"无法跟进")+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"催记时间",align:"center",prop:"assignTime",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.loanReminder&&t.row.loanReminder.createTime?a("span",[e._v(" "+e._s(e.parseTime(t.row.loanReminder.createTime,"{y}-{m}-{d}"))+" ")]):a("span")]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_account_loan:vw_account_loan:edit"],expression:"['vw_account_loan:vw_account_loan:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.logView(t.row)}}},[e._v("查看催记")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("loan-reminder-log",{ref:"loanReminderLog",attrs:{"loan-id":e.currentRow.loanId}}),a("userInfo",{ref:"userInfo",attrs:{visible:e.userInfoVisible,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.userInfoVisible=t}}}),a("carInfo",{ref:"carInfo",attrs:{visible:e.carInfoVisible,title:"车辆信息",plateNo:e.plateNo,permission:"2"},on:{"update:visible":function(t){e.carInfoVisible=t}}})],1)},r=[],s=a("ade3"),n=a("a72d"),o=a("2eca"),i=a("0f5f"),u=a("7954"),c={name:"Vw_account_loan",components:{userInfo:o["a"],carInfo:i["a"],LoanReminderLog:u["a"]},data:function(){return{customerInfo:{customerId:"",applyId:""},userInfoVisible:!1,plateNo:"",carInfoVisible:!1,currentRow:{},loading:!0,showSearch:!0,total:0,vw_account_loanList:[],title:"",queryParams:Object(s["a"])(Object(s["a"])(Object(s["a"])({pageNum:1,pageSize:15,customerName:null,certId:null,plateNo:null,salesman:null,partnerId:null,slippageStatus:null,followStatus:null,allocationTime:null,startTime:null,endTime:null},"slippageStatus",1),"isFindCar",null),"carStatus",null),bankList:[{value:"EO00000010",label:"苏银金租"},{value:"IO00000006",label:"浙商银行"},{value:"IO00000007",label:"中关村银行"},{value:"IO00000008",label:"蓝海银行"},{value:"IO00000009",label:"华瑞银行"},{value:"IO00000010",label:"皖新租赁"}],jgNameList:[{label:"A公司",value:1},{label:"B公司",value:2}],slippageList:[{label:"提醒",value:1},{label:"电催",value:2},{label:"上访",value:3},{label:"逾期30-60",value:4},{label:"逾期60+",value:5}],followUpList:[{label:"无法跟进",value:1},{label:"约定还款",value:2},{label:"继续联系",value:3}],form:{},rules:{},logDetail:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],repaymentList:[{label:"还款中",value:1},{label:"已完结",value:2},{label:"提前结清",value:3},{label:"逾期催回结清",value:4},{label:"逾期减免结清",value:5},{label:"逾期未还款",value:6},{label:"逾期还款中",value:7},{label:"代偿未还款",value:8},{label:"代偿还款中",value:9},{label:"代偿减免结清",value:10},{label:"代偿全额结清",value:11}],accountList:[]}},created:function(){this.getList()},methods:{openUserInfo:function(e){this.customerInfo=e,this.userInfoVisible=!0},openCarInfo:function(e){this.plateNo=e,this.carInfoVisible=!0},logView:function(e){this.currentRow=e,this.$refs.loanReminderLog.openLogDialog()},getList:function(){var e=this;this.loading=!0,Object(n["a"])(this.queryParams).then((function(t){e.vw_account_loanList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.reset()},reset:function(){this.form={id:null},this.resetForm("form")},handleQuery:function(){this.queryParams.allocationTime&&(this.queryParams.startTime=this.queryParams.allocationTime[0],this.queryParams.endTime=this.queryParams.allocationTime[1]),this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.customerName=null,this.queryParams.certId=null,this.queryParams.plateNo=null,this.queryParams.salesman=null,this.queryParams.partnerId=null,this.queryParams.slippageStatus=1,this.queryParams.followStatus=null,this.queryParams.allocationTime=null,this.queryParams.startTime=null,this.queryParams.endTime=null,this.queryParams.isFindCar=null,this.queryParams.carStatus=null,this.handleQuery()},submitForm:function(){}}},p=c,m=a("2877"),d=Object(m["a"])(p,l,r,!1,null,null,null);t["default"]=d.exports},a72d:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"d",(function(){return s})),a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return o}));var l=a("b775");function r(e){return Object(l["a"])({url:"/vw_account_loan/vw_account_loan/list",method:"get",params:e})}function s(e){return Object(l["a"])({url:"/loan_reminder/loan_reminder/list",method:"get",params:e})}function n(e){return Object(l["a"])({url:"/loan_reminder/loan_reminder/list/approve",method:"get",params:e})}function o(e){return Object(l["a"])({url:"/loan_reminder/loan_reminder/approve",method:"put",data:e})}},d6fd:function(e,t,a){"use strict";a("1791")}}]);