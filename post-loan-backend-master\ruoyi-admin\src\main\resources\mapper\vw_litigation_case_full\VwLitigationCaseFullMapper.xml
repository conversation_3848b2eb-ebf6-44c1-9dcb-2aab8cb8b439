<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vw_litigation_case_full.mapper.VwLitigationCaseFullMapper">
    
    <resultMap type="VwLitigationCaseFull" id="VwLitigationCaseFullResult">
        <result property="序号"    column="序号"    />
        <result property="流程序号"    column="流程序号"    />
        <result property="流程序号"    column="流程序号"    />
        <result property="法诉文员"    column="法诉文员"    />
        <result property="发起法诉日"    column="发起法诉日"    />
        <result property="状态更新日"    column="状态更新日"    />
        <result property="法诉状态"    column="法诉状态"    />
        <result property="跟催员"    column="跟催员"    />
        <result property="总欠款金额"    column="总欠款金额"    />
        <result property="已还金额"    column="已还金额"    />
        <result property="剩余金额"    column="剩余金额"    />
        <result property="贷款人"    column="贷款人"    />
        <result property="客户ID"    column="客户ID"    />
        <result property="身份证"    column="身份证"    />
        <result property="出单渠道"    column="出单渠道"    />
        <result property="地区"    column="地区"    />
        <result property="车辆牌号"    column="车辆牌号"    />
        <result property="放款银行"    column="放款银行"    />
        <result property="代偿总金额"    column="代偿总金额"    />
        <result property="代偿证明发出日"    column="代偿证明发出日"    />
        <result property="法院地"    column="法院地"    />
        <result property="诉讼法院"    column="诉讼法院"    />
        <result property="前调号"    column="前调号"    />
        <result property="前调号出具时间"    column="前调号出具时间"    />
        <result property="民初号"    column="民初号"    />
        <result property="民初号时间"    column="民初号时间"    />
        <result property="开庭时间"    column="开庭时间"    />
        <result property="执行号"    column="执行号"    />
        <result property="申请编号"    column="申请编号"    />
        <result property="找车团队ID"    column="找车团队ID"    />
        <result property="找车团队"    column="找车团队"    />
        <result property="车辆状态"    column="车辆状态"    />
        <result property="订单状态"    column="订单状态"    />
        <result property="日志内容"    column="日志内容"    />
        <result property="日志时间"    column="日志时间"    />
        <result property="日志操作人"    column="日志操作人"    />
        <result property="催记类型"    column="催记类型"    />
        <result property="日志类型"    column="日志类型"    />
        <result property="日志更新日"    column="日志更新日"    />
        <result property="还款状态"    column="还款状态"    />
        <result property="审批状态"    column="审批状态"    />
        <result property="催回金额"    column="催回金额"    />
        <result property="约定时间"    column="约定时间"    />
        <result property="下次跟踪时间"    column="下次跟踪时间"    />
        <result property="催回总金额"    column="催回总金额"    />
        <result property="业务员"    column="业务员"    />
        <!-- 新增的金额字段 -->
        <result property="银行代偿金额"    column="银行代偿金额"    />
        <result property="银行催回金额"    column="银行催回金额"    />
        <result property="银行剩余未还代偿金"    column="银行剩余未还代偿金"    />
        <result property="代扣金额"    column="代扣金额"    />
        <result property="代扣催回金额"    column="代扣催回金额"    />
        <result property="代扣剩余未还代偿金"    column="代扣剩余未还代偿金"    />
        <result property="违约金"    column="违约金"    />
        <result property="催回违约金金额"    column="催回违约金金额"    />
        <result property="剩余未还违约金金额"    column="剩余未还违约金金额"    />
        <result property="其他欠款"    column="其他欠款"    />
        <result property="催回其他欠款"    column="催回其他欠款"    />
        <result property="剩余未还其他欠款"    column="剩余未还其他欠款"    />
        <!-- 法诉案件信息字段 -->
        <result property="案件类型"    column="案件类型"    />
        <result property="起诉类型"    column="起诉类型"    />
        <result property="起诉内容"    column="起诉内容"    />
        <result property="起诉金额"    column="起诉金额"    />
        <result property="案件启动日"    column="案件启动日"    />
        <result property="法诉子状态"    column="法诉子状态"    />
        <result property="执行保全号"    column="执行保全号"    />
        <result property="案件负责人"    column="案件负责人"    />
    </resultMap>

    <sql id="selectVwLitigationCaseFullVo">
        SELECT * FROM vw_litigation_case_full
    </sql>

    <select id="selectVwLitigationCaseFullList" parameterType="VwLitigationCaseFull" resultMap="VwLitigationCaseFullResult">
        <include refid="selectVwLitigationCaseFullVo"/>
        <where>
            <if test="序号 != null "> and `序号` = #{序号}</if>
            <if test="流程序号 != null"> and `流程序号` = #{流程序号}</if>
            <if test="法诉文员 != null  and 法诉文员 != ''"> and `法诉文员` like concat('%', #{法诉文员}, '%')</if>
            <if test="发起法诉日 != null "> and `发起法诉日` = #{发起法诉日}</if>
            <if test="litigationStartDate != null and litigationStartDate != ''"> and DATE(`发起法诉日`) &gt;= #{litigationStartDate}</if>
            <if test="litigationEndDate != null and litigationEndDate != ''"> and DATE(`发起法诉日`) &lt;= #{litigationEndDate}</if>
            <if test="状态更新日 != null "> and `状态更新日` = #{状态更新日}</if>
            <if test="法诉子状态 != null  and 法诉子状态 != ''"> and `法诉子状态` like concat('%', #{法诉子状态}, '%')</if>
            <if test="跟催员 != null  and 跟催员 != ''"> and `跟催员` = #{跟催员}</if>
            <if test="总欠款金额 != null "> and `总欠款金额` = #{总欠款金额}</if>
            <if test="已还金额 != null "> and `已还金额` = #{已还金额}</if>
            <if test="剩余金额 != null "> and `剩余金额` = #{剩余金额}</if>
            <if test="贷款人 != null  and 贷款人 != ''"> and `贷款人` like concat('%', #{贷款人}, '%')</if>
            <if test="身份证 != null  and 身份证 != ''"> and `身份证` like concat('%', #{身份证}, '%')</if>
            <if test="出单渠道 != null  and 出单渠道 != ''"> and `出单渠道` like concat('%', #{出单渠道}, '%')</if>
            <if test="地区 != null  and 地区 != ''"> and `地区` = #{地区}</if>
            <if test="车辆牌号 != null  and 车辆牌号 != ''"> and `车辆牌号` like concat('%', #{车辆牌号}, '%')</if>
            <if test="放款银行 != null  and 放款银行 != ''"> and `放款银行` = #{放款银行}</if>
            <if test="代偿总金额 != null "> and `代偿总金额` = #{代偿总金额}</if>
            <if test="代偿证明发出日 != null "> and `代偿证明发出日` = #{代偿证明发出日}</if>
            <if test="法院地 != null  and 法院地 != ''"> and `法院地` = #{法院地}</if>
            <if test="诉讼法院 != null  and 诉讼法院 != ''"> and `诉讼法院` = #{诉讼法院}</if>
            <if test="前调号 != null  and 前调号 != ''"> and `前调号` = #{前调号}</if>
            <if test="前调号出具时间 != null "> and `前调号出具时间` = #{前调号出具时间}</if>
            <if test="民初号 != null  and 民初号 != ''"> and `民初号` = #{民初号}</if>
            <if test="民初号时间 != null "> and `民初号时间` = #{民初号时间}</if>
            <if test="开庭时间 != null "> and `开庭时间` = #{开庭时间}</if>
            <if test="执行号 != null  and 执行号 != ''"> and `执行号` = #{执行号}</if>
            <if test="申请编号 != null  and 申请编号 != ''"> and `申请编号` = #{申请编号}</if>
            <if test="找车团队ID != null "> and `找车团队ID` = #{找车团队ID}</if>
            <if test="找车团队 != null  and 找车团队 != ''"> and `找车团队` = #{找车团队}</if>
            <if test="车辆状态 != null  and 车辆状态 != ''"> and `车辆状态` = #{车辆状态}</if>
            <if test="订单状态 != null "> and `订单状态` = #{订单状态}</if>
            <if test="业务员 != null  and 业务员 != ''"> and `业务员` = #{业务员}</if>
            <if test="案件负责人 != null  and 案件负责人 != ''"> and `案件负责人` like concat('%', #{案件负责人}, '%')</if>
        </where>
    </select>
    
    <select id="selectVwLitigationCaseFullBy序号" parameterType="Long" resultMap="VwLitigationCaseFullResult">
        <include refid="selectVwLitigationCaseFullVo"/>
        where `序号` = #{序号}
    </select>

    <!--
    注释掉insert、update、delete操作，因为这是一个复合查询视图，不支持直接的增删改操作
    如需修改数据，请直接操作对应的基础表：litigation_case、loan_list、account_loan等

    <insert id="insertVwLitigationCaseFull" parameterType="VwLitigationCaseFull">
        ...
    </insert>

    <update id="updateVwLitigationCaseFull" parameterType="VwLitigationCaseFull">
        ...
    </update>

    <delete id="deleteVwLitigationCaseFullBy序号" parameterType="Long">
        ...
    </delete>

    <delete id="deleteVwLitigationCaseFullBy序号s" parameterType="String">
        ...
    </delete>
    -->
</mapper>