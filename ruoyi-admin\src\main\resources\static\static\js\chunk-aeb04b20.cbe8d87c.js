(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-aeb04b20","chunk-2d0c02c3"],{"0c80":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:t.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.customerName,callback:function(e){t.$set(t.queryParams,"customerName",e)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"certId"}},[a("el-input",{attrs:{placeholder:"贷款人身份证号",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.certId,callback:function(e){t.$set(t.queryParams,"certId",e)},expression:"queryParams.certId"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"车牌号码",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.plateNo,callback:function(e){t.$set(t.queryParams,"plateNo",e)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"salesman"}},[a("el-input",{attrs:{placeholder:"业务员姓名",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.salesman,callback:function(e){t.$set(t.queryParams,"salesman",e)},expression:"queryParams.salesman"}})],1),a("el-form-item",{attrs:{label:"",prop:"orgName"}},[a("el-input",{attrs:{placeholder:"录单渠道名称",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.orgName,callback:function(e){t.$set(t.queryParams,"orgName",e)},expression:"queryParams.orgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"bank"}},[a("el-select",{attrs:{placeholder:"放款银行",clearable:""},model:{value:t.queryParams.bank,callback:function(e){t.$set(t.queryParams,"bank",e)},expression:"queryParams.bank"}},t._l(t.lendingBankList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"status"}},[a("el-select",{attrs:{placeholder:"选择代偿还款状态",clearable:""},model:{value:t.queryParams.status,callback:function(e){t.$set(t.queryParams,"status",e)},expression:"queryParams.status"}},t._l(t.statusList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"createBy"}},[a("el-input",{attrs:{placeholder:"跟催员",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.createBy,callback:function(e){t.$set(t.queryParams,"createBy",e)},expression:"queryParams.createBy"}})],1),a("el-form-item",{attrs:{label:"",prop:"followUpType"}},[a("el-select",{attrs:{placeholder:"跟催类型",clearable:""},model:{value:t.queryParams.followUpType,callback:function(e){t.$set(t.queryParams,"followUpType",e)},expression:"queryParams.followUpType"}},t._l(t.followUpList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:t.handleQuery}},[t._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:t.resetQuery}},[t._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.vw_loan_compensationList},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"代偿还款状态",align:"center",prop:"status"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s((t.statusList.find((function(t){return t.value===e.row.status}))||{}).label||"")+" ")]}}])}),a("el-table-column",{attrs:{label:"跟催员",align:"center",prop:"createBy"}}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"customerName"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.openUserInfo({customerId:e.row.customerId,applyId:e.row.applyId})}}},[t._v(" "+t._s(e.row.customerName)+" ")])]}}])}),a("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"orgName"}}),a("el-table-column",{attrs:{label:"业务员",align:"center",prop:"salesman"}}),a("el-table-column",{attrs:{label:"车牌号",align:"center",prop:"plateNo",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.openCarInfo(e.row.plateNo)}}},[t._v(t._s(e.row.plateNo))])]}}])}),a("el-table-column",{attrs:{label:"车辆位置",align:"center",prop:"carDetailAddress"}}),a("el-table-column",{attrs:{label:"车辆状态",align:"center",prop:"carStatus",width:"130"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s((t.carStatusList.find((function(t){return t.value===String(e.row.carStatus)}))||{}).label||"")+" ")]}}])}),a("el-table-column",{attrs:{label:"GPS状态",align:"center",prop:"gpsStatus"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s((t.gpsStatusList.find((function(t){return t.value===String(e.row.gpsStatus)}))||{}).label||"")+" ")]}}])}),a("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"bank"}}),a("el-table-column",{attrs:{label:"总欠金额",align:"center",prop:"loanAmount"}}),a("el-table-column",{attrs:{label:"已还金额",align:"center",prop:"otherDebt"}}),a("el-table-column",{attrs:{label:"还款类型",align:"center",prop:"repayment_type"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(0==e.row.repayment_type?"未选择":1==e.row.repayment_type?"全额":"分期")+" ")]}}])}),a("el-table-column",{attrs:{label:"剩余欠款",align:"center",prop:"totalMoney",width:"120"}}),a("el-table-column",{attrs:{label:"应还银行代偿金额",align:"center",prop:"fxjMoney",width:"120"}}),a("el-table-column",{attrs:{label:"实还银行代偿金额",align:"center",prop:"qdMoney",width:"120"}}),a("el-table-column",{attrs:{label:"应还代扣结清金额",align:"center",prop:"gmjMoney",width:"100"}}),a("el-table-column",{attrs:{label:"实还代扣金额",align:"center",prop:"kjjMoney",width:"100"}}),a("el-table-column",{attrs:{label:"应还违约金",align:"center",prop:"kjczMoney",width:"110"}}),a("el-table-column",{attrs:{label:"实还违约金",align:"center",prop:"sbczMoney",width:"110"}}),a("el-table-column",{attrs:{label:"应还其他金额",align:"center",prop:"overdueDays"}}),a("el-table-column",{attrs:{label:"实还其他金额",align:"center",prop:"overdueDays"}}),a("el-table-column",{attrs:{label:"催记更新时间",align:"center",prop:"overdueDays"}}),a("el-table-column",{attrs:{label:"催记类型",align:"center",prop:"overdueDays"}}),a("el-table-column",{attrs:{label:"全额（代偿发起）时间 ",align:"center",prop:"slippageStatus"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"110"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-popover",{attrs:{placement:"left",trigger:"click","popper-class":"custom-popover"}},[a("div",{staticClass:"operation-buttons"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_loan_compensation:vw_loan_compensation:edit"],expression:"['vw_loan_compensation:vw_loan_compensation:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return t.handleSubmitReminder(e.row)}}},[t._v(" 提交催记 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_loan_compensation:vw_loan_compensation:edit"],expression:"['vw_loan_compensation:vw_loan_compensation:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return t.logView(e.row)}}},[t._v(" 催记日志 ")]),2!=e.row.repayment_type?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_loan_compensation:vw_loan_compensation:edit"],expression:"['vw_loan_compensation:vw_loan_compensation:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return t.handleCompensation(e.row)}}},[t._v(" 代偿分期 ")]):t._e(),2==e.row.repayment_type?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_loan_compensation:vw_loan_compensation:edit"],expression:"['vw_loan_compensation:vw_loan_compensation:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return t.handleUpdate(e.row)}}},[t._v(" 代偿分期账单 ")]):t._e(),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_loan_compensation:vw_loan_compensation:edit"],expression:"['vw_loan_compensation:vw_loan_compensation:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return t.urgeBackSettle(e.row,1)}}},[t._v(" 代偿结清 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_loan_compensation:vw_loan_compensation:edit"],expression:"['vw_loan_compensation:vw_loan_compensation:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return t.urgeBackSettle(e.row,2)}}},[t._v(" 代偿减免结清 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_loan_compensation:vw_loan_compensation:edit"],expression:"['vw_loan_compensation:vw_loan_compensation:edit']"}],staticClass:"operation-btn",attrs:{size:"mini",type:"text"},on:{click:function(a){return t.handleInitiateLegalAction(e.row)}}},[t._v(" 发起法诉 ")])],1),a("el-button",{attrs:{slot:"reference",size:"mini",type:"text"},slot:"reference"},[t._v("更多")])],1)]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),a("el-dialog",{attrs:{title:"代偿分期账单",visible:t.isCompensatoryFormOpen,width:"500px","append-to-body":""},on:{"update:visible":function(e){t.isCompensatoryFormOpen=e}}},[a("el-form",{ref:"form",attrs:{model:t.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"申请分期金额"}},[a("el-input-number",{attrs:{min:0,precision:2,step:100,"controls-position":"right",placeholder:"请输入申请分期金额"},on:{input:t.handleCompensatoryFormChange},model:{value:t.form.applyAmount,callback:function(e){t.$set(t.form,"applyAmount",e)},expression:"form.applyAmount"}})],1),a("el-form-item",{attrs:{label:"分期期数"}},[a("el-input-number",{attrs:{min:1,precision:0,step:1,"controls-position":"right",placeholder:"请输入分期期数"},on:{input:t.handleCompensatoryFormChange},model:{value:t.form.periodCount,callback:function(e){t.$set(t.form,"periodCount",e)},expression:"form.periodCount"}})],1),a("el-form-item",{attrs:{label:"每期账单金额"}},[a("el-input",{attrs:{placeholder:"请输入每期账单金额",disabled:""},model:{value:t.form.billAmount,callback:function(e){t.$set(t.form,"billAmount",e)},expression:"form.billAmount"}})],1),a("el-form-item",{attrs:{label:"每期还款日"}},[a("el-select",{staticClass:"center-select",attrs:{placeholder:"请选择每期还款日",clearable:""},model:{value:t.form.repayDay,callback:function(e){t.$set(t.form,"repayDay",e)},expression:"form.repayDay"}},t._l(28,(function(e){return a("el-option",{key:e,attrs:{label:e+"日",value:e}},[a("span",{staticStyle:{display:"flex","justify-content":"center"}},[t._v(t._s(e+"日"))])])})),1)],1),a("el-form-item",{attrs:{label:"尾款余额"}},[a("el-input-number",{attrs:{min:0,precision:2,step:100,"controls-position":"right",placeholder:"请输入尾款余额"},on:{change:t.handleCompensatoryFormChange},model:{value:t.form.tailAmount,callback:function(e){t.$set(t.form,"tailAmount",e)},expression:"form.tailAmount"}})],1),a("el-form-item",{attrs:{label:"尾款余额支付时间"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"请选择尾款余额支付时间"},model:{value:t.form.tailPayTime,callback:function(e){t.$set(t.form,"tailPayTime",e)},expression:"form.tailPayTime"}})],1),a("el-form-item",{attrs:{label:"账号类型"}},[a("el-select",{attrs:{placeholder:"账号类型",clearable:""},model:{value:t.form.accountType,callback:function(e){t.$set(t.form,"accountType",e)},expression:"form.accountType"}},t._l(t.accountList,(function(t){return a("el-option",{key:t.card,attrs:{label:t.name,value:t.card}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.compensationSubmit}},[t._v("确 定")]),a("el-button",{on:{click:function(e){return t.cancel("isCompensatoryFormOpen")}}},[t._v("取 消")])],1)],1),a("el-dialog",{staticClass:"dialogBox",attrs:{"before-close":t.cancelurgeBack,"close-on-click-modal":!1,title:"代偿结清",visible:t.urgeBackopen,width:"800px","append-to-body":""},on:{"update:visible":function(e){t.urgeBackopen=e}}},[a("el-form",{ref:"urgeform",attrs:{model:t.urgeform,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"状态",prop:"orgName"}},[a("div",{staticStyle:{"margin-bottom":"10px","font-weight":"bold"}},[t._v(" "+t._s(t.examineStatusMap[t.urgeform.examineStatus]||"未知")+" ")])])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"贷款人",prop:"customerName"}},[a("el-input",{attrs:{disabled:!0},model:{value:t.urgeform.customerName,callback:function(e){t.$set(t.urgeform,"customerName",e)},expression:"urgeform.customerName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"出单渠道",prop:"orgName"}},[a("el-input",{attrs:{disabled:!0},model:{value:t.urgeform.orgName,callback:function(e){t.$set(t.urgeform,"orgName",e)},expression:"urgeform.orgName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"放款银行",prop:"bank"}},[a("el-input",{attrs:{disabled:!0},model:{value:t.urgeform.bank,callback:function(e){t.$set(t.urgeform,"bank",e)},expression:"urgeform.bank"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"放款金额",prop:"loanAmount"}},[a("el-input",{attrs:{disabled:!0},model:{value:t.urgeform.loanAmount,callback:function(e){t.$set(t.urgeform,"loanAmount",e)},expression:"urgeform.loanAmount"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"总还款金额"}},[a("el-input",{attrs:{disabled:!0},model:{value:t.urgeform.totalMoney,callback:function(e){t.$set(t.urgeform,"totalMoney",e)},expression:"urgeform.totalMoney"}})],1)],1),2==t.urgeform.status?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"减免金额",prop:"deductionAmount"}},[a("el-input",{attrs:{placeholder:"减免金额",disabled:!t.isUrgeFormEditable},model:{value:t.urgeform.deductionAmount,callback:function(e){t.$set(t.urgeform,"deductionAmount",e)},expression:"urgeform.deductionAmount"}})],1)],1):t._e()],1),2==t.urgeform.status?a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"上传凭据"}},[t.isUrgeFormEditable?a("el-upload",{attrs:{action:t.uploadImgUrl,"list-type":"picture-card","file-list":t.urgeform.reductionImg,headers:t.headers,"on-preview":t.handlePictureCardPreview,"on-success":function(e,a,o){return t.handleUploadSuccess(e,a,o,"urgeform.reductionImg")},"on-remove":function(e,a){return t.handleRemove(e,a,"urgeform.reductionImg")},"on-error":t.handleUploadError}},[a("i",{staticClass:"el-icon-plus"})]):a("div",[t.urgeform.reductionImg&&t.urgeform.reductionImg.length>0?a("el-upload",{attrs:{"list-type":"picture-card","file-list":t.urgeform.reductionImg,disabled:!0,action:t.uploadImgUrl}},[a("div")]):a("el-upload",{attrs:{"list-type":"picture-card",disabled:!0,action:t.uploadImgUrl}},[a("div",[t._v("暂无凭据")])])],1)],1)],1)],1):t._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[0==t.urgeform.examineStatus?a("el-button",{attrs:{type:"primary"},on:{click:t.submitUrge}},[t._v("确 定")]):t._e(),a("el-button",{on:{click:t.cancelurgeBack}},[t._v("取 消")])],1)],1),a("el-dialog",{staticClass:"dialogBox",attrs:{"close-on-click-modal":!1,title:"发起法诉",visible:t.litigationOpen,width:"800px"},on:{"update:visible":function(e){t.litigationOpen=e}}},[a("el-descriptions",{staticStyle:{"margin-bottom":"20px"},attrs:{column:12,border:""}},[a("el-descriptions-item",{attrs:{label:"贷款人",span:6}},[t._v(t._s(this.litigationForm.customerName))]),a("el-descriptions-item",{attrs:{label:"出单渠道",span:6}},[t._v(t._s(this.litigationForm.orgName))]),a("el-descriptions-item",{attrs:{label:"放款银行",span:6}},[t._v(t._s(this.litigationForm.bank))]),a("el-descriptions-item",{attrs:{label:"放款金额",span:6}},[t._v(t._s(this.litigationForm.loanAmount))]),a("el-descriptions-item",{attrs:{label:"总欠款金额",span:4}},[t._v(t._s(this.litigationForm.totalAmount))]),a("el-descriptions-item",{attrs:{label:"催回金额",span:4}},[t._v(t._s(this.litigationForm.totalAmountBack))]),a("el-descriptions-item",{attrs:{label:"剩余未还欠款",span:4}},[t._v(t._s(this.litigationForm.totalCompensation))]),a("el-descriptions-item",{attrs:{label:"银行代偿金金额",span:4}},[t._v(t._s(this.litigationForm.bankAmount))]),a("el-descriptions-item",{attrs:{label:"催回金额",span:4}},[t._v(t._s(this.litigationForm.bankAmountBack))]),a("el-descriptions-item",{attrs:{label:"剩余未还代偿金",span:4}},[t._v(t._s(this.litigationForm.bankCompensation))]),a("el-descriptions-item",{attrs:{label:"代扣结清金额",span:4}},[t._v(t._s(this.litigationForm.withholdingAmount))]),a("el-descriptions-item",{attrs:{label:"催回金额",span:4}},[t._v(t._s(this.litigationForm.withholdingAmountBack))]),a("el-descriptions-item",{attrs:{label:"剩余未还代扣金额",span:4}},[t._v(t._s(this.litigationForm.withholdingCompensation))]),a("el-descriptions-item",{attrs:{label:"违约金",span:4}},[t._v(t._s(this.litigationForm.liquidatedAmount))]),a("el-descriptions-item",{attrs:{label:"催回违约金金额",span:4}},[t._v(t._s(this.litigationForm.liquidatedAmountBack))]),a("el-descriptions-item",{attrs:{label:"剩余未还违约金金额",span:4}},[t._v(t._s(this.litigationForm.liquidatedCompensation))]),a("el-descriptions-item",{attrs:{label:"其他欠款",span:4}},[t._v(t._s(this.litigationForm.otherAmount))]),a("el-descriptions-item",{attrs:{label:"催回其他欠款金额",span:4}},[t._v(t._s(this.litigationForm.otherAmountBack))]),a("el-descriptions-item",{attrs:{label:"剩余未还其他欠款金额",span:4}},[t._v(t._s(this.litigationForm.otherCompensation))])],1),a("el-form",{attrs:{"label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"是否撤销催收单"}},[a("el-switch",{staticStyle:{"margin-left":"20px"},model:{value:t.litigationForm.isDestroy,callback:function(e){t.$set(t.litigationForm,"isDestroy",e)},expression:"litigationForm.isDestroy"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"是否找回找车单"}},[a("el-switch",{staticStyle:{"margin-left":"20px"},model:{value:t.litigationForm.isFindCar,callback:function(e){t.$set(t.litigationForm,"isFindCar",e)},expression:"litigationForm.isFindCar"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.litigationSubmit}},[t._v("确 定")]),a("el-button",{on:{click:function(e){return t.cancel("litigationOpen")}}},[t._v("取 消")])],1)],1),a("userInfo",{ref:"userInfo",attrs:{visible:t.userInfoVisible,title:"贷款人信息",customerInfo:t.customerInfo},on:{"update:visible":function(e){t.userInfoVisible=e}}}),a("carInfo",{ref:"carInfo",attrs:{visible:t.carInfoVisible,title:"车辆信息",plateNo:t.plateNo,permission:"2"},on:{"update:visible":function(e){t.carInfoVisible=e}}}),a("loan-reminder-log",{ref:"loanReminderLog",attrs:{"loan-id":t.currentRow.loanId}}),a("loan-reminder-log-submit",{ref:"loanReminderLogSubmit",attrs:{"loan-id":t.currentRow.loanId,status:1}})],1)},n=[],r=(a("a15b"),a("d81d"),a("a9e3"),a("b680"),a("d3b7"),a("0643"),a("a573"),a("d96a")),l=a("bd52"),i=a("99c9"),s=a("413c"),u=a("2eca"),m=a("0f5f"),c=a("7954"),p=a("a5e3"),d={props:{value:[String,Object,Array],action:{type:String,default:"/common/ossupload"}},name:"Vw_loan_compensation_query",components:{userInfo:u["a"],carInfo:m["a"],LoanReminderLog:c["a"],LoanReminderLogSubmit:p["a"]},data:function(){return{examineStatusMap:{0:"跟催员发起",1:"贷后试算",2:"跟催员提交凭据",3:"同意",4:"拒绝",5:"核对完成"},loading:!1,showSearch:!0,total:0,vw_loan_compensationList:[],accountList:[],form:{},title:"",isCompensatoryFormOpen:!1,queryParams:{pageNum:1,pageSize:15,customerName:"",certId:"",plateNo:"",salesman:"",orgName:"",bank:"",status:"",createBy:"",followUpType:""},lendingBankList:[{value:"EO00000010",label:"苏银金租"},{value:"IO00000006",label:"浙商银行"},{value:"IO00000007",label:"中关村银行"},{value:"IO00000008",label:"蓝海银行"},{value:"IO00000009",label:"华瑞银行"},{value:"IO00000010",label:"皖新租赁"}],examineStatusList:[{label:"跟催员发起",value:"0"},{label:"贷后试算",value:"1"},{label:"跟催员提交凭据",value:"2"},{label:"同意",value:"3"},{label:"拒绝",value:"4"},{label:"核对完成",value:"5"}],statusList:[{label:"待追偿",value:1},{label:"代偿中",value:2},{label:"逾期",value:3},{label:"代偿全额结清",value:4},{label:"减免结清",value:5},{label:"代偿完结",value:6}],carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}],followUpList:[{label:"继续联系",value:1},{label:"约定还款",value:2},{label:"无法跟进",value:3}],urgeBackopen:!1,urgeform:{},litigationOpen:!1,litigationForm:{},isUrgeFormEditable:!0,urgeAllMoney:0,urgeformbtotalMoney:0,urgeformdtotalMoney:0,urgeformliquidatedDamages:0,urgeformoneCommutation:0,addId:null,chForm:{},loanId:null,applyId:"",lenderShow:!1,plateNo:"",carInfoVisible:!1,currentRow:{},customerInfo:{customerId:"",applyId:""},userInfoVisible:!1}},created:function(){this.getList(),this.getBankList()},methods:{getList:function(){var t=this;this.loading=!0,Object(r["e"])(this.queryParams).then((function(e){t.vw_loan_compensationList=e.rows||[],t.total=e.total,t.loading=!1}))},handleCompensation:function(t){this.form={applyAmount:0,periodCount:1,billAmount:"0.00",tailAmount:0,repayDay:1,tailPayTime:null,accountType:""},this.form.loanId=t.loanId,this.form.applyAmount=t.totalMoney,this.form.periodCount=1,this.isCompensatoryFormOpen=!0},cancelurgeBack:function(){this.urgeform={},this.chForm={},this.urgeformbtotalMoney=0,this.urgeformdtotalMoney=0,this.urgeformliquidatedDamages=0,this.urgeformoneCommutation=0,this.urgeAllMoney=0,this.urgeBackopen=!1},urgeBackSettle:function(t,e){var a=this;Object(l["c"])(t.loanId,e).then((function(o){o.data?(a.urgeform=o.data,a.urgeform.status=e,o.data.customerName||(a.urgeform.customerName=t.customerName),o.data.orgName||(a.urgeform.orgName=t.orgName),o.data.bank||(a.urgeform.bank=t.bank),o.data.loanAmount||(a.urgeform.loanAmount=t.loanAmount),a.urgeform.btotalMoney=o.data.trialBalance?o.data.trialBalance.btotalMoney:0,a.urgeform.dtotalMoney=o.data.trialBalance?o.data.trialBalance.dtotalMoney:0,a.urgeform.liquidatedDamages=o.data.trialBalance?o.data.trialBalance.liquidatedDamages:0,a.urgeform.oneCommutation=o.data.trialBalance?o.data.trialBalance.oneCommutation:0,a.urgeformbtotalMoney=o.data.trialBalance?o.data.trialBalance.btotalMoney:0,a.urgeformdtotalMoney=o.data.trialBalance?o.data.trialBalance.dtotalMoney:0,a.urgeformliquidatedDamages=o.data.trialBalance?o.data.trialBalance.liquidatedDamages:0,a.urgeformoneCommutation=o.data.trialBalance?o.data.trialBalance.oneCommutation:0,a.isUrgeFormEditable=!o.data.deductionAmount,a.urgeform.reductionImg=o.data.reductionImg?o.data.reductionImg.split(","):[],a.urgeAllMoney=o.data.totalMoney,a.addId=o.data.id,a.chForm=o.data):a.postTrial(t,1),a.urgeBackopen=!0})).catch((function(t){}))},postTrial:function(t,e){var a=this,o={applyId:t.applyId,loanId:t.loanId,customerName:t.customerName,orgName:t.orgName,loanAmount:t.loanAmount,bank:t.bank,partnerId:t.partnerId,status:e,salesman:t.salesman,overdueDays:t.boverdueDays,examineStatus:0};Object(l["x"])(o).then((function(t){a.addId=t.id,1==e&&(a.urgeform=t.data,a.chForm=t.data)}))},submitUrge:function(){var t=this;if(!this.urgeformbtotalMoney||this.urgeform.accountNumber1)if(!this.urgeformdtotalMoney||this.urgeform.accountNumber2)if(!this.urgeformliquidatedDamages||this.urgeform.accountNumber3)if(!this.urgeform.otherDebt||this.urgeform.accountNumber4)if(!this.urgeformoneCommutation||this.urgeform.accountNumber5){var e={customerName:this.urgeform.customerName,orgName:this.urgeform.orgName,bank:this.urgeform.bank,loanAmount:this.urgeform.loanAmount,btotalMoney:this.urgeform.btotalMoney,accountNumber1:this.urgeform.accountNumber1,dtotalMoney:this.urgeform.dtotalMoney,accountNumber2:this.urgeform.accountNumber2,liquidatedDamages:this.urgeform.liquidatedDamages,accountNumber3:this.urgeform.accountNumber3,otherDebt:this.urgeform.otherDebt,accountNumber4:this.urgeform.accountNumber4,oneCommutation:this.urgeform.oneCommutation,accountNumber5:this.urgeform.accountNumber5,totalMoney:this.urgeAllMoney,id:this.addId,status:1,examineStatus:1,deductionAmount:this.urgeform.deductionAmount,reductionImg:Array.isArray(this.urgeform.reductionImg)?this.urgeform.reductionImg.length?this.urgeform.reductionImg.join(","):"":this.urgeform.reductionImg||""};Object(l["y"])(e).then((function(e){200==e.code&&(t.$modal.msgSuccess("提交成功"),t.urgeBackopen=!1,t.urgeform={},t.urgeformbtotalMoney=0,t.urgeformdtotalMoney=0,t.urgeformliquidatedDamages=0,t.urgeformoneCommutation=0)}))}else this.$modal.msgError("请选择单期代偿金账号");else this.$modal.msgError("请选择其他欠款账号");else this.$modal.msgError("请选择违约金账号");else this.$modal.msgError("请选择代扣剩余账号");else this.$modal.msgError("请选择银行结清账号")},handleUpdate:function(){},handleInitiateLegalAction:function(t){this.litigationForm={loanId:t.loanId,customerName:t.customerName,orgName:t.orgName,bank:t.bank,loanAmount:t.loanAmount||0,totalAmount:0,totalAmountBack:0,totalCompensation:0,bankAmount:t.totalMoney||0,bankAmountBack:t.reminderBMoney||0,bankCompensation:t.remainingBankCompensation||0,withholdingAmount:t.dmoney||0,withholdingAmountBack:t.reminderDMoney||0,withholdingCompensation:t.remainingWithholdingAmount||0,liquidatedAmount:t.penaltyMoney||0,liquidatedAmountBack:t.reminderPMoney||0,liquidatedCompensation:t.remainingPenaltyAmount||0,otherAmount:t.otherDebt||0,otherAmountBack:t.reminderOMoney||0,otherCompensation:t.remainingOtherDebts||0,litigationSubStatus:"暂不起诉",isFindCar:!1,isDestroy:!1},this.litigationForm.totalAmount=this.litigationForm.bankAmount+this.litigationForm.withholdingAmount+this.litigationForm.liquidatedAmount+this.litigationForm.otherAmount,this.litigationForm.totalAmountBack=this.litigationForm.bankAmountBack+this.litigationForm.withholdingAmountBack+this.litigationForm.liquidatedAmountBack+this.litigationForm.otherAmountBack,this.litigationForm.totalCompensation=this.litigationForm.totalAmount-this.litigationForm.totalAmountBack,this.litigationOpen=!0},litigationSubmit:function(){var t=this;console.log(this.litigationForm),Object(s["a"])(this.litigationForm).then((function(e){t.$message.success("提交成功"),t.litigationOpen=!1,t.litigationForm={},t.getList()}))},handleCompensatoryFormChange:function(){var t=Number(this.form.applyAmount)||0,e=Number(this.form.periodCount)||1,a=Number(this.form.tailAmount)||0;this.form.billAmount=t>=0&&e>=1?((t-a)/e).toFixed(2):"0.00"},compensationSubmit:function(){var t=this;if(!this.form.applyAmount||this.form.applyAmount<=0)this.$message.error("申请分期金额必须大于0");else if(!this.form.periodCount||this.form.periodCount<=0)this.$message.error("分期期数必须大于0");else{var e=Number(this.form.billAmount);!e||e<=0?this.$message.error("每期账单金额必须大于0"):this.form.repayDay?this.form.accountType?(console.log(this.form),Object(i["a"])(this.form).then((function(e){t.$message.success("提交成功"),t.isCompensatoryFormOpen=!1,t.getList()}))):this.$message.error("请选择账号类型"):this.$message.error("请选择每期还款日")}},cancel:function(t){this[t]=!1,this.reset()},reset:function(){this.form={}},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},getBankList:function(){var t=this;Object(l["i"])().then((function(e){t.accountList=e.rows}))},resetQuery:function(){this.queryParams={pageNum:1,pageSize:15,customerName:"",certId:"",plateNo:"",salesman:"",orgName:"",bank:"",status:"",createBy:"",followUpType:""},this.handleQuery()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.id})),this.single=1!==t.length,this.multiple=!t.length},logView:function(t){this.currentRow=t,this.$refs.loanReminderLog.openLogDialog()},handleSubmitReminder:function(t){var e=this;this.currentRow=t,this.$nextTick((function(){e.$refs.loanReminderLogSubmit.openDialog()}))},openUserInfo:function(t){this.customerInfo=t,this.userInfoVisible=!0},openCarInfo:function(t){this.plateNo=t,this.carInfoVisible=!0}}},f=d,g=(a("4021"),a("d3b9"),a("2877")),b=Object(g["a"])(f,o,n,!1,null,"168cb737",null);e["default"]=b.exports},"31cd":function(t,e,a){},4021:function(t,e,a){"use strict";a("ef73")},"413c":function(t,e,a){"use strict";a.d(e,"f",(function(){return n})),a.d(e,"a",(function(){return r})),a.d(e,"i",(function(){return l})),a.d(e,"d",(function(){return i})),a.d(e,"h",(function(){return s})),a.d(e,"b",(function(){return u})),a.d(e,"c",(function(){return m})),a.d(e,"e",(function(){return c})),a.d(e,"g",(function(){return p}));var o=a("b775");function n(t){return Object(o["a"])({url:"/vw_litigation_case_full/vw_litigation_case_full/list",method:"get",params:t})}function r(t){return Object(o["a"])({url:"/litigation_case/litigation_case",method:"post",data:t})}function l(t){return Object(o["a"])({url:"/litigation_case/litigation_case",method:"put",data:t})}function i(t){return Object(o["a"])({url:"/litigation_case/litigation_case/byLoanId/"+t,method:"get"})}function s(t){return Object(o["a"])({url:"/common/public/insertLoanReminderAndLitigationLog",method:"post",data:t})}function u(t){return Object(o["a"])({url:"/litigation_cost/litigation_cost",method:"post",data:t})}function m(t){return Object(o["a"])({url:"/litigation_cost/litigation_cost/checkLimitedFees/"+t,method:"get"})}function c(t){return Object(o["a"])({url:"/litigation_cost/litigation_cost/summary",method:"post",data:{caseIds:t}})}function p(t){return Object(o["a"])({url:"/litigation_log/litigation_log/list",method:"get",params:t})}},"99c9":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"a",(function(){return r})),a.d(e,"c",(function(){return l}));var o=a("b775");function n(t){return Object(o["a"])({url:"/installment_application_audit/installment_application_audit/list",method:"get",params:t})}function r(t){return Object(o["a"])({url:"/installment_application_audit/installment_application_audit",method:"post",data:t})}function l(t){return Object(o["a"])({url:"/installment_application_audit/installment_application_audit",method:"put",data:t})}},d3b9:function(t,e,a){"use strict";a("31cd")},d96a:function(t,e,a){"use strict";a.d(e,"d",(function(){return n})),a.d(e,"e",(function(){return r})),a.d(e,"c",(function(){return l})),a.d(e,"a",(function(){return i})),a.d(e,"f",(function(){return s})),a.d(e,"b",(function(){return u}));var o=a("b775");function n(t){return Object(o["a"])({url:"/loan_compensation/loan_compensation/list",method:"get",params:t})}function r(t){return Object(o["a"])({url:"/vw_loan_compensation/vw_loan_compensation/listGte3",method:"get",params:t})}function l(t){return Object(o["a"])({url:"/loan_compensation/loan_compensation/"+t,method:"get"})}function i(t){return Object(o["a"])({url:"/loan_compensation/loan_compensation/initlate",method:"post",data:t})}function s(t){return Object(o["a"])({url:"/loan_compensation/loan_compensation",method:"put",data:t})}function u(t){return Object(o["a"])({url:"/loan_compensation/loan_compensation/"+t,method:"delete"})}},ef73:function(t,e,a){}}]);