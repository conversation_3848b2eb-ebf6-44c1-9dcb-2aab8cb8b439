(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2568739a"],{"0f5f":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.carInfo.plateNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车架号"}},[e._v(e._s(e.carInfo.identityNo||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"发动机号"}},[e._v(e._s(e.carInfo.engineNumber||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(e._s(e.carStatusLabel))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"车辆位置"}},[e._v(e._s(e.carInfo.carAddress||"-"))]):e._e(),"1"==e.permission?a("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(e._s(e.gpsStatusLabel))]):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"车牌照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看车牌照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"行驶证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看行驶证照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?a("el-descriptions-item",{attrs:{label:"登记证照片"}},[a("el-button",{attrs:{type:"text"}},[e._v("查看登记证照片")])],1):e._e()],1)],1)},l=[],o=(a("7db0"),a("d3b7"),a("0643"),a("fffc"),a("bd52")),r={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var e=this,t=this.carStatusList.find((function(t){return t.value===e.carInfo.carStatus}));return t?t.label:"未知状态"},gpsStatusLabel:function(){var e=this,t=this.gpsStatusList.find((function(t){return t.value===e.carInfo.gpsStatus}));return t?t.label:"未知状态"}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,e&&this.plateNo&&Object(o["e"])(this.plateNo).then((function(e){t.carInfo=e.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},i=r,s=a("2877"),u=Object(s["a"])(i,n,l,!1,null,null,null);t["a"]=u.exports},1791:function(e,t,a){},"2eca":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a("div",{staticClass:"descriptions"},[a("el-descriptions",{attrs:{column:3,size:"large",border:""}},[a("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[a("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[a("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),a("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[a("template",{slot:"label"},[e._v("面签照片")]),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),a("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[a("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[a("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[a("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),a("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[a("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),a("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[a("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),a("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),a("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},l=[],o=a("bd52"),r={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(o["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},i=r,s=(a("d6fd"),a("2877")),u=Object(s["a"])(i,n,l,!1,null,"8a3d4978",null);t["a"]=u.exports},b190:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"",prop:"customerName"}},[a("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),a("el-form-item",{attrs:{label:"",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"请输入车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",{attrs:{label:"",prop:"salesman"}},[a("el-input",{attrs:{placeholder:"业务员姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.salesman,callback:function(t){e.$set(e.queryParams,"salesman",t)},expression:"queryParams.salesman"}})],1),a("el-form-item",{attrs:{label:"",prop:"orgName"}},[a("el-input",{attrs:{placeholder:"请输入出单渠道",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.orgName,callback:function(t){e.$set(e.queryParams,"orgName",t)},expression:"queryParams.orgName"}})],1),a("el-form-item",{attrs:{label:"",prop:"bank"}},[a("el-select",{attrs:{placeholder:"放款银行",clearable:""},model:{value:e.queryParams.bank,callback:function(t){e.$set(e.queryParams,"bank",t)},expression:"queryParams.bank"}},e._l(e.lendingBankList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"",prop:"nickName"}},[a("el-input",{attrs:{placeholder:"申请人",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.nickName,callback:function(t){e.$set(e.queryParams,"nickName",t)},expression:"queryParams.nickName"}})],1),a("el-form-item",{attrs:{label:"",prop:"examineStatus"}},[a("el-select",{attrs:{placeholder:"审批状态",clearable:""},model:{value:e.queryParams.examineStatus,callback:function(t){e.$set(e.queryParams,"examineStatus",t)},expression:"queryParams.examineStatus"}},e._l(e.examineStatusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vw_loan_compensationList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),a("el-table-column",{attrs:{label:"审批状态",align:"center",prop:"examineStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(" "+e._s("0"==t.row.examineStatus?"跟催员发起":"1"==t.row.examineStatus?"贷后试算":"2"==t.row.examineStatus?"跟催员提交凭据":"3"==t.row.examineStatus?"同意":"4"==t.row.examineStatus?"拒绝":"核对完成")+" ")])]}}])}),a("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"customerName"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openUserInfo({customerId:t.row.customerId,applyId:t.row.applyId})}}},[e._v(" "+e._s(t.row.customerName)+" ")])]}}])}),a("el-table-column",{attrs:{label:"业务员",align:"center",prop:"salesman"}}),a("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"orgName"}}),a("el-table-column",{attrs:{label:"车牌号",align:"center",prop:"plateNo",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.openCarInfo(t.row.plateNo)}}},[e._v(e._s(t.row.plateNo))])]}}])}),a("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"bank"}}),a("el-table-column",{attrs:{label:"放款金额",align:"center",prop:"loanAmount"}}),a("el-table-column",{attrs:{label:"其他欠款",align:"center",prop:"otherDebt"}}),a("el-table-column",{attrs:{label:"总代偿金额",align:"center",prop:"totalMoney",width:"120"}}),a("el-table-column",{attrs:{label:"风险金划扣金额",align:"center",prop:"fxjMoney",width:"120"}}),a("el-table-column",{attrs:{label:"渠道转入金额",align:"center",prop:"qdMoney",width:"120"}}),a("el-table-column",{attrs:{label:"借广明金额",align:"center",prop:"gmjMoney",width:"100"}}),a("el-table-column",{attrs:{label:"借科技金额",align:"center",prop:"kjjMoney",width:"100"}}),a("el-table-column",{attrs:{label:"科技出资金额",align:"center",prop:"kjczMoney",width:"110"}}),a("el-table-column",{attrs:{label:"守邦出资金额",align:"center",prop:"sbczMoney",width:"110"}}),a("el-table-column",{attrs:{label:"逾期天数",align:"center",prop:"overdueDays"}}),a("el-table-column",{attrs:{label:"车辆位置",align:"center",prop:"carDetailAddress"}}),a("el-table-column",{attrs:{label:"车辆状态",align:"center",prop:"carStatus",width:"130"}}),a("el-table-column",{attrs:{label:"GPS状态",align:"center",prop:"gpsStatus"}}),a("el-table-column",{attrs:{label:"逾期状态 ",align:"center",prop:"slippageStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(" "+e._s("1"==t.row.slippageStatus?"提醒":"2"==t.row.slippageStatus?"电催":"3"==t.row.slippageStatus?"上访":"4"==t.row.slippageStatus?"逾期30-60":"逾期60+")+" ")])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[3===t.row.examineStatus||4===t.row.examineStatus?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_loan_compensation:vw_loan_compensation:edit"],expression:"['vw_loan_compensation:vw_loan_compensation:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleSp(t.row)}}},[e._v(" 查看 ")]):a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vw_loan_compensation:vw_loan_compensation:edit"],expression:"['vw_loan_compensation:vw_loan_compensation:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleSp(t.row)}}},[e._v(" 审批 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.form._readonly?"查看":"审批",visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-descriptions",{staticStyle:{"margin-bottom":"20px"},attrs:{column:2,border:""}},[a("el-descriptions-item",{attrs:{label:"贷款人"}},[e._v(e._s(e.form.customerName))]),a("el-descriptions-item",{attrs:{label:"出单渠道"}},[e._v(e._s(e.form.orgName))]),a("el-descriptions-item",{attrs:{label:"放款银行"}},[e._v(e._s(e.form.bank))]),a("el-descriptions-item",{attrs:{label:"放款金额"}},[e._v(e._s(e.form.loanAmount))]),a("el-descriptions-item",{attrs:{label:"剩余本金"}},[e._v(e._s(e.form.principal||"-"))]),a("el-descriptions-item",{attrs:{label:"银行逾期金额"}},[e._v(e._s(e.form.boverdueAmount||"-"))]),a("el-descriptions-item",{attrs:{label:"银行利息"}},[e._v(e._s(e.form.interest||"-"))]),a("el-descriptions-item",{attrs:{label:"代偿总金额"}},[e._v(e._s(e.form.btotalMoney||"-"))]),a("el-descriptions-item",{attrs:{label:"风险金划金额"}},[e._v(e._s(e.form.fxjMoney||"-"))]),a("el-descriptions-item",{attrs:{label:"账号类型"}},[e._v(e._s(e.form.fxjAccount||"-"))]),a("el-descriptions-item",{attrs:{label:"渠道转入金额"}},[e._v(e._s(e.form.qdMoney||"-"))]),a("el-descriptions-item",{attrs:{label:"账号类型"}},[e._v(e._s(e.form.qdAccount||"-"))]),a("el-descriptions-item",{attrs:{label:"广明借额"}},[e._v(e._s(e.form.gmjMoney||"-"))]),a("el-descriptions-item",{attrs:{label:"账号类型"}},[e._v(e._s(e.form.gmjAccount||"-"))]),a("el-descriptions-item",{attrs:{label:"科技借额"}},[e._v(e._s(e.form.kjjMoney||"-"))]),a("el-descriptions-item",{attrs:{label:"账号类型"}},[e._v(e._s(e.form.kjjAccount||"-"))]),a("el-descriptions-item",{attrs:{label:"科技出资金额"}},[e._v(e._s(e.form.kjczMoney||"-"))]),a("el-descriptions-item",{attrs:{label:"账号类型"}},[e._v(e._s(e.form.kjczAccount||"-"))]),a("el-descriptions-item",{attrs:{label:"守邦出资金额"}},[e._v(e._s(e.form.sbczMoney||"-"))]),a("el-descriptions-item",{attrs:{label:"账号类型"}},[e._v(e._s(e.form.sbczAccount||"-"))]),a("el-descriptions-item",{attrs:{label:"上传凭据",span:2}},[e.form.image&&e.form.image.length?a("div",e._l(e.form.image,(function(t,n){return a("el-image",{key:n,staticStyle:{width:"60px",height:"60px","margin-right":"8px"},attrs:{src:t,"preview-src-list":e.form.image}})})),1):a("span",[e._v("暂无凭据")])]),a("el-descriptions-item",{attrs:{label:"代扣剩余未还金额"}},[e._v(e._s(e.form.dMoney||"-"))]),a("el-descriptions-item",{attrs:{label:"违约金"}},[e._v(e._s(e.form.penaltyMoney||"-"))]),a("el-descriptions-item",{attrs:{label:"登记其他欠款金额"}},[e._v(e._s(e.form.otherDebt||"-"))]),a("el-descriptions-item",{attrs:{label:"总账款"}},[e._v(e._s(e.form.totalMoney||"-"))])],1),a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"120px"}},[e.form._readonly&&3===e.form.examineStatus?[a("div",{staticStyle:{color:"#67c23a","font-size":"18px","margin-bottom":"16px"}},[e._v("已同意")])]:e.form._readonly&&4===e.form.examineStatus?[a("div",{staticStyle:{color:"#f56c6c","font-size":"18px","margin-bottom":"16px"}},[e._v("已拒绝")])]:e._e(),e.form._readonly?e._e():[a("el-form-item",{attrs:{label:"审批"}},[a("el-radio-group",{model:{value:e.form.examineStatus,callback:function(t){e.$set(e.form,"examineStatus",t)},expression:"form.examineStatus"}},[a("el-radio",{attrs:{label:3}},[e._v("同意")]),a("el-radio",{attrs:{label:4}},[e._v("拒绝")])],1)],1),4==e.form.examineStatus?a("el-form-item",{attrs:{label:"拒绝原因"}},[a("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入拒绝原因"},model:{value:e.form.reason,callback:function(t){e.$set(e.form,"reason",t)},expression:"form.reason"}})],1):e._e()]],2),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e.form._readonly?e._e():a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("userInfo",{ref:"userInfo",attrs:{visible:e.userInfoVisible,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.userInfoVisible=t}}}),a("carInfo",{ref:"carInfo",attrs:{visible:e.carInfoVisible,title:"车辆信息",plateNo:e.plateNo,permission:"2"},on:{"update:visible":function(t){e.carInfoVisible=t}}})],1)},l=[],o=a("5530"),r=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("b775"));function i(e){return Object(r["a"])({url:"/vw_loan_compensation/vw_loan_compensation/list",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/vw_loan_compensation/vw_loan_compensation/"+e,method:"get"})}function u(e){return Object(r["a"])({url:"/vw_loan_compensation/vw_loan_compensation/"+e,method:"delete"})}var c=a("d96a"),m=a("2eca"),p=a("0f5f"),d={name:"Vw_loan_compensation",components:{userInfo:m["a"],carInfo:p["a"]},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,vw_loan_compensationList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:15,customerName:null,plateNo:null,salesman:null,orgName:null,bank:null,nickName:null,examineStatus:null},form:{},rules:{id:[{required:!0,message:"$comment不能为空",trigger:"blur"}]},lendingBankList:[{value:"EO00000010",label:"苏银金租"},{value:"IO00000006",label:"浙商银行"},{value:"IO00000007",label:"中关村银行"},{value:"**********",label:"蓝海银行"},{value:"**********",label:"华瑞银行"},{value:"**********",label:"皖新租赁"}],examineStatusList:[{label:"跟催员发起",value:"0"},{label:"贷后试算",value:"1"},{label:"跟催员提交凭据",value:"2"},{label:"同意",value:"3"},{label:"拒绝",value:"4"},{label:"核对完成",value:"5"}],customerInfo:{customerId:"",applyId:""},userInfoVisible:!1,plateNo:"",carInfoVisible:!1}},created:function(){this.getList()},methods:{handleSp:function(e){this.reset(),this.form=Object(o["a"])({},e),this.form._readonly=3===e.examineStatus||4===e.examineStatus,"string"===typeof this.form.image?this.form.image=this.form.image?this.form.image.split(","):[]:Array.isArray(this.form.image)||(this.form.image=[]),this.open=!0},getList:function(){var e=this;this.loading=!0,i(this.queryParams).then((function(t){e.vw_loan_compensationList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,examineStatus:null,reason:null}},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams={pageNum:1,pageSize:15,customerName:"",plateNo:"",salesman:"",orgName:"",bank:"",nickName:"",examineStatus:""},this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加VIEW"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;s(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改VIEW"}))},submitForm:function(){var e=this;if(4!=this.form.examineStatus||this.form.reason){var t={id:this.form.id,examineStatus:this.form.examineStatus,reason:this.form.reason,approveTime:this.formatDateTime(new Date)};Object(c["f"])(t).then((function(t){e.$modal.msgSuccess("审核成功"),e.open=!1,e.getList()}))}else this.$modal.msgError("请输入拒绝原因")},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除VIEW编号为"'+a+'"的数据项？').then((function(){return u(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("vw_loan_compensation/vw_loan_compensation/export",Object(o["a"])({},this.queryParams),"vw_loan_compensation_".concat((new Date).getTime(),".xlsx"))},openUserInfo:function(e){this.customerInfo=e,this.userInfoVisible=!0},openCarInfo:function(e){this.plateNo=e,this.carInfoVisible=!0},formatDateTime:function(e){var t=function(e){return e<10?"0"+e:e};return e.getFullYear()+"-"+t(e.getMonth()+1)+"-"+t(e.getDate())+" "+t(e.getHours())+":"+t(e.getMinutes())+":"+t(e.getSeconds())}}},f=d,b=a("2877"),_=Object(b["a"])(f,n,l,!1,null,null,null);t["default"]=_.exports},bd52:function(e,t,a){"use strict";a.d(t,"l",(function(){return l})),a.d(t,"n",(function(){return o})),a.d(t,"h",(function(){return r})),a.d(t,"i",(function(){return i})),a.d(t,"o",(function(){return s})),a.d(t,"e",(function(){return u})),a.d(t,"s",(function(){return c})),a.d(t,"t",(function(){return m})),a.d(t,"a",(function(){return p})),a.d(t,"A",(function(){return d})),a.d(t,"g",(function(){return f})),a.d(t,"k",(function(){return b})),a.d(t,"w",(function(){return _})),a.d(t,"z",(function(){return v})),a.d(t,"f",(function(){return h})),a.d(t,"x",(function(){return g})),a.d(t,"c",(function(){return y})),a.d(t,"b",(function(){return w})),a.d(t,"v",(function(){return x})),a.d(t,"y",(function(){return S})),a.d(t,"j",(function(){return k})),a.d(t,"q",(function(){return I})),a.d(t,"B",(function(){return j})),a.d(t,"m",(function(){return O})),a.d(t,"r",(function(){return N})),a.d(t,"p",(function(){return P})),a.d(t,"d",(function(){return q})),a.d(t,"u",(function(){return L}));var n=a("b775");function l(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/list",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/listBySlippageStatus3",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/byLoanId/"+e,method:"get"})}function i(){return Object(n["a"])({url:"/bank_account/bank_account/bank?pageSize=30",method:"get"})}function s(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation/detail",method:"get",params:e})}function u(e){return Object(n["a"])({url:"/vw_car/vw_car/"+e,method:"get"})}function c(e){return Object(n["a"])({url:"/loan_reminder/loan_reminder/list",method:"get",params:e})}function m(e){return Object(n["a"])({url:"/loan_reminder/loan_reminder",method:"post",data:e})}function p(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan",method:"put",data:e})}function f(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/"+e,method:"delete"})}function b(e,t){return Object(n["a"])({url:"/vw_customer_info/vw_customer_info/"+e+"?applyNo="+t,method:"get"})}function _(e){return Object(n["a"])({url:"/loan_settle/loan_settle/detail",method:"get",params:e})}function v(e){return Object(n["a"])({url:"/trial_balance/trial_balance/submit",method:"post",data:e})}function h(e){return Object(n["a"])({url:"/trial_balance/trial_balance/submitdc",method:"post",data:e})}function g(e){return Object(n["a"])({url:"/loan_settle/loan_settle",method:"post",data:e})}function y(e,t){return Object(n["a"])({url:"/loan_settle/loan_settle/process",method:"post",data:{loanId:e,status:t}})}function w(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation/initiate",method:"post",data:e})}function x(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation",method:"put",data:e})}function S(e){return Object(n["a"])({url:"/loan_settle/loan_settle",method:"put",data:e})}function k(e){return Object(n["a"])({url:"/system/user/listByRoleId",method:"get",params:{roleId:e}})}function I(e){return Object(n["a"])({url:"/loan_list/loan_list/batchUpdateUrgeUser",method:"put",data:e})}function j(e){return Object(n["a"])({url:"/loan_list/loan_list",method:"put",data:e})}function O(e){return Object(n["a"])({url:"/vw_account_loan/vw_account_loan/extension_list",method:"get",params:e})}function N(e){return Object(n["a"])({url:"/loan_extension/loan_extension/extension_detail",method:"get",params:{loan_id:e}})}function P(e){return Object(n["a"])({url:"/loan_extension/loan_extension/approve",method:"put",data:e})}function q(e){return Object(n["a"])({url:"/loan_list/loan_list/batchAssignPetitionUser",method:"put",data:e})}function L(e){return Object(n["a"])({url:"/loan_list/loan_list/revokePetitionUser/".concat(e),method:"put"})}},d6fd:function(e,t,a){"use strict";a("1791")},d96a:function(e,t,a){"use strict";a.d(t,"d",(function(){return l})),a.d(t,"e",(function(){return o})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return i})),a.d(t,"f",(function(){return s})),a.d(t,"b",(function(){return u}));var n=a("b775");function l(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation/list",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/vw_loan_compensation/vw_loan_compensation/listGte3",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation/"+e,method:"get"})}function i(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation/initlate",method:"post",data:e})}function s(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation",method:"put",data:e})}function u(e){return Object(n["a"])({url:"/loan_compensation/loan_compensation/"+e,method:"delete"})}}}]);