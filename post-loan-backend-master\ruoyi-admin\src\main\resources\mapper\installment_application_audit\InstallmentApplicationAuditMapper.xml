<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.installment_application_audit.mapper.InstallmentApplicationAuditMapper">
    
    <resultMap type="InstallmentApplicationAudit" id="InstallmentApplicationAuditResult">
        <result property="id"    column="id"    />
        <result property="periodCount"    column="period_count"    />
        <result property="billAmount"    column="bill_amount"    />
        <result property="repayDay"    column="repay_day"    />
        <result property="accountType"    column="account_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateDate"    column="update_date"    />
        <result property="status"    column="status"    />
        <result property="reason"    column="reason"    />
        <result property="loanId"    column="loan_id"    />
        <result property="tailAmount"    column="tail_amount"    />
        <result property="applyAmount"    column="apply_amount"    />
        <result property="tailPayTime"    column="tail_pay_time"    />
        <result property="installmentStatus" column="installment_status" />

        <association property="compensateDetail" javaType="VwLoanCompensation">
            <result property="customerId" column="customer_id" />
            <result property="customerName" column="customer_name" />
            <result property="orgName" column="org_name" />
            <result property="salesman" column="salesman" />
            <result property="plateNo" column="plate_no" />
            <result property="carDetailAddress" column="car_detail_address" />
            <result property="totalMoney" column="total_money" />
            <result property="applyId" column="apply_id" />
        </association>
    </resultMap>

    <sql id="selectInstallmentApplicationAuditVo">
        select a.* ,
               vlc.customer_id,
               vlc.customer_name,
               vlc.org_name,
               vlc.salesman,
               vlc.plate_no,
               vlc.car_detail_address,
               vlc.total_money,
               vlc.apply_id
        from installment_application_audit a
        left join vw_loan_compensation vlc on vlc.loan_id = a.loan_id
    </sql>

    <select id="selectInstallmentApplicationAuditList" parameterType="InstallmentApplicationAudit" resultMap="InstallmentApplicationAuditResult">
        <include refid="selectInstallmentApplicationAuditVo"/>
        <where>
            <if test="id !=null"> and a.id = #{id}</if>
            <if test="periodCount != null "> and period_count = #{periodCount}</if>
            <if test="billAmount != null "> and bill_amount = #{billAmount}</if>
            <if test="repayDay != null "> and repay_day = #{repayDay}</if>
            <if test="accountType != null  and accountType != ''"> and account_type = #{accountType}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="status != null "> and a.status = #{status}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="loanId != null "> and loan_id = #{loanId}</if>
            <if test="tailAmount != null "> and tail_amount = #{tailAmount}</if>
            <if test="applyAmount != null "> and apply_amount = #{applyAmount}</if>
            <if test="tailPayTime != null "> and tail_pay_time = #{tailPayTime}</if>
            <if test="tailPayTimeStart != null"> and a.tail_pay_time &gt;= #{tailPayTimeStart}</if>
            <if test="tailPayTimeEnd != null"> and a.tail_pay_time &lt;= #{tailPayTimeEnd}</if>
            <if test="customerName !=null"> and vlc.customer_name like concat('%',#{customerName},'%')</if>
            <if test="jgName !=null"> and vlc.org_name like concat('%',#{jgName},'%')</if>
            <if test="plateNo !=null"> and vlc.plate_no like concat('%',#{plateNo},'%')</if>
            <if test="partnerId !=null"> and vlc.partner_id = #{partnerId}</if>
        </where>
    </select>
    
    <select id="selectInstallmentApplicationAuditById" parameterType="Long" resultMap="InstallmentApplicationAuditResult">
        <include refid="selectInstallmentApplicationAuditVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertInstallmentApplicationAudit" parameterType="InstallmentApplicationAudit" useGeneratedKeys="true" keyProperty="id">
        insert into installment_application_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="periodCount != null">period_count,</if>
            <if test="billAmount != null">bill_amount,</if>
            <if test="repayDay != null">repay_day,</if>
            <if test="accountType != null">account_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="status != null">status,</if>
            <if test="reason != null">reason,</if>
            <if test="loanId != null">loan_id,</if>
            <if test="tailAmount != null">tail_amount,</if>
            <if test="applyAmount != null">apply_amount,</if>
            <if test="tailPayTime != null">tail_pay_time,</if>
            <if test="installmentStatus != null">installment_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="periodCount != null">#{periodCount},</if>
            <if test="billAmount != null">#{billAmount},</if>
            <if test="repayDay != null">#{repayDay},</if>
            <if test="accountType != null">#{accountType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="status != null">#{status},</if>
            <if test="reason != null">#{reason},</if>
            <if test="loanId != null">#{loanId},</if>
            <if test="tailAmount != null">#{tailAmount},</if>
            <if test="applyAmount != null">#{applyAmount},</if>
            <if test="tailPayTime != null">#{tailPayTime},</if>
            <if test="installmentStatus != null">#{installmentStatus},</if>
         </trim>
    </insert>

    <update id="updateInstallmentApplicationAudit" parameterType="InstallmentApplicationAudit">
        update installment_application_audit
        <trim prefix="SET" suffixOverrides=",">
            <if test="periodCount != null">period_count = #{periodCount},</if>
            <if test="billAmount != null">bill_amount = #{billAmount},</if>
            <if test="repayDay != null">repay_day = #{repayDay},</if>
            <if test="accountType != null">account_type = #{accountType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="loanId != null">loan_id = #{loanId},</if>
            <if test="tailAmount != null">tail_amount = #{tailAmount},</if>
            <if test="applyAmount != null">apply_amount = #{applyAmount},</if>
            <if test="tailPayTime != null">tail_pay_time = #{tailPayTime},</if>
            <if test="installmentStatus != null">installment_status = #{installmentStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInstallmentApplicationAuditById" parameterType="Long">
        delete from installment_application_audit where id = #{id}
    </delete>

    <delete id="deleteInstallmentApplicationAuditByIds" parameterType="String">
        delete from installment_application_audit where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>