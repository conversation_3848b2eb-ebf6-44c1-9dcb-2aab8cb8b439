(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f3274d9e"],{"0880":function(e,t,r){"use strict";r("d864")},"0f5f":function(e,t,r){"use strict";var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[r("el-descriptions",{attrs:{column:1,size:"large",border:""}},["1"==e.permission||"2"==e.permission?r("el-descriptions-item",{attrs:{label:"车牌号"}},[e._v(e._s(e.carInfo.plateNo||"-"))]):e._e(),"1"==e.permission?r("el-descriptions-item",{attrs:{label:"车架号"}},[e._v(e._s(e.carInfo.identityNo||"-"))]):e._e(),"1"==e.permission?r("el-descriptions-item",{attrs:{label:"发动机号"}},[e._v(e._s(e.carInfo.engineNumber||"-"))]):e._e(),"1"==e.permission?r("el-descriptions-item",{attrs:{label:"车辆状态"}},[e._v(e._s(e.carStatusLabel))]):e._e(),"1"==e.permission?r("el-descriptions-item",{attrs:{label:"车辆位置"}},[e._v(e._s(e.carInfo.carAddress||"-"))]):e._e(),"1"==e.permission?r("el-descriptions-item",{attrs:{label:"GPS状态"}},[e._v(e._s(e.gpsStatusLabel))]):e._e(),"1"==e.permission||"2"==e.permission?r("el-descriptions-item",{attrs:{label:"车牌照片"}},[r("el-button",{attrs:{type:"text"}},[e._v("查看车牌照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?r("el-descriptions-item",{attrs:{label:"行驶证照片"}},[r("el-button",{attrs:{type:"text"}},[e._v("查看行驶证照片")])],1):e._e(),"1"==e.permission||"2"==e.permission?r("el-descriptions-item",{attrs:{label:"登记证照片"}},[r("el-button",{attrs:{type:"text"}},[e._v("查看登记证照片")])],1):e._e()],1)],1)},o=[],n=(r("7db0"),r("d3b7"),r("0643"),r("fffc"),r("bd52")),l={name:"CarInfo",props:{title:{type:String,default:"车辆信息"},plateNo:{type:String,default:""},visible:{type:Boolean,default:!1},permission:{type:String,default:""}},data:function(){return{dialogVisible:!1,carInfo:{},carStatusList:[{label:"省内正常行驶",value:"1"},{label:"省外正常行驶",value:"2"},{label:"抵押",value:"3"},{label:"疑似抵押",value:"4"},{label:"疑似黑车",value:"5"},{label:"已入库",value:"6"},{label:"车在法院",value:"7"},{label:"已法拍",value:"8"},{label:"协商卖车",value:"9"}],gpsStatusList:[{label:"部分拆除",value:"1"},{label:"全部拆除",value:"2"},{label:"GPS正常",value:"3"},{label:"停车30天以上",value:"4"}]}},computed:{carStatusLabel:function(){var e=this,t=this.carStatusList.find((function(t){return t.value===e.carInfo.carStatus}));return t?t.label:"未知状态"},gpsStatusLabel:function(){var e=this,t=this.gpsStatusList.find((function(t){return t.value===e.carInfo.gpsStatus}));return t?t.label:"未知状态"}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,e&&this.plateNo&&Object(n["e"])(this.plateNo).then((function(e){t.carInfo=e.data||{}}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.carInfo={}}}},i=l,s=r("2877"),c=Object(s["a"])(i,a,o,!1,null,null,null);t["a"]=c.exports},1791:function(e,t,r){},"2eca":function(e,t,r){"use strict";var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{attrs:{title:e.title,customerInfo:e.customerInfo,visible:e.dialogVisible,width:"800px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[r("div",{staticClass:"descriptions"},[r("el-descriptions",{attrs:{column:3,size:"large",border:""}},[r("el-descriptions-item",{attrs:{span:"1",label:"姓名"}},[r("template",{slot:"label"},[e._v("姓名")]),e._v(" "+e._s(e.userInfo.customerName)+" ")],2),r("el-descriptions-item",{attrs:{span:"1",label:"电话"}},[r("template",{slot:"label"},[e._v("电话")]),e._v(" "+e._s(e.userInfo.mobilePhone)+" ")],2),r("el-descriptions-item",{attrs:{span:"1",label:"面签照片"}},[r("template",{slot:"label"},[e._v("面签照片")]),r("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"}},[e._v("点击查看")])],2),r("el-descriptions-item",{attrs:{span:"1",label:"身份证号"}},[r("template",{slot:"label"},[e._v("身份证号")]),e._v(" "+e._s(e.userInfo.certId)+" ")],2),r("el-descriptions-item",{attrs:{span:"3",label:"住址"}},[r("template",{slot:"label"},[e._v("住址")]),e._v(" "+e._s(e.userInfo.liveAddress)+e._s(e.userInfo.detailAddress)+" ")],2),r("el-descriptions-item",{attrs:{span:"3",label:"身份证地址"}},[r("template",{slot:"label"},[e._v("身份证地址")]),e._v(" "+e._s(e.userInfo.address)+" ")],2),r("el-descriptions-item",{attrs:{span:"3",label:"文书送达地址"}},[r("template",{slot:"label"},[e._v("文书送达地址")]),e._v(" "+e._s(e.userInfo.docAddress)+" ")],2)],1),r("div",{staticStyle:{"margin-top":"30px","font-size":"18px"}},[e._v("担保人信息")]),r("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.coborrowerList,size:"large",border:""}},[r("el-table-column",{attrs:{prop:"customerName",label:"担保人",width:"120"}}),r("el-table-column",{attrs:{prop:"mobileNo",label:"电话",width:"150"}}),r("el-table-column",{attrs:{prop:"address",label:"住址"}})],1)],1)])},o=[],n=r("bd52"),l={name:"userInfo",props:{title:{type:String,default:"用户信息"},visible:{type:Boolean,default:!1},customerInfo:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,userInfo:{},coborrowerList:[]}},watch:{visible:{handler:function(e){var t=this;this.dialogVisible=e,console.log(this.customerInfo),e&&this.customerInfo.customerId&&this.customerInfo.applyId&&Object(n["k"])(this.customerInfo.customerId,this.customerInfo.applyId).then((function(e){t.userInfo=e.data.customerInfo,t.coborrowerList=e.data.coborrowerList}))},immediate:!0}},methods:{close:function(){this.$emit("update:visible",!1),this.userInfo={},this.coborrowerList=[]}}},i=l,s=(r("d6fd"),r("2877")),c=Object(s["a"])(i,a,o,!1,null,"8a3d4978",null);t["a"]=c.exports},5029:function(e,t,r){"use strict";r.d(t,"d",(function(){return o})),r.d(t,"g",(function(){return n})),r.d(t,"c",(function(){return l})),r.d(t,"b",(function(){return i})),r.d(t,"a",(function(){return s})),r.d(t,"f",(function(){return c})),r.d(t,"e",(function(){return u}));var a=r("b775");function o(e){return Object(a["a"])({url:"/vm_car_order/vm_car_order/list",method:"get",params:e})}function n(){return Object(a["a"])({url:"/vm_car_order/vm_car_order/cate",method:"get"})}function l(e){return Object(a["a"])({url:"/vm_car_order/vm_car_order/"+e,method:"get"})}function i(e){return Object(a["a"])({url:"/vm_car_order/vm_car_order/"+e,method:"delete"})}function s(e){return Object(a["a"])({url:"/car_order/car_order",method:"put",data:e})}function c(e){return Object(a["a"])({url:"/vm_car_order/vm_car_order/submitCost",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/vm_car_order/vm_car_order/mailKey",method:"post",data:e})}},"9a7c":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"",prop:"customerName"}},[r("el-input",{attrs:{placeholder:"贷款人账户、姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerName,callback:function(t){e.$set(e.queryParams,"customerName",t)},expression:"queryParams.customerName"}})],1),r("el-form-item",{attrs:{label:"",prop:"certId"}},[r("el-input",{attrs:{placeholder:"贷款人身份证号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.certId,callback:function(t){e.$set(e.queryParams,"certId",t)},expression:"queryParams.certId"}})],1),r("el-form-item",{attrs:{label:"",prop:"plateNo"}},[r("el-input",{attrs:{placeholder:"请输入车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),r("el-form-item",{attrs:{label:"",prop:"nickName"}},[r("el-input",{attrs:{placeholder:"业务员",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.nickName,callback:function(t){e.$set(e.queryParams,"nickName",t)},expression:"queryParams.nickName"}})],1),r("el-form-item",{attrs:{label:"",prop:"jgName"}},[r("el-input",{attrs:{placeholder:"录单渠道名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jgName,callback:function(t){e.$set(e.queryParams,"jgName",t)},expression:"queryParams.jgName"}})],1),r("el-form-item",{attrs:{label:"",prop:"orgName"}},[r("el-select",{attrs:{placeholder:"放款银行",clearable:""},model:{value:e.queryParams.orgName,callback:function(t){e.$set(e.queryParams,"orgName",t)},expression:"queryParams.orgName"}},e._l(e.lendingBankList,(function(e){return r("el-option",{key:e.id,attrs:{label:e.orgName,value:e.orgName}})})),1)],1),r("el-form-item",{attrs:{label:"",prop:"followUp"}},[r("el-input",{attrs:{placeholder:"跟催员",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.followUp,callback:function(t){e.$set(e.queryParams,"followUp",t)},expression:"queryParams.followUp"}})],1),r("el-form-item",{attrs:{label:"",prop:"followUpType"}},[r("el-select",{attrs:{placeholder:"跟催类型",clearable:""},model:{value:e.queryParams.followUpType,callback:function(t){e.$set(e.queryParams,"followUpType",t)},expression:"queryParams.followUpType"}},e._l(e.followUpList,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"申请时间"}},[r("el-date-picker",{attrs:{type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.allocationTime,callback:function(t){e.$set(e.queryParams,"allocationTime",t)},expression:"queryParams.allocationTime"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.vw_account_loan_extensionList}},[r("el-table-column",{attrs:{label:"序号",align:"center",type:"index",width:"55",fixed:"left"}}),r("el-table-column",{attrs:{label:"逾期状态",align:"center",prop:"customerName"}}),r("el-table-column",{attrs:{label:"贷款人",align:"center",prop:"customerName"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{type:"text"},on:{click:function(r){return e.openUserInfo({customerId:t.row.customerId,applyId:t.row.applyId})}}},[e._v(" "+e._s(t.row.customerName)+" ")])]}}])}),r("el-table-column",{attrs:{label:"出单渠道",align:"center",prop:"jgName",width:"130"}}),r("el-table-column",{attrs:{label:"业务员",align:"center",prop:"nickName"}}),r("el-table-column",{attrs:{label:"车牌号",align:"center",prop:"plateNo",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{type:"text"},on:{click:function(r){return e.openCarInfo(t.row.plateNo)}}},[e._v(e._s(t.row.plateNo))])]}}])}),r("el-table-column",{attrs:{label:"放款银行",align:"center",prop:"orgName",width:"130"}}),r("el-table-column",{attrs:{label:"逾期天数",align:"center",prop:"overdueDays",width:"130"}}),r("el-table-column",{attrs:{label:"首期逾期金额",align:"center",prop:"foverdueAmount",width:"100"}}),r("el-table-column",{attrs:{label:"银行逾期金额",align:"center",prop:"boverdueAmount",width:"100"}}),r("el-table-column",{attrs:{label:"代扣逾期金额",align:"center",prop:"doverdueAmount"}}),r("el-table-column",{attrs:{label:"代扣结清金额",align:"center",prop:"drepaymentAmounts"}}),r("el-table-column",{attrs:{label:"银行结清金额",align:"center",prop:"brepaymentAmounts",width:"100"}}),r("el-table-column",{attrs:{label:"跟催类型",align:"center",prop:"loanReminder.urgeStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getUrgeStatusText(t.row.loanReminder&&t.row.loanReminder.urgeStatus))+" ")]}}])}),r("el-table-column",{attrs:{label:"催记日期",align:"center",prop:"loanReminder.createTime"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.parseTime(t.row.loanReminder&&t.row.loanReminder.createTime,"{y}-{m}-{d}"))+" ")]}}])}),r("el-table-column",{attrs:{label:"指派时间",align:"center",prop:"followTime",width:"130"}}),r("el-table-column",{attrs:{label:"延期时间",align:"center",prop:"createDate",width:"130"}}),r("el-table-column",{attrs:{label:"预扣款时间",align:"center",prop:"keyTime",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.parseTime(t.row.loanReminder&&t.row.loanReminder.appointedTime,"{y}-{m}-{d}"))+" ")]}}])}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"130",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["vm_car_order:vm_car_order:edit"],expression:"['vm_car_order:vm_car_order:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(r){return e.logView(t.row)}}},[e._v("催记查看")]),1==t.row.isExtension?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["loan_extension:loan_extension:approve"],expression:"['loan_extension:loan_extension:approve']"}],attrs:{size:"mini",type:"text"},on:{click:function(r){return e.handleExtension(t.row)}}},[e._v(" 审批 ")]):e._e(),2==t.row.isExtension||3==t.row.isExtension?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["loan_extension:loan_extension:approve"],expression:"['loan_extension:loan_extension:approve']"}],attrs:{size:"mini",type:"text"},on:{click:function(r){return e.handleExtension(t.row,!0)}}},[e._v(" 查看 ")]):e._e()]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"邮寄地址"}},[r("el-select",{attrs:{"value-key":"children",placeholder:"请选择省"},on:{change:e.provinceChange},model:{value:e.form.keyProvince,callback:function(t){e.$set(e.form,"keyProvince",t)},expression:"form.keyProvince"}},e._l(e.provinceList,(function(e){return r("el-option",{key:e.name,attrs:{label:e.name,value:e}})})),1),r("el-select",{staticStyle:{"margin-top":"10px"},attrs:{"value-key":"children",placeholder:"请选择市"},on:{change:e.cityChange},model:{value:e.form.keyCity,callback:function(t){e.$set(e.form,"keyCity",t)},expression:"form.keyCity"}},e._l(e.cityList,(function(e){return r("el-option",{key:e.name,attrs:{label:e.name,value:e}})})),1),r("el-select",{staticStyle:{"margin-top":"10px"},attrs:{"value-key":"children",placeholder:"请选择区"},on:{change:e.districtChange},model:{value:e.form.keyBorough,callback:function(t){e.$set(e.form,"keyBorough",t)},expression:"form.keyBorough"}},e._l(e.districtList,(function(e){return r("el-option",{key:e.name,attrs:{label:e.name,value:e}})})),1)],1),r("el-form-item",{attrs:{label:"详细地址"}},[r("el-input",{attrs:{placeholder:"请填写详细地址",clearable:""},model:{value:e.form.keyAddress,callback:function(t){e.$set(e.form,"keyAddress",t)},expression:"form.keyAddress"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),r("el-dialog",{attrs:{title:e.form._readonly?"详情":"延期审批",visible:e.openExtension,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.openExtension=t}}},[r("el-descriptions",{staticStyle:{"margin-bottom":"20px"},attrs:{column:2,border:""}},[r("el-descriptions-item",{attrs:{label:"贷款人"}},[r("el-button",{attrs:{type:"text"},on:{click:function(t){return e.openUserInfo({customerId:e.form.customerId,applyId:e.form.applyId})}}},[e._v(" "+e._s(e.form.customerName)+" ")])],1),r("el-descriptions-item",{attrs:{label:"车牌号"}},[r("el-button",{attrs:{type:"text"},on:{click:function(t){return e.openCarInfo(e.form.plateNo)}}},[e._v(e._s(e.form.plateNo))])],1),r("el-descriptions-item",{attrs:{label:"出单渠道"}},[e._v(e._s(e.form.jgName))]),r("el-descriptions-item",{attrs:{label:"放款银行"}},[e._v(e._s(e.form.orgName))]),r("el-descriptions-item",{attrs:{label:"逾期天数"}},[e._v(e._s(e.form.overdueDays))]),r("el-descriptions-item",{attrs:{label:"首期逾期金额"}},[e._v(e._s(e.form.foverdueAmount))]),r("el-descriptions-item",{attrs:{label:"银行逾期金额"}},[e._v(e._s(e.form.boverdueAmount))]),r("el-descriptions-item",{attrs:{label:"延期时间"}},[e._v(e._s(e.form.extensionDate))]),r("el-descriptions-item",{attrs:{label:"申请时间"}},[e._v(e._s(e.form.createDate))])],1),r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[e.form._readonly&&2==e.form.isExtension?[r("div",{staticStyle:{color:"#67c23a","font-size":"18px","margin-bottom":"16px"}},[e._v("已同意")])]:e.form._readonly&&3==e.form.isExtension?[r("div",{staticStyle:{color:"#f56c6c","font-size":"18px","margin-bottom":"16px"}},[e._v("已拒绝")])]:e._e(),e.form._readonly?e._e():[r("el-form-item",{attrs:{label:"审批"}},[r("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[r("el-radio",{attrs:{label:2}},[e._v("同意")]),r("el-radio",{attrs:{label:3}},[e._v("拒绝")])],1)],1),3==e.form.status?r("el-form-item",{attrs:{label:"拒绝原因"}},[r("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入拒绝原因"},model:{value:e.form.reason,callback:function(t){e.$set(e.form,"reason",t)},expression:"form.reason"}})],1):e._e()]],2),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e.form._readonly?e._e():r("el-button",{attrs:{type:"primary"},on:{click:e.submitExtension}},[e._v("提交")]),r("el-button",{on:{click:function(t){return e.cancel("extension")}}},[e._v(e._s(e.form._readonly?"关 闭":"取 消"))])],1)],1),r("userInfo",{ref:"userInfo",attrs:{visible:e.userInfoVisible,title:"贷款人信息",customerInfo:e.customerInfo},on:{"update:visible":function(t){e.userInfoVisible=t}}}),r("carInfo",{ref:"carInfo",attrs:{visible:e.carInfoVisible,title:"车辆信息",plateNo:e.plateNo,permission:"2"},on:{"update:visible":function(t){e.carInfoVisible=t}}}),r("loan-reminder-log",{ref:"loanReminderLog",attrs:{"loan-id":e.currentRow.loanId}})],1)},o=[],n=r("5530"),l=(r("b0c0"),r("a9e3"),r("5029")),i=r("bd52"),s=r("cf0d"),c=r("2eca"),u=r("0f5f"),m=r("7954"),d=r("a4ce"),p={name:"Vm_car_order",components:{userInfo:c["a"],carInfo:u["a"],LoanReminderLog:m["a"]},data:function(){return{loading:!0,showSearch:!0,total:0,vw_account_loan_extensionList:[],title:"",open:!1,openExtension:!1,queryParams:{pageSize:10,customerName:"",plateNo:"",jgName:"",garageId:"",lendingBank:"",followUpType:"",pageNum:1,cardID:"",clerk:"",followUp:"",allocationTime:""},jgNameList:[{label:"A公司",value:1},{label:"B公司",value:2}],lendingBankList:[],followUp:[{label:"无法跟进",value:1},{label:"约定还款",value:2},{label:"继续联系",value:3}],followUpList:[{label:"无法跟进",value:1},{label:"约定还款",value:2},{label:"继续联系",value:3}],form:{id:"",keyProvince:"",keyCity:"",keyBorough:"",keyAddress:"",extensionDate:"",status:"",reason:"",_readonly:!1,isExtension:""},rules:{keyProvince:"",keyCity:"",keyBorough:"",keyAddress:""},provinceList:s,cityList:[],districtList:[],revokeList:{id:"",status:4},customerInfo:{customerId:"",applyId:""},userInfoVisible:!1,plateNo:"",carInfoVisible:!1,currentRow:{}}},created:function(){this.getBank(),this.getTeam(),this.getList()},methods:{getBank:function(){var e=this;Object(d["e"])(this.queryParams).then((function(t){e.lendingBankList=t.data}))},getUrgeStatusText:function(e){switch(e){case 1:case"1":return"继续跟踪";case 2:case"2":return"约定还款";case 3:case"3":return"无法跟进";case 4:case"4":return"暂时无需跟进";default:return""}},handleChange:function(e){this.queryParams.jgName=e},provinceChange:function(e){this.form.keyProvince=e.name,this.cityList=e.children},cityChange:function(e){this.form.keyCity=e.name,this.districtList=e.children},districtChange:function(e){this.form.keyBorough=e.name},getTeam:function(){var e=this;Object(l["g"])().then((function(t){e.teamList=t.team,e.jgNameList=t.office}))},getList:function(){var e=this;this.loading=!0,Object(i["m"])(this.queryParams).then((function(t){e.vw_account_loan_extensionList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(e){"extension"==e?this.openExtension=!1:this.open=!1,this.reset()},reset:function(){this.form={id:"",keyProvince:"",keyCity:"",keyBorough:"",keyAddress:""},this.resetForm("form")},handleQuery:function(){this.queryParams.allocationTime&&(this.queryParams.startTime=this.queryParams.allocationTime[0],this.queryParams.endTime=this.queryParams.allocationTime[1]),this.queryParams.pageNum=1,console.log(this.queryParams),this.getList()},resetQuery:function(){this.restSearch(),this.handleQuery()},restSearch:function(){this.queryParams={pageSize:10,customerName:"",plateNo:"",cardID:"",jgName:"",garageId:"",lendingBank:"",followUpType:"",pageNum:1,clerk:"",followUp:"",allocationTime:""}},handleAdd:function(){this.reset(),this.open=!0,this.title="添加VIEW"},handleUpdate:function(e){var t=this;this.reset(),this.form.id=e.id,this.form.keyProvince=e.keyProvince,this.form.keyCity=e.keyCity,this.form.keyBorough=e.keyBorough,this.form.keyAddress=e.keyAddress;var r=e.id||this.ids;Object(l["c"])(r).then((function(e){t.open=!0,t.title="邮寄钥匙"}))},handleExtension:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.reset(),Object(i["r"])(Number(e.loanId)).then((function(a){var o=a&&a.data?a.data:{};t.form=Object(n["a"])({customerName:e.customerName||"",customerId:e.customerId||"",applyId:e.applyId||"",plateNo:e.plateNo||"",jgName:e.jgName||"",orgName:e.orgName||"",overdueDays:e.overdueDays||"",foverdueAmount:e.foverdueAmount||"",boverdueAmount:e.boverdueAmount||"",extensionDate:e.extensionDate||"",createDate:e.createDate||"",followUp:e.followUp||"",id:o.id||"",examineStatus:o.examineStatus||"",reason:o.reason||"",_readonly:!!r,isExtension:e.isExtension||""},o),t.openExtension=!0,t.title=r?"详情":"延期审批"})).catch((function(){t.$modal.msgError("未获取到延期详情数据")}))},submitExtension:function(){var e=this;2===this.form.status||3===this.form.status?Object(i["p"])(this.form).then((function(t){e.$modal.msgSuccess("提交成功"),e.openExtension=!1,e.getList()})):this.$modal.msgError("请选择审批结果")},submitForm:function(){var e=this;Object(l["a"])(this.form).then((function(t){e.$modal.msgSuccess("提交成功"),e.open=!1,e.getList()}))},handleRevoke:function(e){var t=this;console.log("1111"),this.revokeList.id=e.id;var r={id:e.id,status:4};this.$modal.confirm("是否确认撤销？").then((function(){return Object(l["a"])(r)})).then((function(){t.getList(),t.$modal.msgSuccess("撤销成功")})).catch((function(e){console.log(e)}))},handleDelete:function(e){var t=this,r=e.id||this.ids;this.$modal.confirm('是否确认撤销编号为"'+r+'"的数据项？').then((function(){return Object(l["b"])(r)})).then((function(){t.getList(),t.$modal.msgSuccess("撤销成功")})).catch((function(){}))},handleExport:function(){this.download("vm_car_order/vm_car_order/export",Object(n["a"])({},this.queryParams),"vm_car_order_".concat((new Date).getTime(),".xlsx"))},openUserInfo:function(e){this.customerInfo=e,this.userInfoVisible=!0},openCarInfo:function(e){this.plateNo=e,this.carInfoVisible=!0},logView:function(e){this.currentRow=e,this.$refs.loanReminderLog.openLogDialog()}}},f=p,b=(r("0880"),r("2877")),v=Object(b["a"])(f,a,o,!1,null,null,null);t["default"]=v.exports},a4ce:function(e,t,r){"use strict";r.d(t,"d",(function(){return o})),r.d(t,"c",(function(){return n})),r.d(t,"a",(function(){return l})),r.d(t,"f",(function(){return i})),r.d(t,"b",(function(){return s})),r.d(t,"e",(function(){return c}));var a=r("b775");function o(e){return Object(a["a"])({url:"/partner_info/partner_info/list",method:"get",params:e})}function n(e){return Object(a["a"])({url:"/partner_info/partner_info/"+e,method:"get"})}function l(e){return Object(a["a"])({url:"/partner_info/partner_info",method:"post",data:e})}function i(e){return Object(a["a"])({url:"/partner_info/partner_info",method:"put",data:e})}function s(e){return Object(a["a"])({url:"/partner_info/partner_info/"+e,method:"delete"})}function c(e){return Object(a["a"])({url:"/partner_info/partner_info/id-name-list",method:"get",params:e})}},d6fd:function(e,t,r){"use strict";r("1791")},d864:function(e,t,r){}}]);