(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d22b9de"],{f075:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"申请编号",prop:"applyNo"}},[a("el-input",{attrs:{placeholder:"请输入申请编号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.applyNo,callback:function(t){e.$set(e.queryParams,"applyNo",t)},expression:"queryParams.applyNo"}})],1),a("el-form-item",{attrs:{label:"客户编号",prop:"customerId"}},[a("el-input",{attrs:{placeholder:"请输入客户编号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.customerId,callback:function(t){e.$set(e.queryParams,"customerId",t)},expression:"queryParams.customerId"}})],1),a("el-form-item",{attrs:{label:"车架号",prop:"identityNo"}},[a("el-input",{attrs:{placeholder:"请输入车架号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.identityNo,callback:function(t){e.$set(e.queryParams,"identityNo",t)},expression:"queryParams.identityNo"}})],1),a("el-form-item",{attrs:{label:"车牌号",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"请输入车牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.plateNo,callback:function(t){e.$set(e.queryParams,"plateNo",t)},expression:"queryParams.plateNo"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["card:card:edit"],expression:"['card:card:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["card:card:remove"],expression:"['card:card:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["card:card:export"],expression:"['card:card:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.cardList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"主键",align:"center",prop:"id"}}),a("el-table-column",{attrs:{label:"申请编号",align:"center",prop:"applyNo"}}),a("el-table-column",{attrs:{label:"客户编号",align:"center",prop:"customerId"}}),a("el-table-column",{attrs:{label:"车架号",align:"center",prop:"identityNo"}}),a("el-table-column",{attrs:{label:"品牌型号",align:"center",prop:"shape"}}),a("el-table-column",{attrs:{label:"车牌号",align:"center",prop:"plateNo"}}),a("el-table-column",{attrs:{label:"车辆类型",align:"center",prop:"carType"}}),a("el-table-column",{attrs:{label:"车辆所有人",align:"center",prop:"carOwner"}}),a("el-table-column",{attrs:{label:"住址",align:"center",prop:"address"}}),a("el-table-column",{attrs:{label:"使用性质",align:"center",prop:"useNature"}}),a("el-table-column",{attrs:{label:"发动机号",align:"center",prop:"engineNumber"}}),a("el-table-column",{attrs:{label:"注册日期",align:"center",prop:"registerDate"}}),a("el-table-column",{attrs:{label:"发证日期",align:"center",prop:"issueDate"}}),a("el-table-column",{attrs:{label:"登记机关",align:"center",prop:"registerOrg"}}),a("el-table-column",{attrs:{label:"创建日期",align:"center",prop:"createDate",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"修改日期",align:"center",prop:"updateDate",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.updateDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["card:card:edit"],expression:"['card:card:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["card:card:remove"],expression:"['card:card:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"申请编号",prop:"applyNo"}},[a("el-input",{attrs:{placeholder:"请输入申请编号"},model:{value:e.form.applyNo,callback:function(t){e.$set(e.form,"applyNo",t)},expression:"form.applyNo"}})],1),a("el-form-item",{attrs:{label:"客户编号",prop:"customerId"}},[a("el-input",{attrs:{placeholder:"请输入客户编号"},model:{value:e.form.customerId,callback:function(t){e.$set(e.form,"customerId",t)},expression:"form.customerId"}})],1),a("el-form-item",{attrs:{label:"车架号",prop:"identityNo"}},[a("el-input",{attrs:{placeholder:"请输入车架号"},model:{value:e.form.identityNo,callback:function(t){e.$set(e.form,"identityNo",t)},expression:"form.identityNo"}})],1),a("el-form-item",{attrs:{label:"品牌型号",prop:"shape"}},[a("el-input",{attrs:{placeholder:"请输入品牌型号"},model:{value:e.form.shape,callback:function(t){e.$set(e.form,"shape",t)},expression:"form.shape"}})],1),a("el-form-item",{attrs:{label:"车牌号",prop:"plateNo"}},[a("el-input",{attrs:{placeholder:"请输入车牌号"},model:{value:e.form.plateNo,callback:function(t){e.$set(e.form,"plateNo",t)},expression:"form.plateNo"}})],1),a("el-form-item",{attrs:{label:"车辆类型",prop:"carType"}},[a("el-input",{attrs:{placeholder:"请输入车辆类型"},model:{value:e.form.carType,callback:function(t){e.$set(e.form,"carType",t)},expression:"form.carType"}})],1),a("el-form-item",{attrs:{label:"车辆所有人",prop:"carOwner"}},[a("el-input",{attrs:{placeholder:"请输入车辆所有人"},model:{value:e.form.carOwner,callback:function(t){e.$set(e.form,"carOwner",t)},expression:"form.carOwner"}})],1),a("el-form-item",{attrs:{label:"住址",prop:"address"}},[a("el-input",{attrs:{placeholder:"请输入住址"},model:{value:e.form.address,callback:function(t){e.$set(e.form,"address",t)},expression:"form.address"}})],1),a("el-form-item",{attrs:{label:"使用性质",prop:"useNature"}},[a("el-input",{attrs:{placeholder:"请输入使用性质"},model:{value:e.form.useNature,callback:function(t){e.$set(e.form,"useNature",t)},expression:"form.useNature"}})],1),a("el-form-item",{attrs:{label:"发动机号",prop:"engineNumber"}},[a("el-input",{attrs:{placeholder:"请输入发动机号"},model:{value:e.form.engineNumber,callback:function(t){e.$set(e.form,"engineNumber",t)},expression:"form.engineNumber"}})],1),a("el-form-item",{attrs:{label:"注册日期",prop:"registerDate"}},[a("el-input",{attrs:{placeholder:"请输入注册日期"},model:{value:e.form.registerDate,callback:function(t){e.$set(e.form,"registerDate",t)},expression:"form.registerDate"}})],1),a("el-form-item",{attrs:{label:"发证日期",prop:"issueDate"}},[a("el-input",{attrs:{placeholder:"请输入发证日期"},model:{value:e.form.issueDate,callback:function(t){e.$set(e.form,"issueDate",t)},expression:"form.issueDate"}})],1),a("el-form-item",{attrs:{label:"登记机关",prop:"registerOrg"}},[a("el-input",{attrs:{placeholder:"请输入登记机关"},model:{value:e.form.registerOrg,callback:function(t){e.$set(e.form,"registerOrg",t)},expression:"form.registerOrg"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],n=a("5530"),o=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("b775"));function i(e){return Object(o["a"])({url:"/card/card/list",method:"get",params:e})}function s(e){return Object(o["a"])({url:"/card/card/"+e,method:"get"})}function c(e){return Object(o["a"])({url:"/card/card",method:"post",data:e})}function u(e){return Object(o["a"])({url:"/card/card",method:"put",data:e})}function p(e){return Object(o["a"])({url:"/card/card/"+e,method:"delete"})}var m={name:"Card",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,cardList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,applyNo:null,customerId:null,identityNo:null,plateNo:null},form:{},rules:{}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,i(this.queryParams).then((function(t){e.cardList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,applyNo:null,customerId:null,identityNo:null,shape:null,plateNo:null,carType:null,carOwner:null,address:null,useNature:null,engineNumber:null,registerDate:null,issueDate:null,registerOrg:null,createBy:null,createDate:null,updateBy:null,updateDate:null,delFlag:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加行驶证"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;s(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改行驶证"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?u(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):c(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除行驶证编号为"'+a+'"的数据项？').then((function(){return p(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("card/card/export",Object(n["a"])({},this.queryParams),"card_".concat((new Date).getTime(),".xlsx"))}}},d=m,f=a("2877"),h=Object(f["a"])(d,r,l,!1,null,null,null);t["default"]=h.exports}}]);