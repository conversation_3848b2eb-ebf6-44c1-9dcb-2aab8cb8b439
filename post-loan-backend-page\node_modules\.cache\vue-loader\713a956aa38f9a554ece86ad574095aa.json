{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\layout\\components\\Dialog\\litigationStatus.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\layout\\components\\Dialog\\litigationStatus.vue", "mtime": 1754364092731}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["litigationStatus.vue"], "names": [], "mappings": ";;;;;;;;;;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "litigationStatus.vue", "sourceRoot": "src/layout/components/Dialog", "sourcesContent": ["<template>\r\n  <el-cascader\r\n    v-model=\"proxyValue\"\r\n    :options=\"litigationStatusTree\"\r\n    :props=\"cascaderProps\"\r\n    :placeholder=\"placeholder\"\r\n    clearable\r\n    @change=\"handleChange\" />\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'LitigationStatusCascader',\r\n  props: {\r\n    value: {\r\n      type: [String, Array],\r\n      default: '',\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: '法诉状态',\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      litigationStatusTree: [\r\n        // 独立状态（不需要分类）\r\n        { label: '暂不起诉', value: '暂不起诉' },\r\n        { label: '撤案', value: '撤案' },\r\n        {\r\n          label: '立案前',\r\n          value: '立案前',\r\n          children: [\r\n            { label: '准备资料', value: '准备资料' },\r\n            { label: '已邮寄', value: '已邮寄' },\r\n            { label: '待立案', value: '待立案' },\r\n          ],\r\n        },\r\n        {\r\n          label: '立案-判决',\r\n          value: '立案-判决',\r\n          children: [\r\n            { label: '获取案件号', value: '获取案件号' },\r\n            { label: '待出民初号', value: '待出民初号' },\r\n            { label: '待开庭', value: '待开庭' },\r\n            { label: '待出法院文书', value: '待出法院文书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '判决-执行',\r\n          value: '判决-执行',\r\n          children: [\r\n            { label: '待执行', value: '待执行' },\r\n            { label: '待出申请书', value: '待出申请书' },\r\n            { label: '已提交执行书', value: '已提交执行书' },\r\n          ],\r\n        },\r\n        {\r\n          label: '执行后',\r\n          value: '执行后',\r\n          children: [\r\n            { label: '执行中', value: '执行中' },\r\n            { label: '执行终本', value: '执行终本' },\r\n            { label: '继续执行', value: '继续执行' },\r\n            { label: '待送车', value: '待送车' },\r\n            { label: '待法拍', value: '待法拍' },\r\n          ],\r\n        },\r\n        {\r\n          label: '结案',\r\n          value: '结案',\r\n          children: [\r\n            { label: '法诉减免结清', value: '法诉减免结清' },\r\n            { label: '法诉全额结清', value: '法诉全额结清' },\r\n          ],\r\n        },\r\n      ],\r\n      cascaderProps: {\r\n        emitPath: false,\r\n        value: 'value',\r\n        label: 'label',\r\n        children: 'children',\r\n        // 只允许选择子节点（有children的父节点不可选）\r\n        disabled: node => !!node.children && node.children.length > 0,\r\n      },\r\n    }\r\n  },\r\n  computed: {\r\n    proxyValue: {\r\n      get() {\r\n        return this.value\r\n      },\r\n      set(val) {\r\n        this.$emit('input', val)\r\n        this.$emit('update:value', val)\r\n      },\r\n    },\r\n  },\r\n  methods: {\r\n    handleChange(val) {\r\n      if (this.$listeners && this.$listeners.change) {\r\n        this.$emit('change', val)\r\n      }\r\n    },\r\n  },\r\n}\r\n</script>\r\n"]}]}